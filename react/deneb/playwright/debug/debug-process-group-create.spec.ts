import { test, expect } from '@playwright/test';
import { SimpleEnvironmentFacade } from '../shared/facades/SimpleEnvironmentFacade';
import { CreateProcessGroupFacade } from '../shared/facades/CreateProcessGroupFacade';
import { LoginPage } from '../shared/page-object-models/LoginPage';

test.describe('Debug Process Group Create', () => {
  test('Test CreateProcessGroupFacade with simplified environment', async ({ browser }) => {
    // Setup simplified environment
    const simpleEnvF = new SimpleEnvironmentFacade(browser);
    const { email, password } = await simpleEnvF.execute();

    console.log(`Environment setup complete. Using credentials: ${email}`);

    // Create a new page for process group creation
    const page = await browser.newPage();

    try {
      // Login again on the new page
      const login = new LoginPage(page);
      await login.goto();
      await login.loginWithEmail(email, password);

      // Wait for login
      await page.waitForTimeout(3000);

      // Test CreateProcessGroupFacade
      const createProcessGroupF = new CreateProcessGroupFacade(page);

      await createProcessGroupF.execute({
        name: `Test Process Group ${Date.now()}`,
        managementCode: `CODE${Date.now()}`,
      });

      console.log('Process group creation test completed successfully!');
    } finally {
      await page.close();
    }
  });
});
