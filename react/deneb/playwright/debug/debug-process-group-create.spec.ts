import { test, expect } from '@playwright/test';
import { LoginPage } from '../shared/page-object-models/LoginPage';

test.describe('Debug Process Group Create Page', () => {
  test('Debug with admin credentials', async ({ page }) => {
    // Try login with admin credentials from env
    const loginPage = new LoginPage(page);
    await loginPage.goto();

    // Take screenshot of login page
    await page.screenshot({ path: 'debug-login-page.png', fullPage: true });
    console.log('Login page loaded');

    // Login with admin credentials from playwright.env
    await loginPage.loginWithEmail('<EMAIL>', '0000');

    // Wait for login to complete
    await page.waitForTimeout(5000);

    // Check current URL after login
    const urlAfterLogin = page.url();
    console.log(`URL after login: ${urlAfterLogin}`);

    // Take screenshot after login attempt
    await page.screenshot({ path: 'debug-after-login-attempt.png', fullPage: true });

    // If still on login page, check for error messages
    if (urlAfterLogin.includes('login')) {
      const errorMessages = await page
        .locator('.error, .alert, [class*="error"], [class*="alert"]')
        .allTextContents();
      console.log(`Error messages: ${errorMessages.join(', ')}`);

      // Try to see what's actually on the page
      const pageContent = await page.content();
      console.log('Page content length:', pageContent.length);

      // Look for any form validation messages
      const allText = await page.locator('body').textContent();
      console.log('Page text (first 500 chars):', allText?.substring(0, 500));

      return; // Exit early if login failed
    }

    // Navigate to the target page regardless
    await page.goto('https://app.local.crowdlog.jp/pages/mastersettings/processes/');
    await page.waitForTimeout(3000);

    // Take a screenshot to see what's on the page
    await page.screenshot({ path: 'debug-final-page.png', fullPage: true });

    // Get the page title
    const title = await page.title();
    console.log(`Page title: ${title}`);

    // Get the current URL
    const url = page.url();
    console.log(`Current URL: ${url}`);

    // Debug: Check what tabs are available
    console.log('=== AVAILABLE TABS ===');
    const tabs = await page.locator('[role="tab"]').all();
    console.log(`Found ${tabs.length} tabs:`);
    for (let i = 0; i < tabs.length; i++) {
      const tab = tabs[i];
      const text = await tab.textContent();
      console.log(`Tab ${i}: "${text}"`);
    }

    // Try to find Process Group tab with different approaches
    const processGroupTab = page.getByRole('tab').filter({ hasText: 'Process Group' });
    const processGroupTabExists = (await processGroupTab.count()) > 0;
    console.log(`Process Group tab exists: ${processGroupTabExists}`);

    if (processGroupTabExists) {
      await processGroupTab.click();
      await page.waitForTimeout(2000);
    } else {
      console.log('Process Group tab not found, checking current page content...');
    }

    // Try to find and click Create button
    console.log('=== LOOKING FOR CREATE BUTTON ===');
    const createButtons = await page.locator('a, button').filter({ hasText: 'Create' }).all();
    console.log(`Found ${createButtons.length} Create buttons/links`);

    if (createButtons.length > 0) {
      await createButtons[0].click();
      await page.waitForTimeout(2000);
    } else {
      console.log('No Create button found, skipping form debug');
      return;
    }

    // Wait for create page to load
    await page.waitForTimeout(2000);

    // Debug: Print page HTML structure
    console.log('=== PAGE HTML ===');
    const html = await page.content();
    console.log(html);

    // Debug: Check for form elements
    console.log('=== FORM ELEMENTS ===');

    // Check for input fields
    const inputs = await page.locator('input').all();
    console.log(`Found ${inputs.length} input elements:`);
    for (let i = 0; i < inputs.length; i++) {
      const input = inputs[i];
      const name = await input.getAttribute('name');
      const placeholder = await input.getAttribute('placeholder');
      const type = await input.getAttribute('type');
      console.log(`Input ${i}: name="${name}", placeholder="${placeholder}", type="${type}"`);
    }

    // Check for select elements
    const selects = await page.locator('select').all();
    console.log(`Found ${selects.length} select elements:`);
    for (let i = 0; i < selects.length; i++) {
      const select = selects[i];
      const name = await select.getAttribute('name');
      console.log(`Select ${i}: name="${name}"`);
    }

    // Check for buttons
    const buttons = await page.locator('button').all();
    console.log(`Found ${buttons.length} button elements:`);
    for (let i = 0; i < buttons.length; i++) {
      const button = buttons[i];
      const text = await button.textContent();
      const type = await button.getAttribute('type');
      console.log(`Button ${i}: text="${text}", type="${type}"`);
    }

    // Check for React Select components
    const reactSelects = await page.locator('[class*="react-select"]').all();
    console.log(`Found ${reactSelects.length} React Select components`);

    // Check for any div with select-like behavior
    const selectDivs = await page.locator('div[role="combobox"], div[role="listbox"]').all();
    console.log(`Found ${selectDivs.length} select-like div elements`);

    // Take a screenshot for visual debugging
    await page.screenshot({ path: 'debug-process-group-create.png', fullPage: true });

    console.log('=== DEBUG COMPLETE ===');
  });
});
