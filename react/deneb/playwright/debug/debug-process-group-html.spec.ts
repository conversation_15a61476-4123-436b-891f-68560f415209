import { test } from '@playwright/test';

import { SetupEnvironmentFacade } from '@/../playwright/shared/facades/SetupEnvironmentFacade';
import { CreateProcessGroupFacade } from '@/../playwright/shared/facades/CreateProcessGroupFacade';
import { CreateProcessFacade } from '@/../playwright/shared/facades/CreateProcessFacade';
import { ProcessGroupMasterPage } from '@/../playwright/shared/page-object-models/ProcessGroupMasterPage';
import * as utils from '@/../playwright/shared/utils';

test.describe('Debug Process Group HTML Structure', () => {
  test('Inspect HTML structure of Process Group list page', async ({ browser }) => {
    test.setTimeout(300000); // 5 minutes

    // Setup environment
    const setupEnvironmentF = new SetupEnvironmentFacade(browser);
    const result = await setupEnvironmentF.execute();
    const page = result.page;

    try {
      // First, create a test process
      const createProcessF = new CreateProcessFacade(page);
      const uniqueId = Date.now().toString();
      const testProcessName = `HTMLDebugProcessTest_${uniqueId}`;

      console.log('Creating test process...');
      await createProcessF.execute({
        name: testProcessName,
        category: `HTMLDebugProcessCategory_${uniqueId}`,
        active: true,
      });
      console.log('Test process created successfully.');

      // Create a test process group
      const createProcessGroupF = new CreateProcessGroupFacade(page);
      const testProcessGroupName = `HTMLDebugGroupTest_${uniqueId}`;

      console.log('Creating test process group...');
      await createProcessGroupF.execute({
        name: testProcessGroupName,
        managementCode: `HDGT_${uniqueId}`,
        active: true,
      });
      console.log('Test process group created successfully.');

      // Navigate to Process Group list page
      const processGroupMasterPage = new ProcessGroupMasterPage(page);
      console.log('Navigating to Process Group list page...');
      await processGroupMasterPage.goto();
      await utils.sleep(5000); // Wait longer for data to load

      // Get the full HTML content of the page
      console.log('=== FULL PAGE HTML ===');
      const fullHTML = await page.content();
      console.log('Page title:', await page.title());
      console.log('Page URL:', page.url());
      
      // Look for table-related elements
      console.log('\n=== TABLE ELEMENTS ===');
      const tables = await page.locator('table').count();
      console.log(`Number of <table> elements: ${tables}`);
      
      if (tables > 0) {
        for (let i = 0; i < tables; i++) {
          const tableHTML = await page.locator('table').nth(i).innerHTML();
          console.log(`Table ${i + 1} HTML:`, tableHTML.substring(0, 500) + '...');
        }
      }

      // Look for tbody elements
      const tbodies = await page.locator('tbody').count();
      console.log(`Number of <tbody> elements: ${tbodies}`);
      
      if (tbodies > 0) {
        for (let i = 0; i < tbodies; i++) {
          const tbodyHTML = await page.locator('tbody').nth(i).innerHTML();
          console.log(`Tbody ${i + 1} HTML:`, tbodyHTML);
        }
      }

      // Look for tr elements
      const rows = await page.locator('tr').count();
      console.log(`Number of <tr> elements: ${rows}`);
      
      if (rows > 0) {
        for (let i = 0; i < Math.min(rows, 10); i++) {
          const rowHTML = await page.locator('tr').nth(i).innerHTML();
          const rowText = await page.locator('tr').nth(i).textContent();
          console.log(`Row ${i + 1} HTML:`, rowHTML);
          console.log(`Row ${i + 1} Text:`, rowText);
        }
      }

      // Look for any elements containing our test data
      console.log('\n=== SEARCHING FOR TEST DATA ===');
      const testNameElements = await page.locator(`text=${testProcessGroupName}`).count();
      console.log(`Elements containing test process group name: ${testNameElements}`);
      
      const testCodeElements = await page.locator(`text=HDGT_${uniqueId}`).count();
      console.log(`Elements containing test management code: ${testCodeElements}`);

      // Look for common data container elements
      console.log('\n=== DATA CONTAINERS ===');
      const divs = await page.locator('div').count();
      console.log(`Number of <div> elements: ${divs}`);
      
      // Look for elements with data-related attributes
      const dataElements = await page.locator('[data-testid], [data-cy], [role="row"], [role="cell"]').count();
      console.log(`Elements with data attributes: ${dataElements}`);

      // Check if there are any loading indicators
      console.log('\n=== LOADING INDICATORS ===');
      const loadingElements = await page.locator('.loading, .spinner, [data-testid*="loading"]').count();
      console.log(`Loading indicators found: ${loadingElements}`);

      // Check for empty state messages
      console.log('\n=== EMPTY STATE ===');
      const emptyMessages = await page.locator('text=/no.*data|empty|nothing.*found/i').count();
      console.log(`Empty state messages found: ${emptyMessages}`);

      // Save the full HTML to a file for inspection
      const fs = require('fs');
      fs.writeFileSync('debug-process-group-page.html', fullHTML);
      console.log('Full HTML saved to debug-process-group-page.html');

      // Take a screenshot
      await page.screenshot({ path: 'debug-process-group-html.png', fullPage: true });
      console.log('Screenshot saved as debug-process-group-html.png');

    } catch (error: any) {
      console.error(`Error in HTML debug test: ${error.message}`);
      await page.screenshot({ path: 'debug-html-error.png', fullPage: true });
      throw error;
    }
  });
});
