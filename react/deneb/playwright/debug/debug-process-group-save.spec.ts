import { test } from '@playwright/test';

import { SetupEnvironmentFacade } from '@/../playwright/shared/facades/SetupEnvironmentFacade';
import { CreateProcessFacade } from '@/../playwright/shared/facades/CreateProcessFacade';
import * as utils from '@/../playwright/shared/utils';

test.describe('Debug Process Group Save', () => {
  test('Monitor Process Group creation network requests', async ({ browser }) => {
    test.setTimeout(300000); // 5 minutes

    // Setup environment
    const setupEnvironmentF = new SetupEnvironmentFacade(browser);
    const result = await setupEnvironmentF.execute();
    const page = result.page;

    // Track network requests specifically for process groups
    const processGroupRequests: any[] = [];
    const processGroupResponses: any[] = [];

    page.on('request', (request) => {
      if (request.url().includes('process_group') || request.url().includes('process-group')) {
        processGroupRequests.push({
          url: request.url(),
          method: request.method(),
          headers: request.headers(),
          postData: request.postData(),
          timestamp: new Date().toISOString(),
        });
        console.log(`🔵 PROCESS GROUP REQUEST: ${request.method()} ${request.url()}`);
        if (request.postData()) {
          console.log(`   📤 POST Data: ${request.postData()}`);
        }
      }
    });

    page.on('response', (response) => {
      if (response.url().includes('process_group') || response.url().includes('process-group')) {
        processGroupResponses.push({
          url: response.url(),
          status: response.status(),
          statusText: response.statusText(),
          timestamp: new Date().toISOString(),
        });
        console.log(`🟢 PROCESS GROUP RESPONSE: ${response.status()} ${response.url()}`);
      }
    });

    try {
      // First, create a test process
      const createProcessF = new CreateProcessFacade(page);
      const uniqueId = Date.now().toString();
      const testProcessName = `SaveDebugProcessTest_${uniqueId}`;

      console.log('=== CREATING TEST PROCESS ===');
      await createProcessF.execute({
        name: testProcessName,
        category: `SaveDebugProcessCategory_${uniqueId}`,
        active: true,
      });
      console.log('Test process created successfully.');

      // Navigate to Process Group creation page
      console.log('=== NAVIGATING TO PROCESS GROUP CREATION PAGE ===');
      await page.goto('https://app.local.crowdlog.jp/pages/mastersettings/processes/group/new/');
      await utils.sleep(3000);

      // Fill out the form manually with detailed monitoring
      const testProcessGroupName = `SaveDebugGroupTest_${uniqueId}`;
      const testManagementCode = `SDGT_${uniqueId}`;

      console.log('=== FILLING PROCESS GROUP FORM ===');

      // Fill Process Group Name
      console.log('Filling Process Group Name...');
      const nameInput = page.locator('input[name="name"]');
      await nameInput.fill(testProcessGroupName);
      console.log(`Filled Process Group Name: ${testProcessGroupName}`);

      // Fill Management Code (try different field names)
      console.log('Filling Management Code...');
      const codeInput = page
        .locator('input[name="code"]')
        .or(page.locator('input[name="management_code"]'))
        .or(page.locator('input').filter({ hasText: 'Management Code' }));

      if ((await codeInput.count()) > 0) {
        await codeInput.fill(testManagementCode);
        console.log(`Filled Management Code: ${testManagementCode}`);
      } else {
        console.log('Management Code input not found, skipping...');
      }

      // Fill Display Order
      console.log('Filling Display Order...');
      const orderInput = page.locator('input[name="order"]');
      await orderInput.fill('1');
      console.log('Filled Display Order: 1');

      // Wait a moment for form validation
      await utils.sleep(1000);

      console.log('=== ATTEMPTING TO SAVE PROCESS GROUP ===');
      console.log(`Process Group requests before save: ${processGroupRequests.length}`);
      console.log(`Process Group responses before save: ${processGroupResponses.length}`);

      // Find and click the save button
      const saveButton = page
        .getByRole('button', { name: 'Save' })
        .or(page.getByRole('button', { name: '保存' }))
        .or(page.locator('button[type="submit"]'));

      if ((await saveButton.count()) === 0) {
        console.log('❌ Save button not found!');
        await page.screenshot({ path: 'debug-no-save-button.png', fullPage: true });
        throw new Error('Save button not found');
      }

      console.log('Save button found, clicking...');
      await saveButton.click();
      console.log('Save button clicked');

      // Wait for potential network requests
      console.log('Waiting for network requests...');
      await utils.sleep(5000);

      console.log('=== POST-SAVE ANALYSIS ===');
      console.log(`Process Group requests after save: ${processGroupRequests.length}`);
      console.log(`Process Group responses after save: ${processGroupResponses.length}`);

      if (processGroupRequests.length > 0) {
        console.log('✅ Process Group requests detected:');
        processGroupRequests.forEach((req, index) => {
          console.log(`${index + 1}. ${req.method} ${req.url} at ${req.timestamp}`);
          if (req.postData) {
            console.log(`   Data: ${req.postData}`);
          }
        });
      } else {
        console.log('❌ No Process Group requests detected!');
      }

      if (processGroupResponses.length > 0) {
        console.log('✅ Process Group responses detected:');
        processGroupResponses.forEach((res, index) => {
          console.log(
            `${index + 1}. ${res.status} ${res.statusText} - ${res.url} at ${res.timestamp}`,
          );
        });
      } else {
        console.log('❌ No Process Group responses detected!');
      }

      // Check current URL
      const currentUrl = page.url();
      console.log(`Current URL after save: ${currentUrl}`);

      // Check for any error messages or success indicators
      const errorMessages = await page.locator('.error, .alert-danger, .text-danger').count();
      const successMessages = await page.locator('.success, .alert-success, .text-success').count();
      console.log(`Error messages: ${errorMessages}`);
      console.log(`Success messages: ${successMessages}`);

      // Check if we're still on the creation page or redirected
      if (currentUrl.includes('/new/')) {
        console.log('⚠️  Still on creation page - save might have failed');
      } else {
        console.log('✅ Redirected away from creation page - save might have succeeded');
      }

      // Take a screenshot for analysis
      await page.screenshot({ path: 'debug-process-group-save.png', fullPage: true });
      console.log('Screenshot saved as debug-process-group-save.png');
    } catch (error: any) {
      console.error(`Error in save debug test: ${error.message}`);
      await page.screenshot({ path: 'debug-save-error.png', fullPage: true });
      throw error;
    }
  });
});
