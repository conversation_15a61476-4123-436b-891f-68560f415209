import { expect, test } from '@playwright/test';

import { SetupEnvironmentFacade } from '@/../playwright/shared/facades/SetupEnvironmentFacade';
import { CreateProcessGroupFacade } from '@/../playwright/shared/facades/CreateProcessGroupFacade';
import { CreateProcessFacade } from '@/../playwright/shared/facades/CreateProcessFacade';
import { ProcessGroupMasterPage } from '@/../playwright/shared/page-object-models/ProcessGroupMasterPage';
import * as utils from '@/../playwright/shared/utils';

test.describe('Debug Process Group Persistence', () => {
  test('Create and verify process group persistence', async ({ browser }) => {
    test.setTimeout(300000); // 5 minutes

    // Setup environment
    const setupEnvironmentF = new SetupEnvironmentFacade(browser);
    const result = await setupEnvironmentF.execute();
    const page = result.page;

    try {
      // First, create a test process
      const createProcessF = new CreateProcessFacade(page);
      const uniqueId = Date.now().toString();
      const testProcessName = `DebugProcessTest_${uniqueId}`;

      console.log('Creating test process...');
      await createProcessF.execute({
        name: testProcessName,
        category: `DebugProcessCategory_${uniqueId}`,
        active: true,
      });
      console.log('Test process created successfully.');

      // Create a test process group
      const createProcessGroupF = new CreateProcessGroupFacade(page);
      const testProcessGroupName = `DebugGroupTest_${uniqueId}`;

      console.log('Creating test process group...');
      await createProcessGroupF.execute({
        name: testProcessGroupName,
        managementCode: `DGT_${uniqueId}`,
        active: true,
      });
      console.log('Test process group created successfully.');

      // Navigate to Process Group list page
      const processGroupMasterPage = new ProcessGroupMasterPage(page);
      console.log('Navigating to Process Group list page...');
      await processGroupMasterPage.goto();
      await utils.sleep(3000);

      // Check if we can find any process groups
      console.log('Checking for process groups in the list...');
      const allRows = await processGroupMasterPage.getProcessGroupRows();
      const totalRowCount = await allRows.count();
      console.log(`Total process groups found: ${totalRowCount}`);

      if (totalRowCount > 0) {
        // Get the text content of all rows to see what's there
        for (let i = 0; i < Math.min(totalRowCount, 5); i++) {
          const rowText = await allRows.nth(i).textContent();
          console.log(`Row ${i + 1}: ${rowText}`);
        }

        // Search for our specific test process group
        console.log(`Searching for test process group: ${testProcessGroupName}`);
        const testProcessGroupRow = allRows.filter({ hasText: testProcessGroupName });
        const testRowCount = await testProcessGroupRow.count();
        console.log(`Test process group found: ${testRowCount > 0 ? 'YES' : 'NO'}`);

        if (testRowCount > 0) {
          console.log('✅ SUCCESS: Process group persistence is working!');
          expect(testRowCount).toBe(1);
        } else {
          console.log('❌ ISSUE: Test process group not found in list');
          // Let's try searching by management code instead
          const testProcessGroupByCode = allRows.filter({ hasText: `DGT_${uniqueId}` });
          const testByCodeCount = await testProcessGroupByCode.count();
          console.log(`Test process group found by management code: ${testByCodeCount > 0 ? 'YES' : 'NO'}`);
        }
      } else {
        console.log('❌ ISSUE: No process groups found in the list at all');
        
        // Let's check if we're on the right page
        const currentUrl = page.url();
        console.log(`Current URL: ${currentUrl}`);
        
        // Check if there are any error messages or loading indicators
        const errorMessages = await page.locator('.error, .alert, .warning').count();
        console.log(`Error messages found: ${errorMessages}`);
        
        // Check if the table exists but is empty
        const tableExists = await page.locator('table, .table, [role="table"]').count();
        console.log(`Table elements found: ${tableExists}`);
        
        // Take a screenshot for debugging
        await page.screenshot({ path: 'debug-process-group-list.png', fullPage: true });
        console.log('Screenshot saved as debug-process-group-list.png');
      }

      // Try to refresh the page and check again
      console.log('Refreshing page and checking again...');
      await page.reload();
      await utils.sleep(3000);
      
      const refreshedRows = await processGroupMasterPage.getProcessGroupRows();
      const refreshedRowCount = await refreshedRows.count();
      console.log(`Process groups found after refresh: ${refreshedRowCount}`);

    } catch (error: any) {
      console.error(`Error in debug test: ${error.message}`);
      await page.screenshot({ path: 'debug-error.png', fullPage: true });
      throw error;
    }
  });
});
