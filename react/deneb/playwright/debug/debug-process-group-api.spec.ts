import { test } from '@playwright/test';

import { SetupEnvironmentFacade } from '@/../playwright/shared/facades/SetupEnvironmentFacade';
import { CreateProcessGroupFacade } from '@/../playwright/shared/facades/CreateProcessGroupFacade';
import { CreateProcessFacade } from '@/../playwright/shared/facades/CreateProcessFacade';
import { ProcessGroupMasterPage } from '@/../playwright/shared/page-object-models/ProcessGroupMasterPage';
import * as utils from '@/../playwright/shared/utils';

test.describe('Debug Process Group API', () => {
  test('Check API calls and network activity', async ({ browser }) => {
    test.setTimeout(300000); // 5 minutes

    // Setup environment
    const setupEnvironmentF = new SetupEnvironmentFacade(browser);
    const result = await setupEnvironmentF.execute();
    const page = result.page;

    // Track network requests
    const requests: any[] = [];
    const responses: any[] = [];

    page.on('request', (request) => {
      requests.push({
        url: request.url(),
        method: request.method(),
        headers: request.headers(),
        postData: request.postData(),
      });
      console.log(`REQUEST: ${request.method()} ${request.url()}`);
    });

    page.on('response', (response) => {
      responses.push({
        url: response.url(),
        status: response.status(),
        statusText: response.statusText(),
      });
      console.log(`RESPONSE: ${response.status()} ${response.url()}`);
    });

    try {
      // First, create a test process
      const createProcessF = new CreateProcessFacade(page);
      const uniqueId = Date.now().toString();
      const testProcessName = `APIDebugProcessTest_${uniqueId}`;

      console.log('=== CREATING TEST PROCESS ===');
      await createProcessF.execute({
        name: testProcessName,
        category: `APIDebugProcessCategory_${uniqueId}`,
        active: true,
      });
      console.log('Test process created successfully.');

      // Create a test process group
      const createProcessGroupF = new CreateProcessGroupFacade(page);
      const testProcessGroupName = `APIDebugGroupTest_${uniqueId}`;

      console.log('=== CREATING TEST PROCESS GROUP ===');
      await createProcessGroupF.execute({
        name: testProcessGroupName,
        managementCode: `ADGT_${uniqueId}`,
        active: true,
      });
      console.log('Test process group created successfully.');

      // Navigate to Process Group list page and monitor network
      console.log('=== NAVIGATING TO PROCESS GROUP LIST ===');
      const processGroupMasterPage = new ProcessGroupMasterPage(page);
      await processGroupMasterPage.goto();
      await utils.sleep(5000); // Wait for all network requests to complete

      // Filter and analyze relevant API calls
      console.log('\n=== ANALYZING API CALLS ===');
      
      const processGroupRequests = requests.filter(req => 
        req.url.includes('process') || 
        req.url.includes('group') ||
        req.url.includes('master')
      );
      
      console.log(`Process Group related requests: ${processGroupRequests.length}`);
      processGroupRequests.forEach((req, index) => {
        console.log(`${index + 1}. ${req.method} ${req.url}`);
        if (req.postData) {
          console.log(`   POST Data: ${req.postData}`);
        }
      });

      const processGroupResponses = responses.filter(res => 
        res.url.includes('process') || 
        res.url.includes('group') ||
        res.url.includes('master')
      );
      
      console.log(`Process Group related responses: ${processGroupResponses.length}`);
      processGroupResponses.forEach((res, index) => {
        console.log(`${index + 1}. ${res.status} ${res.statusText} - ${res.url}`);
      });

      // Try to get the actual response data for list requests
      console.log('\n=== CHECKING RESPONSE DATA ===');
      const listRequests = requests.filter(req => 
        req.method === 'GET' && 
        (req.url.includes('process') && req.url.includes('group'))
      );

      if (listRequests.length > 0) {
        console.log('Found list request, waiting for response...');
        
        // Make a fresh request to the list endpoint
        try {
          const response = await page.request.get(listRequests[0].url);
          const responseData = await response.text();
          console.log('List API Response Status:', response.status());
          console.log('List API Response Data:', responseData.substring(0, 1000) + '...');
        } catch (error: any) {
          console.log('Error fetching list data:', error.message);
        }
      }

      // Check if there are any console errors
      console.log('\n=== CONSOLE MESSAGES ===');
      page.on('console', (msg) => {
        console.log(`CONSOLE ${msg.type()}: ${msg.text()}`);
      });

      // Check for JavaScript errors
      page.on('pageerror', (error) => {
        console.log(`PAGE ERROR: ${error.message}`);
      });

      // Refresh the page and monitor again
      console.log('\n=== REFRESHING PAGE ===');
      await page.reload();
      await utils.sleep(5000);

      // Final check for data
      const finalRows = await processGroupMasterPage.getProcessGroupRows();
      const finalRowCount = await finalRows.count();
      console.log(`Final row count after refresh: ${finalRowCount}`);

    } catch (error: any) {
      console.error(`Error in API debug test: ${error.message}`);
      await page.screenshot({ path: 'debug-api-error.png', fullPage: true });
      throw error;
    }
  });
});
