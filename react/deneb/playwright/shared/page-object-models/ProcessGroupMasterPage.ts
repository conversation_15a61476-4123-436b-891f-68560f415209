import { type Page } from '@playwright/test';

import { getPageUrl } from '@/../playwright/env';
import * as utils from '@/../playwright/shared/utils';
import { Select } from '@/../playwright/shared/component-models/Select';

export class ProcessGroupMasterPage {
  readonly page: Page;

  constructor(page: Page) {
    this.page = page;
  }

  async goto() {
    await this.page.goto(getPageUrl('/pages/mastersettings/processes/'));
    // Wait for page to load
    await utils.sleep(1000);

    // Try different approaches to find the Process Group tab
    let processGroupTab = this.page.getByRole('tab', { name: 'Process Group', exact: true });

    // If exact match doesn't work, try partial match
    if ((await processGroupTab.count()) === 0) {
      processGroupTab = this.page.getByRole('tab').filter({ hasText: 'Process Group' });
    }

    // If still not found, try looking for any tab containing "Group"
    if ((await processGroupTab.count()) === 0) {
      processGroupTab = this.page.getByRole('tab').filter({ hasText: 'Group' });
    }

    // Click the tab if found
    if ((await processGroupTab.count()) > 0) {
      await processGroupTab.click();
      await utils.sleep(500);
    } else {
      console.log('Process Group tab not found, assuming we are already on the correct page');
    }
  }

  // Search methods
  async searchByKeyword(keyword: string) {
    await this.page.getByPlaceholder('Process name, Management code').fill(keyword);
    await this.page.getByRole('button', { name: 'Search' }).click();
    // Wait for search results to load
    await utils.sleep(500);
  }

  async searchByProcessesIncluded(processName: string) {
    // Find the "Processes Included" select dropdown
    const processesIncludedLabel = this.page.getByText('Processes Included');
    const processSelectContainer = processesIncludedLabel
      .locator('..')
      .locator('..')
      .locator('.cl-select-container')
      .first();

    const processSelect = new Select(processSelectContainer);
    await processSelect.selectByLabel(processName);
    await this.page.getByRole('button', { name: 'Search' }).click();
    // Wait for search results to load
    await utils.sleep(500);
  }

  async resetSearch() {
    await this.page.getByRole('button', { name: 'Reset' }).click();
    // Wait for reset to complete
    await utils.sleep(500);
  }

  // Navigation methods
  async clickCreate() {
    // Try button first (like Process Master), then fallback to link
    const createButton = this.page
      .getByRole('button', { name: 'Create' })
      .or(this.page.getByRole('button', { name: '创造' })) // Chinese
      .or(this.page.getByRole('button', { name: '新規作成' })); // Japanese

    const createLink = this.page
      .getByRole('link', { name: 'Create' })
      .or(this.page.getByRole('link', { name: '创造' })) // Chinese
      .or(this.page.getByRole('link', { name: '新規作成' })); // Japanese

    if ((await createButton.count()) > 0) {
      await createButton.click();
      // Wait for new row to be added (inline creation like Process Master)
      await utils.sleep(500);
    } else if ((await createLink.count()) > 0) {
      await createLink.click();
      // Wait for navigation to create page
      await utils.sleep(1000);
    } else {
      throw new Error('Create button/link not found');
    }
  }

  // Data manipulation methods
  async selectProcessGroupByIndex(index: number) {
    const rows = await this.getProcessGroupRows();
    const checkbox = rows.nth(index).locator('input[type="checkbox"]').first();

    // Try clicking the checkbox directly first
    try {
      await checkbox.click({ timeout: 2000 });
    } catch {
      // If direct click fails, try clicking the label
      const label = rows.nth(index).locator('label').first();
      await label.click();
    }

    await utils.sleep(300);
  }

  async deleteSelectedProcessGroups() {
    await this.page.getByRole('button', { name: 'Delete' }).click();
    // Wait for delete modal to appear
    await utils.sleep(500);

    // Click the Delete button in the modal
    await this.page.getByRole('button', { name: 'Delete' }).last().click();
    // Wait for deletion to complete
    await utils.sleep(1000);
  }

  // Pagination methods
  async goToNextPage() {
    const nextButton = this.page.locator('.pagination button:has(svg[data-icon="angle-right"])');
    await nextButton.waitFor({ state: 'visible', timeout: 5000 });

    const isEnabled = await nextButton.isEnabled();
    if (!isEnabled) {
      throw new Error('Next page button is disabled - no more pages available');
    }

    await nextButton.click();
    await utils.sleep(1000);
  }

  async goToPreviousPage() {
    const prevButton = this.page.locator('.pagination button:has(svg[data-icon="angle-left"])');
    await prevButton.waitFor({ state: 'visible', timeout: 5000 });

    const isEnabled = await prevButton.isEnabled();
    if (!isEnabled) {
      throw new Error('Previous page button is disabled - already on first page');
    }

    await prevButton.click();
    await utils.sleep(1000);
  }

  async goToLastPage() {
    const lastButton = this.page.locator(
      '.pagination button:has(svg[data-icon="angle-double-right"])',
    );
    await lastButton.waitFor({ state: 'visible', timeout: 5000 });

    const isEnabled = await lastButton.isEnabled();
    if (!isEnabled) {
      throw new Error('Last page button is disabled - already on last page');
    }

    await lastButton.click();
    await utils.sleep(1000);
  }

  async goToFirstPage() {
    const firstButton = this.page.locator(
      '.pagination button:has(svg[data-icon="angle-double-left"])',
    );
    await firstButton.waitFor({ state: 'visible', timeout: 5000 });

    const isEnabled = await firstButton.isEnabled();
    if (!isEnabled) {
      throw new Error('First page button is disabled - already on first page');
    }

    await firstButton.click();
    await utils.sleep(1000);
  }

  async changeItemsPerPage(itemsPerPage: string) {
    // Find the "Per page" select dropdown
    const perPageLabel = this.page.getByText('Per page');
    const perPageSelectContainer = perPageLabel
      .locator('..')
      .locator('.cl-select-container')
      .first();

    const perPageSelect = new Select(perPageSelectContainer);
    await perPageSelect.selectByLabel(itemsPerPage);
    // Wait for page to reload with new items per page
    await utils.sleep(1000);
  }

  // Sorting methods
  async sortByManagementCode() {
    // Click on the Management Code column header to sort
    const managementCodeHeader = this.page
      .locator('th')
      .filter({ hasText: 'Management Code' })
      .locator('button');
    await managementCodeHeader.click();
    // Wait for sorting to complete
    await utils.sleep(500);
  }

  // Utility methods
  async getProcessGroupRows() {
    // Wait for the table to be visible first
    await this.page.locator('table').waitFor({ state: 'visible', timeout: 10000 });

    // Check if there are any rows, but don't fail if there aren't any
    const rowCount = await this.page.locator('table tbody tr').count();
    if (rowCount === 0) {
      console.log('No process group rows found in the table');
    }

    return this.page.locator('table tbody tr');
  }

  async getProcessGroupRowByName(name: string) {
    const rows = await this.getProcessGroupRows();
    return rows.filter({ hasText: name });
  }

  async waitForToastMessage(message: string) {
    try {
      const toast = this.page.getByText(message);
      await toast.waitFor({ state: 'visible', timeout: 5000 });
      console.log(`Toast message "${message}" found`);
    } catch (error) {
      console.log(`Toast message "${message}" not found, continuing...`);
    }
  }

  // Get the current page URL for verification
  async getCurrentUrl() {
    return this.page.url();
  }

  // Check if we're on the Process Group tab
  async isOnProcessGroupTab() {
    const processGroupTab = this.page.getByRole('tab', { name: 'Process Group', exact: true });
    const isSelected = await processGroupTab.getAttribute('aria-selected');
    return isSelected === 'true';
  }
}
