import { type Page, expect } from '@playwright/test';

import { getPageUrl } from '@/../playwright/env';
import * as utils from '@/../playwright/shared/utils';
import { Select } from '@/../playwright/shared/component-models/Select';
import { Table } from '@/../playwright/shared/component-models/Table';
import { Checkbox } from '@/../playwright/shared/component-models/Checkbox';

export class ProcessMasterPage {
  readonly page: Page;

  constructor(page: Page) {
    this.page = page;
  }

  async goto() {
    await this.page.goto(getPageUrl('/pages/mastersettings/processes/'));
    // Ensure we're on the Process tab, not Process Group
    // Use exact: true to avoid matching "Process Group" tab
    await this.page.getByRole('tab', { name: 'Process', exact: true }).click();
    // Wait for the page to load
    await utils.sleep(500);
  }

  // Search methods
  async searchByKeyword(keyword: string) {
    await this.page.getByPlaceholder('Process name, Management code').fill(keyword);
    await this.page.getByRole('button', { name: 'Search' }).click();
    // Wait for search results to load
    await utils.sleep(500);
  }

  async searchByCategory(category: string) {
    // Find the process category select by looking for the cl-select-container near the "Process Category" label
    const processCategoryLabel = this.page.getByText('Process Category');
    const categorySelectContainer = processCategoryLabel
      .locator('..')
      .locator('..')
      .locator('.cl-select-container')
      .first();

    const categorySelect = new Select(categorySelectContainer);
    await categorySelect.selectByLabel(category);
    await this.page.getByRole('button', { name: 'Search' }).click();
    // Wait for search results to load
    await utils.sleep(500);
  }

  async searchByActiveStatus(active: boolean) {
    // The active status uses checkboxes, not a select dropdown
    // Find the Active section and then the specific checkbox within it
    const activeSection = this.page.getByText('Active').first().locator('..').locator('..');

    if (active) {
      // Click the "Active" checkbox within the active section
      await activeSection.getByRole('checkbox', { name: 'Active' }).first().check();
    } else {
      // Click the "Not active" checkbox within the active section
      await activeSection.getByRole('checkbox', { name: 'Inactive' }).first().check();
    }
    await this.page.getByRole('button', { name: 'Search' }).click();
    // Wait for search results to load
    await utils.sleep(500);
  }

  async searchByUseCount(useCount: boolean) {
    // The use count uses checkboxes, not a select dropdown
    // Find the Use count section and then the specific checkbox within it
    const useCountSection = this.page.getByText('Use count').first().locator('..').locator('..');

    if (useCount) {
      // Click the "Use" checkbox within the use count section
      await useCountSection.getByRole('checkbox', { name: 'Use' }).first().check();
    } else {
      // Click the "Not use" checkbox within the use count section
      await useCountSection.getByRole('checkbox', { name: 'Not use' }).first().check();
    }
    await this.page.getByRole('button', { name: 'Search' }).click();
    // Wait for search results to load
    await utils.sleep(500);
  }

  async resetSearch() {
    await this.page.getByRole('button', { name: 'Reset' }).click();
    // Wait for reset to complete
    await utils.sleep(500);
  }

  // Action buttons
  async clickCreate() {
    await this.page.getByRole('button', { name: 'Create' }).click();
    // Wait for new row to be added
    await utils.sleep(500);
  }

  async clickImport() {
    await this.page.getByRole('button', { name: 'Import' }).click();
  }

  async clickExportAll() {
    await this.page.getByRole('button', { name: 'Export All' }).click();
  }

  async selectExportFormat(format: string) {
    await this.page.getByRole('button', { name: format }).click();
  }

  // Process list interactions
  async getProcessTable() {
    return Table.findWithin(this.page.locator('body'));
  }

  async getProcessRows() {
    const table = await this.getProcessTable();
    return table.getBodyRows();
  }

  async getProcessRowByName(name: string) {
    const table = await this.getProcessTable();
    return table.getRowByText(name);
  }

  async checkProcessByName(name: string) {
    const row = await this.getProcessRowByName(name);
    const checkbox = await Checkbox.findWithin(row.locator('.custom-checkbox').first());
    await checkbox.check();
  }

  async clickDeleteSelected() {
    await this.page.getByRole('button', { name: 'Delete' }).click();
  }

  async confirmDelete() {
    // Find the delete button in the modal
    await this.page.getByRole('button', { name: 'Delete', exact: true }).click();
    // Wait for deletion to complete
    await utils.sleep(1000);
    // Wait for toast message
    await this.waitForToastMessage('Deleted');
  }

  async editProcessName(rowIndex: number, newName: string) {
    const rows = await this.getProcessRows();
    const nameCell = rows.nth(rowIndex).locator('td').nth(3);
    await nameCell.click();

    // Wait for the input field to appear and use a more specific selector
    // The input field should be visible after clicking the cell
    const inputField = nameCell.locator('input');
    await inputField.waitFor({ state: 'visible' });
    await inputField.fill(newName);

    await this.page.keyboard.press('Enter');
    // Wait for update to complete
    await utils.sleep(500);
    // Wait for toast message
    await this.waitForToastMessage('Saved');
  }

  async addProcessCategory(rowIndex: number, categoryName: string) {
    const rows = await this.getProcessRows();
    const categoryCell = rows.nth(rowIndex).locator('td').nth(2);
    await categoryCell.click();

    await this.page
      .getByRole('button', { name: 'Add new category' })
      .waitFor({ state: 'visible', timeout: 3000 });
    await this.page.getByRole('button', { name: 'Add new category' }).click();

    await this.page.getByPlaceholder('Input Category Name').fill(categoryName);
    await this.page.getByRole('button', { name: 'Save' }).click();

    // Wait for update to complete
    await utils.sleep(500);

    // Wait for toast message
    await this.waitForToastMessage('Saved');
  }

  async editProcessCategory(rowIndex: number, category: string) {
    const rows = await this.getProcessRows();
    const categoryCell = rows.nth(rowIndex).locator('td').nth(2);
    await categoryCell.click();

    await this.page.getByText(category).waitFor({ state: 'visible', timeout: 3000 });
    await this.page.getByText(category).click();

    // Wait for update to complete
    await utils.sleep(500);
    // Wait for toast message
    await this.waitForToastMessage('Saved');
  }

  async editDisplayOrder(rowIndex: number, order: string) {
    const rows = await this.getProcessRows();
    const orderCell = rows.nth(rowIndex).locator('td').nth(4);
    await orderCell.click();

    // Wait for the input field to appear and use a more specific selector
    // The input field should be visible after clicking the cell
    const inputField = orderCell.locator('input');
    await inputField.waitFor({ state: 'visible' });
    await inputField.fill(order);

    await this.page.keyboard.press('Enter');
    // Wait for update to complete
    await utils.sleep(500);
    // Wait for toast message
    await this.waitForToastMessage('Saved');
  }

  async toggleUseCount(rowIndex: number) {
    const rows = await this.getProcessRows();
    const useCountCell = rows.nth(rowIndex).locator('td').nth(5);
    await useCountCell.locator('input[type="checkbox"]').click();
    // Wait for update to complete
    await utils.sleep(500);
    // Wait for toast message
    await this.waitForToastMessage('Saved');
  }

  async toggleActiveState(rowIndex: number) {
    const rows = await this.getProcessRows();
    const stateCell = rows.nth(rowIndex).locator('td').nth(6);
    await stateCell.click();
    // Wait for update to complete
    await utils.sleep(500);
    // Wait for toast message
    await this.waitForToastMessage('Saved');
  }

  // Pagination
  async goToNextPage() {
    await this.page.getByRole('button', { name: '>' }).click();
    // Wait for page to load
    await utils.sleep(500);
  }

  async goToPreviousPage() {
    await this.page.getByRole('button', { name: '<' }).click();
    // Wait for page to load
    await utils.sleep(500);
  }

  async goToFirstPage() {
    await this.page.getByRole('button', { name: '<<' }).click();
    // Wait for page to load
    await utils.sleep(500);
  }

  async goToLastPage() {
    await this.page.getByRole('button', { name: '>>' }).click();
    // Wait for page to load
    await utils.sleep(500);
  }

  async changeItemsPerPage(count: string) {
    try {
      // First, make sure we're at the bottom of the page where the pagination controls are
      await this.page.evaluate(() => {
        window.scrollTo(0, document.body.scrollHeight);
      });
      await utils.sleep(500);

      // Find the items per page selector
      // Use a more specific selector to find the pagination dropdown
      const paginationContainer = this.page.locator('.pagination-container').last();
      await paginationContainer.waitFor({ state: 'visible', timeout: 5000 });

      const select = await Select.findWithin(paginationContainer);
      await select.selectByLabel(count);

      // Wait for page to reload with new item count
      await utils.sleep(1000);

      // Wait for any loaders to disappear
      await this.waitForLoader();
    } catch (error: any) {
      console.log(`Error changing items per page to ${count}: ${error.message}`);
      // Continue execution even if changing items per page fails
    }
  }

  // Sorting
  async sortByManagementCode() {
    await this.page.getByRole('columnheader', { name: 'Management Code' }).click();
    // Wait for sorting to complete
    await utils.sleep(500);
  }

  // Helper methods
  async waitForToastMessage(message: string) {
    try {
      await expect(this.page.getByText(message)).toBeVisible({ timeout: 3000 });
    } catch (error) {
      // If toast is not visible, continue anyway
      console.log(`Toast message "${message}" not found, continuing...`);
    }
  }

  async waitForLoader() {
    // Wait for loader to appear
    const loader = this.page.locator('.loader');
    if (await loader.isVisible()) {
      // Wait for loader to disappear
      await loader.waitFor({ state: 'hidden', timeout: 5000 });
    }
  }

  async acceptDialog() {
    await utils.acceptDialogOnce(this.page);
  }
}
