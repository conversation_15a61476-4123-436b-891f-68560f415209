import { type Page, expect } from '@playwright/test';

import { getPageUrl } from '@/../playwright/env';
import * as utils from '@/../playwright/shared/utils';
import { Select } from '@/../playwright/shared/component-models/Select';
import { Table } from '@/../playwright/shared/component-models/Table';

export class ProcessMasterPage {
  readonly page: Page;

  constructor(page: Page) {
    this.page = page;
  }

  async goto() {
    await this.page.goto(getPageUrl('/pages/mastersettings/processes/'));
    // Ensure we're on the Process tab, not Process Group
    // Use exact: true to avoid matching "Process Group" tab
    await this.page.getByRole('tab', { name: 'Process', exact: true }).click();
    // Wait for the page to load
    await utils.sleep(500);
  }

  // Search methods
  async searchByKeyword(keyword: string) {
    await this.page.getByPlaceholder('Process name, Management code').fill(keyword);
    await this.page.getByRole('button', { name: 'Search' }).click();
    // Wait for search results to load
    await utils.sleep(500);
  }

  async searchByCategory(category: string) {
    // Find the process category select by looking for the cl-select-container near the "Process Category" label
    const processCategoryLabel = this.page.getByText('Process Category');
    const categorySelectContainer = processCategoryLabel
      .locator('..')
      .locator('..')
      .locator('.cl-select-container')
      .first();

    const categorySelect = new Select(categorySelectContainer);
    await categorySelect.selectByLabel(category);
    await this.page.getByRole('button', { name: 'Search' }).click();
    // Wait for search results to load
    await utils.sleep(500);
  }

  async searchByActiveStatus(active: boolean) {
    await this.clearActiveCheckbox();

    // The active status uses checkboxes, not a select dropdown
    // Find the Active section and then click the label (not the checkbox directly)
    const activeSection = this.page
      .getByText('Active')
      .first()
      .locator('xpath=./following-sibling::div[1]');

    await activeSection.waitFor({ state: 'visible' });

    if (active) {
      // Click the "Active" label within the active section - use exact match
      await activeSection
        .locator('span.custom-control-label')
        .filter({ hasText: /^Active$/ })
        .click();
    } else {
      // Click the "Inactive" label within the active section
      await activeSection
        .locator('span.custom-control-label')
        .filter({ hasText: 'Inactive' })
        .click();
    }
    await this.page.getByRole('button', { name: 'Search' }).click();
    // Wait for search results to load
    await utils.sleep(500);
  }

  async searchByUseCount(useCount: boolean) {
    await this.clearActiveCheckbox();

    // The use count uses checkboxes, not a select dropdown
    // Find the Use count section and then the specific checkbox within it
    const useCountSection = this.page
      .getByText('Use count')
      .first()
      .locator('xpath=./following-sibling::div[1]');

    await useCountSection.waitFor({ state: 'visible' });

    if (useCount) {
      // Click the "Use" label within the use count section - use exact match
      await useCountSection
        .locator('span.custom-control-label')
        .filter({ hasText: /^Use$/ })
        .click();
    } else {
      // Click the "Not use" label within the use count section
      await useCountSection
        .locator('span.custom-control-label')
        .filter({ hasText: 'Not use' })
        .click();
    }
    await this.page.getByRole('button', { name: 'Search' }).click();
    // Wait for search results to load
    await utils.sleep(500);
  }

  async clearActiveCheckbox() {
    await this.page
      .getByText('Active')
      .first()
      .locator('xpath=./following-sibling::div[1]')
      .locator('span.custom-control-label')
      .filter({ hasText: /^Active$/ })
      .click();
  }

  async resetSearch() {
    await this.page.getByRole('button', { name: 'Reset' }).click();
    // Wait for reset to complete
    await utils.sleep(500);
  }

  // Action buttons
  async clickCreate() {
    await this.page.getByRole('button', { name: 'Create' }).click();
    // Wait for new row to be added
    await utils.sleep(500);
  }

  async clickImport() {
    await this.page.getByRole('button', { name: 'Import' }).click();
  }

  async clickExportAll() {
    await this.page.getByRole('button', { name: 'Export All' }).click();
  }

  async selectExportFormat(format: string) {
    await this.page.getByRole('button', { name: format }).click();
  }

  // Process list interactions
  async getProcessTable() {
    return Table.findWithin(this.page.locator('body'));
  }

  async getProcessRows() {
    const table = await this.getProcessTable();
    return table.getBodyRows();
  }

  async getProcessRowByName(name: string) {
    const table = await this.getProcessTable();
    return table.getRowByText(name);
  }

  async checkProcessByName(name: string) {
    const row = await this.getProcessRowByName(name);
    const firstCell = row.locator('td').first();

    try {
      // First try clicking the cell
      await firstCell.click();
    } catch (error) {
      // Fallback: force click the checkbox directly
      await firstCell.locator('input[type="checkbox"]').click({ force: true });
    }
  }

  async clickDeleteSelected() {
    await this.page.getByRole('button', { name: 'Delete' }).click();
  }

  async confirmDelete() {
    // Find the delete button in the modal
    await this.page.getByRole('button', { name: 'Delete', exact: true }).click();
    // Wait for toast message
    await this.waitForToastMessage('Deleted');
  }

  async editProcessName(rowIndex: number, newName: string) {
    const rows = await this.getProcessRows();
    const nameCell = rows.nth(rowIndex).locator('td').nth(3);
    await nameCell.click();

    // Wait for the input field to appear and use a more specific selector
    // The input field should be visible after clicking the cell
    const inputField = nameCell.locator('input');
    await inputField.waitFor({ state: 'visible' });
    await inputField.fill(newName);

    await this.page.keyboard.press('Enter');
    // Wait for update to complete
    await utils.sleep(500);
    // Wait for toast message
    await this.waitForToastMessage('Saved');
  }

  async addProcessCategory(rowIndex: number, categoryName: string) {
    const rows = await this.getProcessRows();
    const categoryCell = rows.nth(rowIndex).locator('td').nth(2);
    await categoryCell.click();

    await this.page
      .getByRole('button', { name: 'Add new category' })
      .waitFor({ state: 'visible', timeout: 3000 });
    await this.page.getByRole('button', { name: 'Add new category' }).click();

    await this.page.getByPlaceholder('Input Category Name').fill(categoryName);
    await this.page.getByRole('button', { name: 'Save' }).click();

    // Wait for update to complete
    await utils.sleep(500);

    // Wait for toast message
    await this.waitForToastMessage('Saved');
  }

  async editProcessCategory(rowIndex: number, newCategoryName: string) {
    const rows = await this.getProcessRows();
    const categoryCell = rows.nth(rowIndex).locator('td').nth(2);
    await categoryCell.click();

    // Wait for the dropdown to appear and find the first category item
    await utils.sleep(500);

    // Click the pencil (edit) icon on the first category item
    const editIcon = this.page.locator('i[class*="fa-pencil"], svg[data-icon="pencil"]').first();
    await editIcon.waitFor({ state: 'visible', timeout: 3000 });
    await editIcon.click();

    // Wait for the edit form to appear
    await utils.sleep(500);

    // Fill in the new category name in the form
    const categoryNameInput = this.page.getByPlaceholder(/input.*category.*name/i);
    await categoryNameInput.waitFor({ state: 'visible', timeout: 3000 });
    await categoryNameInput.clear();
    await categoryNameInput.fill(newCategoryName);

    // Click the Save button
    await this.page.getByRole('button', { name: 'Save' }).click();
    // Wait for toast message
    await this.waitForToastMessage('Saved');
    // select the edited option
    await editIcon.locator('..').click();
    // Wait for toast message
    await this.waitForToastMessage('Saved');
  }

  async editDisplayOrder(rowIndex: number, order: string) {
    const rows = await this.getProcessRows();
    const orderCell = rows.nth(rowIndex).locator('td').nth(4);
    await orderCell.click();

    // Wait for the input field to appear and use a more specific selector
    // The input field should be visible after clicking the cell
    const inputField = orderCell.locator('input');
    await inputField.waitFor({ state: 'visible' });
    await inputField.fill(order);

    await this.page.keyboard.press('Enter');
    // Wait for update to complete
    await utils.sleep(500);
    // Wait for toast message
    await this.waitForToastMessage('Saved');
  }

  async toggleUseCount(rowIndex: number) {
    const rows = await this.getProcessRows();
    const useCountCell = rows.nth(rowIndex).locator('td').nth(5);

    try {
      // First try clicking the cell
      await useCountCell.click();
    } catch (error) {
      // Fallback: force click the checkbox directly
      await useCountCell.locator('input[type="checkbox"]').click({ force: true });
    }

    // Wait for toast message
    await this.waitForToastMessage('Saved');
  }

  async toggleActiveState(rowIndex: number) {
    const rows = await this.getProcessRows();
    const stateCell = rows.nth(rowIndex).locator('td').nth(6);
    await stateCell.click();
    // Wait for update to complete
    await utils.sleep(500);
    // Wait for toast message
    await this.waitForToastMessage('Saved');
  }

  // Pagination
  async goToNextPage() {
    await this.page.getByRole('button', { name: '>' }).click();
    // Wait for page to load
    await utils.sleep(500);
  }

  async goToPreviousPage() {
    await this.page.getByRole('button', { name: '<' }).click();
    // Wait for page to load
    await utils.sleep(500);
  }

  async goToFirstPage() {
    await this.page.getByRole('button', { name: '<<' }).click();
    // Wait for page to load
    await utils.sleep(500);
  }

  async goToLastPage() {
    await this.page.getByRole('button', { name: '>>' }).click();
    // Wait for page to load
    await utils.sleep(500);
  }

  async changeItemsPerPage(count: string) {
    try {
      // First, make sure we're at the bottom of the page where the pagination controls are
      await this.page.evaluate(() => {
        window.scrollTo(0, document.body.scrollHeight);
      });
      await utils.sleep(500);

      // Find the React Select component for "Per page"
      const perPageSelect = this.page.locator('span[aria-label="selectbox-per-page"]');
      await perPageSelect.waitFor({ state: 'visible', timeout: 5000 });

      // Click on the React Select to open the dropdown
      await perPageSelect.click();
      await utils.sleep(500);

      // Find and click the option with the desired count in the dropdown menu
      // Use a more specific selector to avoid conflicts with other elements
      const option = this.page
        .locator(`div[id*="react-select"][class*="option"]`)
        .filter({ hasText: new RegExp(`^${count}$`) });
      await option.waitFor({ state: 'visible', timeout: 3000 });
      await option.click();

      // Wait for page to reload with new item count
      await utils.sleep(1000);

      // Wait for any loaders to disappear
      await this.waitForLoader();
    } catch (error: any) {
      console.log(`Error changing items per page to ${count}: ${error.message}`);
      // Continue execution even if changing items per page fails
    }
  }

  // Sorting
  async sortByManagementCode() {
    const managementCodeHeader = this.page.getByRole('button', { name: 'Management Code' });
    await managementCodeHeader.waitFor({ state: 'visible', timeout: 5000 });
    await managementCodeHeader.click();
    await utils.sleep(500);
  }

  // Helper methods
  async waitForToastMessage(message: string) {
    try {
      await expect(this.page.getByText(message)).toBeVisible({ timeout: 3000 });
    } catch (error) {
      // If toast is not visible, continue anyway
      console.log(`Toast message "${message}" not found, continuing...`);
    }
  }

  async waitForLoader() {
    // Wait for loader to appear
    const loader = this.page.locator('.loader');
    if (await loader.isVisible()) {
      // Wait for loader to disappear
      await loader.waitFor({ state: 'hidden', timeout: 5000 });
    }
  }
}
