import { type Locator } from '@playwright/test';

import * as testIds from '@/test-utils/test-ids';

export class Select {
  private container: Locator;

  static async findWithin(target: Locator) {
    const container = target.locator('.cl-select-container');
    return new Select(container);
  }

  constructor(container: Locator) {
    this.container = container;
  }

  async openDropdown() {
    await this.container
      .locator(`[data-testid="${testIds.Select.dropdownIndicator}"]`)
      .last()
      .click();
  }

  async selectByLabel(label: string) {
    await this.openDropdown();
    await this.container.locator(`[data-testid="${testIds.Select.menu}"]`).getByText(label).click();
  }
}
