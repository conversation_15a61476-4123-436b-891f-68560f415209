import { type Page } from '@playwright/test';

import { getPageUrl } from '@/../playwright/env';
import * as utils from '@/../playwright/shared/utils';
import { Select } from '@/../playwright/shared/component-models/Select';
import { CreateProcessFacade } from './CreateProcessFacade';

export interface CreateProcessGroupOptions {
  name: string;
  managementCode?: string;
  processes?: string[];
  active?: boolean;
}

export class CreateProcessGroupFacade {
  readonly page: Page;

  constructor(page: Page) {
    this.page = page;
  }

  async execute(options: CreateProcessGroupOptions) {
    // First, ensure we have at least one process to associate with the process group
    const baseProcessName = await this.ensureProcessExists();

    // If no processes were specified, use the base process we just created
    if (!options.processes || options.processes.length === 0) {
      options.processes = [baseProcessName];
    }

    // Navigate to the process group creation page
    await this.page.goto(getPageUrl('/pages/mastersettings/processes/'));
    await utils.sleep(1000);

    // Try different approaches to find the Process Group tab
    let processGroupTab = this.page.getByRole('tab', { name: 'Process Group', exact: true });

    // If exact match doesn't work, try partial match
    if ((await processGroupTab.count()) === 0) {
      processGroupTab = this.page.getByRole('tab').filter({ hasText: 'Process Group' });
    }

    // If still not found, try looking for any tab containing "Group"
    if ((await processGroupTab.count()) === 0) {
      processGroupTab = this.page.getByRole('tab').filter({ hasText: 'Group' });
    }

    // Click the tab if found
    if ((await processGroupTab.count()) > 0) {
      await processGroupTab.click();
      await utils.sleep(500);
    } else {
      console.log('Process Group tab not found, assuming we are already on the correct page');
    }

    // Try Create button first, then fallback to link
    const createButton = this.page
      .getByRole('button', { name: 'Create' })
      .or(this.page.getByRole('button', { name: '创造' })) // Chinese
      .or(this.page.getByRole('button', { name: '新規作成' })); // Japanese

    const createLink = this.page
      .getByRole('link', { name: 'Create' })
      .or(this.page.getByRole('link', { name: '创造' })) // Chinese
      .or(this.page.getByRole('link', { name: '新規作成' })); // Japanese

    if ((await createButton.count()) > 0) {
      await createButton.click();
      await utils.sleep(500);
    } else if ((await createLink.count()) > 0) {
      await createLink.click();
      await utils.sleep(1000);
    } else {
      throw new Error('Create button/link not found');
    }

    // Fill in the process group name
    await this.page.getByPlaceholder('Input Process Group Name').fill(options.name);

    // Fill in management code if provided
    if (options.managementCode) {
      await this.page.getByPlaceholder('Input Management Code').fill(options.managementCode);
    }

    // Select processes if provided
    if (options.processes && options.processes.length > 0) {
      for (const processName of options.processes) {
        await this.selectProcess(processName);
      }
    }

    // Note: Active status might need to be set after creation on the list page
    // For now, we'll skip this as it may not be available on the create form

    // Save the process group (support multiple languages)
    const saveButton = this.page
      .getByRole('button', { name: 'Save' })
      .or(this.page.getByRole('button', { name: '保存' })) // Japanese
      .or(this.page.getByRole('button', { name: '保存' })); // Chinese (same as Japanese)

    try {
      await saveButton.click();
    } catch (error) {
      console.log('Normal click failed, trying force click:', error);
      try {
        await saveButton.click({ force: true });
      } catch (forceError) {
        console.log('Force click also failed, trying JavaScript execution:', forceError);
        // Use JavaScript to directly trigger the click
        await saveButton.evaluate((button) => button.click());
      }
    }

    // Wait for save to complete
    await utils.sleep(2000);

    // Wait for success message or navigation back to list
    try {
      await this.page.waitForURL('**/pages/mastersettings/processes/**', { timeout: 10000 });
    } catch {
      // If URL doesn't change, check for success message
      await utils.sleep(1000);
    }

    // Navigate back to the Process Group list to ensure we're on the right page
    await this.page.goto(getPageUrl('/pages/mastersettings/processes/'));
    // Click on Process Group tab to ensure we're viewing the right list
    await this.page.getByRole('tab', { name: 'Process Group', exact: true }).click();
    await utils.sleep(1000);
  }

  private async selectProcess(processName: string) {
    // This would depend on the actual UI structure for selecting processes
    // For now, we'll implement a basic approach
    try {
      // Look for a process selection area (could be checkboxes, multi-select, etc.)
      const processCheckbox = this.page.getByLabel(processName);
      if (await processCheckbox.isVisible()) {
        await processCheckbox.check();
      } else {
        // If it's a multi-select dropdown
        const processSelect = this.page
          .locator('.cl-select-container')
          .filter({ hasText: 'Process' });
        if ((await processSelect.count()) > 0) {
          const select = new Select(processSelect.first());
          await select.selectByLabel(processName);
        }
      }
    } catch (error) {
      console.log(`Could not select process "${processName}": ${error}`);
      // Continue without failing the entire creation
    }
  }

  private async ensureProcessExists(): Promise<string> {
    try {
      // Create a basic process that can be used for process groups
      const createProcessF = new CreateProcessFacade(this.page);
      const processName = `Base Process ${Date.now()}`;

      await createProcessF.execute({
        name: processName,
        category: 'Default Category',
        active: true,
      });

      console.log(`Created base process: ${processName}`);
      return processName;
    } catch (error) {
      console.log(`Could not create base process: ${error}`);
      // Return a default name, maybe there are existing processes
      return 'Default Process';
    }
  }
}
