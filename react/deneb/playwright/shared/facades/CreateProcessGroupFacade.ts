import { type Page } from '@playwright/test';

import { getPageUrl } from '@/../playwright/env';
import * as utils from '@/../playwright/shared/utils';
import { Select } from '@/../playwright/shared/component-models/Select';

export interface CreateProcessGroupOptions {
  name: string;
  managementCode?: string;
  processes?: string[];
  active?: boolean;
}

export class CreateProcessGroupFacade {
  readonly page: Page;

  constructor(page: Page) {
    this.page = page;
  }

  async execute(options: CreateProcessGroupOptions) {
    // Navigate to the process group creation page
    await this.page.goto(getPageUrl('/pages/mastersettings/processes/'));

    // Click on Process Group tab
    await this.page.getByRole('tab', { name: 'Process Group' }).click();
    await utils.sleep(500);

    // Click Create button (it's actually a link, not a button)
    const createLink = this.page
      .getByRole('link', { name: 'Create' })
      .or(this.page.getByRole('link', { name: '创造' })) // Chinese
      .or(this.page.getByRole('link', { name: '新規作成' })); // Japanese
    await createLink.click();
    await utils.sleep(1000);

    // Fill in the process group name
    await this.page.getByPlaceholder('Input Process Group Name').fill(options.name);

    // Fill in management code if provided
    if (options.managementCode) {
      await this.page.getByPlaceholder('Input Management Code').fill(options.managementCode);
    }

    // Select processes if provided
    if (options.processes && options.processes.length > 0) {
      for (const processName of options.processes) {
        await this.selectProcess(processName);
      }
    }

    // Note: Active status might need to be set after creation on the list page
    // For now, we'll skip this as it may not be available on the create form

    // Save the process group (support multiple languages)
    const saveButton = this.page
      .getByRole('button', { name: 'Save' })
      .or(this.page.getByRole('button', { name: '保存' })) // Japanese
      .or(this.page.getByRole('button', { name: '保存' })); // Chinese (same as Japanese)
    await saveButton.click();

    // Wait for save to complete
    await utils.sleep(2000);

    // Wait for success message or navigation back to list
    try {
      await this.page.waitForURL('**/pages/mastersettings/processes/**', { timeout: 10000 });
    } catch {
      // If URL doesn't change, check for success message
      await utils.sleep(1000);
    }

    // Navigate back to the Process Group list to ensure we're on the right page
    await this.page.goto(getPageUrl('/pages/mastersettings/processes/'));
    // Click on Process Group tab to ensure we're viewing the right list
    await this.page.getByRole('tab', { name: 'Process Group', exact: true }).click();
    await utils.sleep(1000);
  }

  private async selectProcess(processName: string) {
    // This would depend on the actual UI structure for selecting processes
    // For now, we'll implement a basic approach
    try {
      // Look for a process selection area (could be checkboxes, multi-select, etc.)
      const processCheckbox = this.page.getByLabel(processName);
      if (await processCheckbox.isVisible()) {
        await processCheckbox.check();
      } else {
        // If it's a multi-select dropdown
        const processSelect = this.page
          .locator('.cl-select-container')
          .filter({ hasText: 'Process' });
        if ((await processSelect.count()) > 0) {
          const select = new Select(processSelect.first());
          await select.selectByLabel(processName);
        }
      }
    } catch (error) {
      console.log(`Could not select process "${processName}": ${error}`);
      // Continue without failing the entire creation
    }
  }
}
