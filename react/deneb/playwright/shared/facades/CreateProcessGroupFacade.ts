import { type Page } from '@playwright/test';

import { getPageUrl } from '@/../playwright/env';
import * as utils from '@/../playwright/shared/utils';
import { Select } from '@/../playwright/shared/component-models/Select';
import { CreateProcessFacade } from './CreateProcessFacade';

export interface CreateProcessGroupOptions {
  name: string;
  managementCode?: string;
  processes?: string[];
  active?: boolean;
}

export class CreateProcessGroupFacade {
  readonly page: Page;

  constructor(page: Page) {
    this.page = page;
  }

  async execute(options: CreateProcessGroupOptions) {
    console.log(`Creating process group: ${options.name}`);

    // Navigate directly to the process group creation page (based on debug findings)
    await this.page.goto(getPageUrl('/pages/mastersettings/processes/group/new/'));
    await utils.sleep(2000);

    // Fill in the process group name using exact selector from debug
    const nameInput = this.page.locator('input[name="name"]');
    if ((await nameInput.count()) > 0) {
      await nameInput.fill(options.name);
      console.log(`Filled Process Group Name: ${options.name}`);
    } else {
      throw new Error('Process Group Name input not found');
    }

    // Fill in management code if provided using exact selector from debug
    if (options.managementCode) {
      const codeInput = this.page.locator('input[name="code"]');
      if ((await codeInput.count()) > 0) {
        // Ensure management code meets requirements: uppercase letters only, max 7 chars
        const validManagementCode = this.generateValidManagementCode(options.managementCode);
        await codeInput.fill(validManagementCode);
        console.log(`Filled Management Code: ${validManagementCode}`);
      }
    }

    // Fill display order (optional) using exact selector from debug
    const orderInput = this.page.locator('input[name="order"]');
    if ((await orderInput.count()) > 0) {
      await orderInput.fill('1');
      console.log('Filled Display Order: 1');
    }

    // Process selection is required for successful save
    if (options.processes && options.processes.length > 0) {
      console.log(`Selecting processes: ${options.processes.join(', ')}`);
      await Promise.all(options.processes.map((processName) => this.selectProcess(processName)));
    } else {
      // If no specific processes provided, try to select any available process
      console.log('No specific processes provided, attempting to select any available process...');
      await this.selectAnyAvailableProcess();
    }

    // Wait a moment for the form to update after process selection
    await utils.sleep(1000);

    // Save the process group using multiple selector strategies
    let saveButton = this.page
      .getByRole('button', { name: 'Save' })
      .or(this.page.getByRole('button', { name: '保存' })) // Chinese
      .or(this.page.getByRole('button', { name: '保存' })) // Japanese
      .or(this.page.locator('button[type="submit"]').filter({ hasText: 'Save' }))
      .or(this.page.locator('button').filter({ hasText: 'Save' }))
      .or(this.page.locator('button[type="submit"]'))
      .or(this.page.locator('button').filter({ hasText: /save/i }));

    const buttonCount = await saveButton.count();
    console.log(`Found ${buttonCount} potential save buttons`);

    if (buttonCount === 0) {
      // Debug: check current page URL and what buttons are available
      const currentUrl = this.page.url();
      console.log(`Current page URL: ${currentUrl}`);

      const allButtons = await this.page.locator('button').count();
      console.log(`Total buttons found on page: ${allButtons}`);

      if (allButtons > 0) {
        for (let i = 0; i < Math.min(allButtons, 10); i++) {
          const buttonText = await this.page.locator('button').nth(i).textContent();
          const buttonType = await this.page.locator('button').nth(i).getAttribute('type');
          const buttonClass = await this.page.locator('button').nth(i).getAttribute('class');
          console.log(
            `Button ${i + 1}: "${buttonText?.trim()}" (type: ${buttonType}, class: ${buttonClass})`,
          );
        }
      }

      // Also check for input[type="submit"] elements
      const submitInputs = await this.page.locator('input[type="submit"]').count();
      console.log(`Submit inputs found: ${submitInputs}`);

      if (submitInputs > 0) {
        for (let i = 0; i < submitInputs; i++) {
          const inputValue = await this.page
            .locator('input[type="submit"]')
            .nth(i)
            .getAttribute('value');
          console.log(`Submit input ${i + 1}: value="${inputValue}"`);
        }
      }

      throw new Error('Save button not found');
    }

    console.log('Attempting to save process group...');
    try {
      await saveButton.click();
      console.log('Save button clicked successfully');
    } catch (error) {
      console.log('Normal click failed, trying force click:', error);
      try {
        await saveButton.click({ force: true });
        console.log('Force click succeeded');
      } catch (forceError) {
        console.log('Force click also failed, trying JavaScript execution:', forceError);
        await saveButton.evaluate((button) => button.click());
        console.log('JavaScript click executed');
      }
    }

    // Wait for save to complete and check result
    await utils.sleep(3000);

    const currentUrl = this.page.url();
    console.log(`Current URL after save: ${currentUrl}`);

    // Check if we're redirected back to the list page or if there are validation errors
    if (currentUrl.includes('/new/')) {
      // Still on create page, check for validation errors
      const errorMessages = await this.page
        .locator('.alert-danger, .error, [class*="error"]')
        .allTextContents();
      if (errorMessages.length > 0) {
        console.log(`Validation errors found: ${errorMessages.join(', ')}`);
        throw new Error(`Process group creation failed with errors: ${errorMessages.join(', ')}`);
      }
    }

    // Navigate back to the Process Group list page to ensure we can see the created item
    console.log('Navigating back to Process Group list page...');
    await this.page.goto(getPageUrl('/pages/mastersettings/processes/'));
    await utils.sleep(2000);

    // Click on Process Group tab to ensure we're viewing the right list
    const processGroupTab = this.page.getByRole('tab', { name: 'Process Group', exact: true });
    if ((await processGroupTab.count()) > 0) {
      await processGroupTab.click();
      await utils.sleep(2000);
    }

    // Force refresh the page to ensure we see the latest data
    console.log('Refreshing page to ensure latest data is loaded...');
    await this.page.reload();
    await utils.sleep(2000);

    // Click on Process Group tab again after refresh
    const processGroupTabAfterRefresh = this.page.getByRole('tab', {
      name: 'Process Group',
      exact: true,
    });
    if ((await processGroupTabAfterRefresh.count()) > 0) {
      await processGroupTabAfterRefresh.click();
      await utils.sleep(1000);
    }

    console.log(`Process group "${options.name}" created successfully`);
  }

  private generateValidManagementCode(input: string): string {
    // Convert to uppercase and keep only letters and numbers
    let code = input.toUpperCase().replace(/[^A-Z0-9]/g, '');

    // If the code is too long, truncate it to 7 characters
    if (code.length > 7) {
      code = code.substring(0, 7);
    }

    // If the code is too short, pad it with random letters/numbers to make it exactly 7 characters
    while (code.length < 7) {
      const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
      code += chars.charAt(Math.floor(Math.random() * chars.length));
    }

    return code;
  }

  private async selectProcess(processName: string) {
    try {
      console.log(`Attempting to select process: ${processName}`);

      // Look for process selection dropdown/multi-select
      const processSelect = this.page
        .locator('.cl-select-container')
        .filter({ hasText: 'Process' })
        .or(this.page.locator('[data-testid*="process"]'))
        .or(this.page.locator('select[name*="process"]'))
        .or(this.page.locator('.select').filter({ hasText: 'Process' }));

      if ((await processSelect.count()) > 0) {
        console.log('Found process select dropdown');

        // Click to open the dropdown
        await processSelect.first().click();
        await utils.sleep(1000);

        // Wait for dropdown options to load
        await utils.sleep(1000);

        // Look for the specific process option with multiple selector strategies
        const processOption = this.page
          .locator('.cl-select-option')
          .filter({ hasText: processName })
          .or(this.page.locator('[role="option"]').filter({ hasText: processName }))
          .or(this.page.locator('option').filter({ hasText: processName }))
          .or(this.page.getByText(processName))
          .or(this.page.locator('div').filter({ hasText: new RegExp(`^${processName}$`) }));

        // Also try to find any available options to debug
        const allOptions = this.page
          .locator('.cl-select-option')
          .or(this.page.locator('[role="option"]'))
          .or(this.page.locator('option'));

        const optionCount = await allOptions.count();
        console.log(`Found ${optionCount} total options in dropdown`);

        if (optionCount > 0) {
          // Log the first few options for debugging
          for (let i = 0; i < Math.min(optionCount, 3); i++) {
            const optionText = await allOptions.nth(i).textContent();
            console.log(`Option ${i + 1}: "${optionText}"`);
          }
        }

        if ((await processOption.count()) > 0) {
          console.log(`Found process option: ${processName}`);
          await processOption.first().click();
          console.log(`Successfully selected process: ${processName}`);

          // Close the dropdown safely and ensure the selection is registered
          console.log('Closing dropdown...');
          // Try pressing Escape key first (safest method)
          await this.page.keyboard.press('Escape');
          await utils.sleep(500);

          // Verify we're still on the correct page
          const currentUrl = this.page.url();
          if (!currentUrl.includes('/new/')) {
            console.log(`Warning: Page URL changed to ${currentUrl}`);
            // Navigate back to the creation page if needed
            await this.page.goto(
              'https://app.local.crowdlog.jp/pages/mastersettings/processes/group/new/',
            );
            await utils.sleep(2000);
          }
          console.log('Dropdown closed successfully');
        } else {
          console.log(`Process option not found for: ${processName}`);

          // If we have options but can't find the specific one, try selecting the first available
          if (optionCount > 0) {
            console.log('Selecting first available option as fallback...');
            await allOptions.first().click();
            console.log('Selected first available option');

            // Close the dropdown safely
            await this.page.keyboard.press('Escape');
            await utils.sleep(500);
          } else {
            // Try using the Select utility as final fallback
            console.log('Trying Select utility as fallback...');
            const select = new Select(processSelect.first());
            await select.selectByLabel(processName);
            console.log(`Successfully selected process using Select utility: ${processName}`);
          }
        }
      } else {
        // Try checkbox approach
        const processCheckbox = this.page
          .getByLabel(processName)
          .or(this.page.locator(`input[type="checkbox"][value*="${processName}"]`));

        if ((await processCheckbox.count()) > 0) {
          console.log('Found process checkbox');
          await processCheckbox.check();
          console.log(`Successfully checked process: ${processName}`);
        } else {
          console.log(`Process selection element not found for: ${processName}`);
        }
      }
    } catch (error) {
      console.log(`Could not select process "${processName}": ${error}`);
    }
  }

  private async selectAnyAvailableProcess() {
    try {
      console.log('Looking for any available process to select...');

      // Look for process selection dropdown
      const processSelect = this.page
        .locator('.cl-select-container')
        .filter({ hasText: 'Process' })
        .or(this.page.locator('[data-testid*="process"]'))
        .or(this.page.locator('select[name*="process"]'))
        .or(this.page.locator('.select').filter({ hasText: 'Process' }));

      if ((await processSelect.count()) > 0) {
        console.log('Found process select dropdown, clicking to open...');
        await processSelect.first().click();
        await utils.sleep(1000);

        // Look for the first available option
        const firstOption = this.page
          .locator('.cl-select-option')
          .or(this.page.locator('option'))
          .or(this.page.locator('[role="option"]'))
          .first();

        if ((await firstOption.count()) > 0) {
          const optionText = await firstOption.textContent();
          console.log(`Selecting first available process: ${optionText}`);
          await firstOption.click();
          console.log('Successfully selected first available process');

          // Close the dropdown safely and ensure the selection is registered
          console.log('Closing dropdown...');
          await this.page.keyboard.press('Escape');
          await utils.sleep(500);
          console.log('Dropdown closed successfully');
        } else {
          console.log('No process options found in dropdown');
        }
      } else {
        // Try checkbox approach - select the first checkbox
        const firstCheckbox = this.page
          .locator('input[type="checkbox"]')
          .filter({ hasText: /process/i })
          .first();

        if ((await firstCheckbox.count()) > 0) {
          console.log('Found process checkbox, checking first one...');
          await firstCheckbox.check();
          console.log('Successfully checked first available process');
        } else {
          console.log('No process selection elements found');
        }
      }
    } catch (error) {
      console.log(`Could not select any available process: ${error}`);
    }
  }
}
