import { type <PERSON>rowser } from '@playwright/test';

import { LoginPage } from '../page-object-models/LoginPage';

/**
 * Simplified environment setup that bypasses the problematic PersonalSettingsPage
 * This facade directly logs in with admin credentials without changing language settings
 */
export class SimpleEnvironmentFacade {
  readonly browser: Browser;

  constructor(browser: Browser) {
    this.browser = browser;
  }

  async execute(): Promise<{ email: string; password: string }> {
    const page = await this.browser.newPage();

    try {
      console.log('Setting up simplified test environment...');

      // Use admin credentials directly
      const email = '<EMAIL>';
      const password = '0000';

      // Login with admin credentials
      const loginPage = new LoginPage(page);
      await loginPage.goto();
      await loginPage.loginWithEmail(email, password);

      // Wait for login to complete
      await page.waitForTimeout(3000);

      // Verify login was successful by checking URL
      const currentUrl = page.url();
      if (currentUrl.includes('login')) {
        throw new Error('Login failed - still on login page');
      }

      console.log(`Successfully logged in as: ${email}`);
      console.log(`Current URL: ${currentUrl}`);

      return { email, password };
    } finally {
      await page.close();
    }
  }
}
