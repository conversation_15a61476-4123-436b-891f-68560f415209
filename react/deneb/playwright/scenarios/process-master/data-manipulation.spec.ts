import { expect, test, type Page } from '@playwright/test';

import { SetupEnvironmentFacade } from '@/../playwright/shared/facades/SetupEnvironmentFacade';
import { CreateProcessFacade } from '@/../playwright/shared/facades/CreateProcessFacade';
import { ProcessMasterPage } from '@/../playwright/shared/page-object-models/ProcessMasterPage';
import * as utils from '@/../playwright/shared/utils';

test.describe('Process Master Data Manipulation', () => {
  let page: Page;

  test.beforeEach(async ({ browser }) => {
    const setupEnvironmentF = new SetupEnvironmentFacade(browser);
    const result = await setupEnvironmentF.execute();
    page = result.page;

    // Create a test process to search for
    const createProcessF = new CreateProcessFacade(page);
    const uniqueId = Date.now().toString();
    await createProcessF.execute({
      name: `EditTest_${uniqueId}`,
      category: `EditCateTest_${uniqueId}`,
    });
  });

  test('Click "Create" to add a new process row into list', async () => {
    const processMasterPage = new ProcessMasterPage(page);
    await processMasterPage.goto();

    // Get initial row count
    const initialRows = await processMasterPage.getProcessRows();
    const initialCount = await initialRows.count();

    // Click create button
    await processMasterPage.clickCreate();

    // Verify a new row was added
    const newRows = await processMasterPage.getProcessRows();
    await expect(newRows).toHaveCount(initialCount + 1);
  });

  test('Edit process category', async () => {
    // Create a test process
    const createProcessF = new CreateProcessFacade(page);
    const uniqueId = Date.now().toString();
    const testProcessName = `CategoryTest_${uniqueId}`;
    await createProcessF.execute({ name: testProcessName });

    const processMasterPage = new ProcessMasterPage(page);
    await processMasterPage.goto();

    // Search for the test process
    await processMasterPage.searchByKeyword(testProcessName);
    await utils.sleep(500);

    // Edit the category
    await processMasterPage.editProcessCategory(0, 'New Category');
    await utils.sleep(1000);

    // Verify the category was updated
    const processRow = await processMasterPage.getProcessRowByName(testProcessName);
    await expect(processRow.locator('td').nth(2)).toContainText('New Category');
  });

  test('Edit process name', async () => {
    // Create a test process
    const createProcessF = new CreateProcessFacade(page);
    const uniqueId = Date.now().toString();
    const testProcessName = `NameTest_${uniqueId}`;
    await createProcessF.execute({ name: testProcessName });

    const processMasterPage = new ProcessMasterPage(page);
    await processMasterPage.goto();

    // Search for the test process
    await processMasterPage.searchByKeyword(testProcessName);
    await utils.sleep(500);

    // Edit the name
    const newName = `Updated_${testProcessName}`;
    await processMasterPage.editProcessName(0, newName);
    await utils.sleep(1000);

    // Verify the name was updated
    const processRow = await processMasterPage.getProcessRowByName(newName);
    await expect(processRow).toBeVisible();
  });

  test('Edit display order', async () => {
    // Create a test process
    const createProcessF = new CreateProcessFacade(page);
    const uniqueId = Date.now().toString();
    const testProcessName = `OrderTest_${uniqueId}`;
    await createProcessF.execute({ name: testProcessName });

    const processMasterPage = new ProcessMasterPage(page);
    await processMasterPage.goto();

    // Search for the test process
    await processMasterPage.searchByKeyword(testProcessName);
    await utils.sleep(500);

    // Edit the display order
    const newOrder = '999';
    await processMasterPage.editDisplayOrder(0, newOrder);
    await utils.sleep(1000);

    // Verify the order was updated
    const processRow = await processMasterPage.getProcessRowByName(testProcessName);
    await expect(processRow.locator('td').nth(4)).toContainText(newOrder);
  });

  test('Toggle use count', async () => {
    // Create a test process with use count disabled
    const createProcessF = new CreateProcessFacade(page);
    const uniqueId = Date.now().toString();
    const testProcessName = `UseCountTest_${uniqueId}`;
    await createProcessF.execute({
      name: testProcessName,
      useCount: false,
    });

    const processMasterPage = new ProcessMasterPage(page);
    await processMasterPage.goto();

    // Search for the test process
    await processMasterPage.searchByKeyword(testProcessName);
    await utils.sleep(500);

    // Toggle use count
    await processMasterPage.toggleUseCount(0);
    await expect(page.getByText('Saved')).toBeVisible();
  });

  test('Toggle active state', async () => {
    // Create a test process with active state enabled
    const createProcessF = new CreateProcessFacade(page);
    const uniqueId = Date.now().toString();
    const testProcessName = `ActiveTest_${uniqueId}`;
    await createProcessF.execute({
      name: testProcessName,
      active: true,
    });

    const processMasterPage = new ProcessMasterPage(page);
    await processMasterPage.goto();

    // Search for the test process
    await processMasterPage.searchByKeyword(testProcessName);
    await utils.sleep(500);

    // Toggle active state
    await processMasterPage.toggleActiveState(0);
    await utils.sleep(500);

    // Verify the active state was toggled
    // Re-get the process row after the toggle to ensure fresh element reference
    let processRow = await processMasterPage.getProcessRowByName(testProcessName);
    await expect(processRow.locator('td').nth(6)).toContainText('Inactive');

    // Toggle it back to active
    await processMasterPage.toggleActiveState(0);
    await utils.sleep(500);

    // Re-get the process row again after the second toggle
    processRow = await processMasterPage.getProcessRowByName(testProcessName);
    await expect(processRow.locator('td').nth(6)).toContainText('Active');
  });

  test('Delete processes', async () => {
    // Create a test process to delete
    const createProcessF = new CreateProcessFacade(page);
    const uniqueId = Date.now().toString();
    const testProcessName = `DeleteTest_${uniqueId}`;
    await createProcessF.execute({ name: testProcessName });

    const processMasterPage = new ProcessMasterPage(page);
    await processMasterPage.goto();

    // Search for the test process
    await processMasterPage.searchByKeyword(testProcessName);
    await utils.sleep(500);

    // Check the process and delete it
    await processMasterPage.checkProcessByName(testProcessName);
    await processMasterPage.clickDeleteSelected();

    // Confirm deletion in the modal
    await processMasterPage.confirmDelete();

    // Verify the process was deleted
    await processMasterPage.searchByKeyword(testProcessName);
    await utils.sleep(500);
    const processRows = await processMasterPage.getProcessRows();

    // Either no rows or the row doesn't contain our test process name
    if ((await processRows.count()) > 0) {
      await expect(processRows.first()).not.toContainText(testProcessName);
    } else {
      await expect(processRows).toHaveCount(0);
    }
  });
});
