import { expect, test } from '@playwright/test';

import { SetupEnvironmentFacade } from '@/../playwright/shared/facades/SetupEnvironmentFacade';
import { ProcessMasterPage } from '@/../playwright/shared/page-object-models/ProcessMasterPage';
import * as utils from '@/../playwright/shared/utils';

test.describe('Process Master Export and Import', () => {
  test('Export processes', async ({ browser }) => {
    const setupEnvironmentF = new SetupEnvironmentFacade(browser);
    const { page } = await setupEnvironmentF.execute();

    const processMasterPage = new ProcessMasterPage(page);
    await processMasterPage.goto();

    // Verify the Export All button is present
    const exportButton = page.getByRole('button', { name: 'Export All' });
    await expect(exportButton).toBeVisible();

    // Set up download handling before clicking
    const downloadPromise = page.waitForEvent('download');

    // Click the Export All button to open the menu
    await exportButton.click();
    await utils.sleep(1000);

    // Verify the export format menu appears
    const exportMenu = page.locator('.dropdown-menu');
    await expect(exportMenu).toBeVisible();

    // Find and click the UTF-8(Mac) option to trigger download
    const macOption = exportMenu.getByText('UTF-8(Mac)');
    await expect(macOption).toBeVisible();
    await macOption.click();

    // Wait for the download to start
    const download = await downloadPromise;

    // Verify the download was initiated successfully
    expect(download.suggestedFilename()).toMatch(/\.csv$/);

    // The download is triggered successfully - test passes
    console.log(`Download initiated: ${download.suggestedFilename()}`);
  });

  test('Navigate to import page', async ({ browser }) => {
    const setupEnvironmentF = new SetupEnvironmentFacade(browser);
    const { page } = await setupEnvironmentF.execute();

    const processMasterPage = new ProcessMasterPage(page);
    await processMasterPage.goto();

    // Find the Import element
    const importElement = page.getByText('Import');
    await expect(importElement).toBeVisible();

    // Click the Import element to navigate to import page
    await importElement.click();

    // Wait for navigation to complete
    await utils.sleep(2000);

    // Verify we're on the import/file management page
    expect(page.url()).toContain('rm=file');
  });
});
