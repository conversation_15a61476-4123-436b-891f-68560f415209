import { expect, test } from '@playwright/test';

import { SetupEnvironmentFacade } from '@/../playwright/shared/facades/SetupEnvironmentFacade';
import { ProcessMasterPage } from '@/../playwright/shared/page-object-models/ProcessMasterPage';

test.describe('Process Master Navigation', () => {
  test('Click "Process" tag to go to process list page', async ({ browser }) => {
    const setupEnvironmentF = new SetupEnvironmentFacade(browser);
    const { page } = await setupEnvironmentF.execute();

    // Navigate to Process Master page
    const processMasterPage = new ProcessMasterPage(page);
    await processMasterPage.goto();

    // Verify we're on the Process Master page
    const url = page.url();
    expect(url).toContain('/pages/mastersettings/processes/');

    // Verify the Process tab is active
    const processTab = page.getByRole('tab', { name: 'Process', exact: true });
    await expect(processTab).toHaveAttribute('aria-selected', 'true');
  });
});
