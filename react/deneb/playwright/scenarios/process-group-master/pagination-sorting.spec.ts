import { expect, test, type Page } from '@playwright/test';

import { SimpleEnvironmentFacade } from '@/../playwright/shared/facades/SimpleEnvironmentFacade';
import { CreateProcessGroupFacade } from '@/../playwright/shared/facades/CreateProcessGroupFacade';
import { ProcessGroupMasterPage } from '@/../playwright/shared/page-object-models/ProcessGroupMasterPage';
import { LoginPage } from '@/../playwright/shared/page-object-models/LoginPage';
import * as utils from '@/../playwright/shared/utils';

test.describe('Process Group Master Pagination and Sorting', () => {
  let page: Page;
  let credentials: { email: string; password: string };

  test.beforeAll(async ({ browser }) => {
    test.setTimeout(180000);

    try {
      // Setup simplified environment
      const simpleEnvF = new SimpleEnvironmentFacade(browser);
      credentials = await simpleEnvF.execute();

      // Create a new page for testing
      page = await browser.newPage();

      // Login to the application
      const loginPage = new LoginPage(page);
      await loginPage.goto();
      await loginPage.loginWithEmail(credentials.email, credentials.password);
      await page.waitForTimeout(3000);

      // Create multiple test process groups to ensure we have enough data for pagination
      const createProcessGroupF = new CreateProcessGroupFacade(page);
      const baseTimestamp = Date.now();

      console.log('Creating test process groups for pagination tests...');
      for (let i = 1; i <= 25; i++) {
        const uniqueId = `${baseTimestamp}_${i.toString().padStart(2, '0')}`;
        const testProcessGroupName = `PaginationTest_${uniqueId}`;

        try {
          await createProcessGroupF.execute({
            name: testProcessGroupName,
            managementCode: `PT_${uniqueId}`,
            active: true,
          });
          console.log(`Created process group ${i}/25: ${testProcessGroupName}`);
        } catch (error: any) {
          console.log(`Failed to create process group ${i}: ${error.message}`);
          // Continue with other process groups
        }
      }
      console.log('Test process groups creation completed.');
    } catch (error: any) {
      console.log(`Error in beforeAll hook: ${error.message}`);
    }
  });

  test.beforeEach(async () => {
    try {
      test.setTimeout(60000);

      const processGroupMasterPage = new ProcessGroupMasterPage(page);
      await processGroupMasterPage.goto();
      await utils.sleep(1000);
    } catch (error: any) {
      console.log(`Error in beforeEach hook: ${error.message}`);
    }
  });

  test('Navigate to next page', async () => {
    test.setTimeout(60000);

    try {
      const processGroupMasterPage = new ProcessGroupMasterPage(page);

      if (page.isClosed()) {
        test.skip(true, 'Page is closed, skipping test');
        return;
      }

      await processGroupMasterPage.goto();
      await utils.sleep(1000);

      // Check if we have enough data for pagination
      const processGroupRows = await processGroupMasterPage.getProcessGroupRows();
      const rowCount = await processGroupRows.count();

      if (rowCount < 20) {
        test.skip(true, 'Not enough process groups for pagination test');
        return;
      }

      try {
        await processGroupMasterPage.goToNextPage();

        // Verify we're on a different page by checking if the data changed
        const newRows = await processGroupMasterPage.getProcessGroupRows();
        const newRowCount = await newRows.count();
        expect(newRowCount).toBeGreaterThan(0);
      } catch (error: any) {
        if (error.message.includes('Next page button is disabled')) {
          test.skip(true, 'Already on the last page, no next page available');
        } else {
          throw error;
        }
      }
    } catch (error: any) {
      console.log(`Error in Navigate to next page test: ${error.message}`);
      if (error.message.includes('Target page, context or browser has been closed')) {
        test.skip(true, 'Page was closed during test execution');
      } else {
        throw error;
      }
    }
  });

  test('Navigate to previous page', async () => {
    test.setTimeout(60000);

    try {
      const processGroupMasterPage = new ProcessGroupMasterPage(page);

      if (page.isClosed()) {
        test.skip(true, 'Page is closed, skipping test');
        return;
      }

      await processGroupMasterPage.goto();
      await utils.sleep(1000);

      // First go to next page if possible
      try {
        await processGroupMasterPage.goToNextPage();
        await utils.sleep(1000);

        // Now try to go back to previous page
        await processGroupMasterPage.goToPreviousPage();

        // Verify we successfully navigated
        const processGroupRows = await processGroupMasterPage.getProcessGroupRows();
        const rowCount = await processGroupRows.count();
        expect(rowCount).toBeGreaterThan(0);
      } catch (error: any) {
        if (error.message.includes('button is disabled')) {
          test.skip(true, 'Pagination not available or already on appropriate page');
        } else {
          throw error;
        }
      }
    } catch (error: any) {
      console.log(`Error in Navigate to previous page test: ${error.message}`);
      if (error.message.includes('Target page, context or browser has been closed')) {
        test.skip(true, 'Page was closed during test execution');
      } else {
        throw error;
      }
    }
  });

  test('Navigate to last page', async () => {
    test.setTimeout(60000);

    try {
      const processGroupMasterPage = new ProcessGroupMasterPage(page);

      if (page.isClosed()) {
        test.skip(true, 'Page is closed, skipping test');
        return;
      }

      await processGroupMasterPage.goto();
      await utils.sleep(1000);

      try {
        await processGroupMasterPage.goToLastPage();

        // Verify we're on the last page
        const processGroupRows = await processGroupMasterPage.getProcessGroupRows();
        const rowCount = await processGroupRows.count();
        expect(rowCount).toBeGreaterThan(0);
      } catch (error: any) {
        if (error.message.includes('Last page button is disabled')) {
          test.skip(true, 'Already on the last page');
        } else {
          throw error;
        }
      }
    } catch (error: any) {
      console.log(`Error in Navigate to last page test: ${error.message}`);
      if (error.message.includes('Target page, context or browser has been closed')) {
        test.skip(true, 'Page was closed during test execution');
      } else {
        throw error;
      }
    }
  });

  test('Navigate to first page', async () => {
    test.setTimeout(60000);

    try {
      const processGroupMasterPage = new ProcessGroupMasterPage(page);

      if (page.isClosed()) {
        test.skip(true, 'Page is closed, skipping test');
        return;
      }

      await processGroupMasterPage.goto();
      await utils.sleep(1000);

      // First try to go to last page, then go to first page
      try {
        await processGroupMasterPage.goToLastPage();
        await utils.sleep(1000);

        await processGroupMasterPage.goToFirstPage();

        // Verify we're on the first page
        const processGroupRows = await processGroupMasterPage.getProcessGroupRows();
        const rowCount = await processGroupRows.count();
        expect(rowCount).toBeGreaterThan(0);
      } catch (error: any) {
        if (error.message.includes('button is disabled')) {
          test.skip(true, 'Pagination not available or already on appropriate page');
        } else {
          throw error;
        }
      }
    } catch (error: any) {
      console.log(`Error in Navigate to first page test: ${error.message}`);
      if (error.message.includes('Target page, context or browser has been closed')) {
        test.skip(true, 'Page was closed during test execution');
      } else {
        throw error;
      }
    }
  });

  test('Change items count per page', async () => {
    test.setTimeout(60000);

    try {
      const processGroupMasterPage = new ProcessGroupMasterPage(page);

      if (page.isClosed()) {
        test.skip(true, 'Page is closed, skipping test');
        return;
      }

      await processGroupMasterPage.goto();
      await utils.sleep(1000);

      // Get initial row count
      const initialRows = await processGroupMasterPage.getProcessGroupRows();
      const initialCount = await initialRows.count();

      // Change to 50 items per page
      await processGroupMasterPage.changeItemsPerPage('50');
      await utils.sleep(2000);

      // Get new row count
      const newRows = await processGroupMasterPage.getProcessGroupRows();
      const newCount = await newRows.count();

      // Verify the change took effect (should show more items if available)
      expect(newCount).toBeGreaterThanOrEqual(initialCount);
    } catch (error: any) {
      console.log(`Error in Change items per page test: ${error.message}`);
      if (error.message.includes('Target page, context or browser has been closed')) {
        test.skip(true, 'Page was closed during test execution');
      } else {
        throw error;
      }
    }
  });

  test('Sort by management code', async () => {
    test.setTimeout(60000);

    try {
      const processGroupMasterPage = new ProcessGroupMasterPage(page);

      if (page.isClosed()) {
        test.skip(true, 'Page is closed, skipping test');
        return;
      }

      await processGroupMasterPage.goto();
      await utils.sleep(1000);

      // Get initial order
      const initialRows = await processGroupMasterPage.getProcessGroupRows();
      const initialCount = await initialRows.count();

      if (initialCount === 0) {
        test.skip(true, 'No process groups available for sorting test');
        return;
      }

      // Click sort by management code (first click for ASC)
      await processGroupMasterPage.sortByManagementCode();
      await utils.sleep(1000);

      // Verify sorting worked by checking that we still have the same number of rows
      const sortedRows = await processGroupMasterPage.getProcessGroupRows();
      const sortedCount = await sortedRows.count();
      expect(sortedCount).toBe(initialCount);

      // Click again for DESC sorting
      await processGroupMasterPage.sortByManagementCode();
      await utils.sleep(1000);

      // Verify DESC sorting worked
      const descSortedRows = await processGroupMasterPage.getProcessGroupRows();
      const descSortedCount = await descSortedRows.count();
      expect(descSortedCount).toBe(initialCount);
    } catch (error: any) {
      console.log(`Error in Sort by management code test: ${error.message}`);
      if (error.message.includes('Target page, context or browser has been closed')) {
        test.skip(true, 'Page was closed during test execution');
      } else {
        throw error;
      }
    }
  });
});
