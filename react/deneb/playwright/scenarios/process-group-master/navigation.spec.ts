import { expect, test } from '@playwright/test';

import { SimpleEnvironmentFacade } from '@/../playwright/shared/facades/SimpleEnvironmentFacade';
import { ProcessGroupMasterPage } from '@/../playwright/shared/page-object-models/ProcessGroupMasterPage';
import { LoginPage } from '@/../playwright/shared/page-object-models/LoginPage';

test.describe('Process Group Master Navigation', () => {
  test('Click "Process Group" tag to go to process group list page', async ({ browser }) => {
    // Setup simplified environment
    const simpleEnvF = new SimpleEnvironmentFacade(browser);
    const { email, password } = await simpleEnvF.execute();

    // Create a new page for testing
    const page = await browser.newPage();

    try {
      // Login to the application
      const loginPage = new LoginPage(page);
      await loginPage.goto();
      await loginPage.loginWithEmail(email, password);
      await page.waitForTimeout(3000);

      // Navigate to Process Group Master page
      const processGroupMasterPage = new ProcessGroupMasterPage(page);
      await processGroupMasterPage.goto();

      // Verify we're on the Process Group Master page
      const url = page.url();
      expect(url).toContain('/pages/mastersettings/processes/');

      // Verify the Process Group tab is active or visible
      // Try multiple possible tab names due to i18n
      const processGroupTab = page
        .getByRole('tab', { name: 'Process Group' })
        .or(page.getByRole('tab', { name: '工程グループ' })) // Japanese
        .or(page.getByRole('tab', { name: '进程组' })) // Chinese
        .or(page.getByRole('tab').filter({ hasText: 'Process Group' }))
        .or(page.getByRole('tab').filter({ hasText: 'Group' }));

      await expect(processGroupTab.first()).toBeVisible({ timeout: 10000 });
    } finally {
      await page.close();
    }
  });

  test('Click "Create" to go to the create process group page', async ({ browser }) => {
    // Setup simplified environment
    const simpleEnvF = new SimpleEnvironmentFacade(browser);
    const { email, password } = await simpleEnvF.execute();

    // Create a new page for testing
    const page = await browser.newPage();

    try {
      // Login to the application
      const loginPage = new LoginPage(page);
      await loginPage.goto();
      await loginPage.loginWithEmail(email, password);
      await page.waitForTimeout(3000);

      // Navigate to Process Group Master page
      const processGroupMasterPage = new ProcessGroupMasterPage(page);
      await processGroupMasterPage.goto();

      // Click the Create button
      await processGroupMasterPage.clickCreate();

      // Verify we're on the create process group page
      // The URL should change or we should see the create form
      const currentUrl = await processGroupMasterPage.getCurrentUrl();

      // Check if we're on a create page or if a create form is visible
      const isOnCreatePage =
        currentUrl.includes('/create') ||
        currentUrl.includes('/new') ||
        (await page.getByText('Create Process Group').isVisible()) ||
        (await page.getByText('Process Group Name').isVisible()) ||
        (await page.locator('input[name="name"]').isVisible());

      expect(isOnCreatePage).toBeTruthy();
    } finally {
      await page.close();
    }
  });
});
