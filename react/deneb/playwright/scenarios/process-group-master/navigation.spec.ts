import { expect, test } from '@playwright/test';

import { SetupEnvironmentFacade } from '@/../playwright/shared/facades/SetupEnvironmentFacade';
import { ProcessGroupMasterPage } from '@/../playwright/shared/page-object-models/ProcessGroupMasterPage';

test.describe('Process Group Master Navigation', () => {
  test('Click "Process Group" tag to go to process group list page', async ({ browser }) => {
    const setupEnvironmentF = new SetupEnvironmentFacade(browser);
    const { page } = await setupEnvironmentF.execute();

    // Navigate to Process Group Master page
    const processGroupMasterPage = new ProcessGroupMasterPage(page);
    await processGroupMasterPage.goto();

    // Verify we're on the Process Group Master page
    const url = page.url();
    expect(url).toContain('/pages/mastersettings/processes/');

    // Verify the Process Group tab is active
    const processGroupTab = page.getByRole('tab', { name: 'Process Group' });
    await expect(processGroupTab).toHaveAttribute('aria-selected', 'true');
  });

  test('Click "Create" to go to the create process group page', async ({ browser }) => {
    const setupEnvironmentF = new SetupEnvironmentFacade(browser);
    const { page } = await setupEnvironmentF.execute();

    // Navigate to Process Group Master page
    const processGroupMasterPage = new ProcessGroupMasterPage(page);
    await processGroupMasterPage.goto();

    // Click the Create button
    await processGroupMasterPage.clickCreate();

    // Verify we're on the create process group page
    // The URL should change or we should see the create form
    const currentUrl = await processGroupMasterPage.getCurrentUrl();
    
    // Check if we're on a create page or if a create form is visible
    const isOnCreatePage = currentUrl.includes('/create') || 
                          currentUrl.includes('/new') ||
                          await page.getByText('Create Process Group').isVisible() ||
                          await page.getByText('Process Group Name').isVisible();
    
    expect(isOnCreatePage).toBeTruthy();
  });
});
