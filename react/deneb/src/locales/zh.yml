word:
  account:                 帐户
  action:                  行动
  active:                  积极
  activity_log:            活动日志
  add:                     添加
  add_column:              添加列
  add_new_category:        添加新类别
  admin:                   管理
  admin_menu:              管理清单
  aggregate_axis:          方面
  ago:                     前
  all:                     全部
  all_download_zip:        所有数据下载(ZIP)
  allow:                   允许
  answer_questionnaire:    回答问卷
  any_period:              任何时期
  api_key:                 API KEY
  apply_availability:      是否适用
  apply_available:         适用的
  apply_unavailable:       不适用
  apply_unavailable_cause: 申请不适用原因
  apply_permission_setting: 是否适用设置
  approval:                赞同
  approved:                已批准
  approve_pending:         等待批准
  approve_unapplied:       不适用
  asc:                     升序
  assign:                  指定
  assignee:                负责人
  assigned_department:     指定部门
  assign_and_budget:       指派和估算
  attendance:              考勤
  attendance_and_pc_logs:  考勤和电脑日志
  attendance_end:          考勤下班
  attendance_set_to_worktime: 根据工时调整
  attendance_menu:         考勤清单
  attendance_start:        上班
  attendance_start_end:    出勤
  attendance_time:         工作时间
  attendance_total:        工作总量
  attendance_work_unmatch: 工作与出勤不匹配
  authorization_settings:  授权设置
  auto_sync:               自动同步
  basic_setting:           基本设置
  back_to_top:             返回首页
  back_to_previous_page:   返回前页
  bar_chart:               条状图
  batch_insert:            批量注册
  before_start:            开始前
  belong:                  公司所属
  beta_version:            測試版
  budget:                  预算
  budget_digestion:        预算消化
  business:                业务
  business_master:         主要业务
  calendar:                日历
  calendar_view:           日历视图
  cancel:                  取消
  cases:                   例
  cash:                    现金
  cash_admin:              现金输入
  chart_type:              图表类型
  category_name:           分类名称
  category_management_code: 品类管理代码
  change_password:         密码变更
  change_settings:         设置变更
  clone:                   克隆
  close:                   关闭
  closing_admin:           管理截止
  closing_day:             截止日
  create:                  创造
  check_all:               全选
  check_details:           查看详情
  change:                  改变
  code:                    代码
  comment:                 备注
  company_key:             登录 URL 中的公司名称
  company_settings:        基本设置
  completed:               完成
  confirm_delete:          确认删除
  connect:                 结盟
  connected:               链接
  connected_service:       联动服务
  copied:                  复制
  copy:                    复制
  contact_us:              询问
  cost:                    成本
  cost_management:         成本管理
  custom_field:            自定义项目
  custom_report:           自定义报告
  customer:                客户
  daily:                   每天
  daily_report:            每日报告
  daily_work:              每日工时
  dashboard:               工时并预算（以前的工時儀表板）
  data_manage:             数据管理
  date:                    日期
  day:                     日
  days:                    天
  day_of_week:             星期几
  day_of_week_short:       星期几
  day_unit:                日
  deadline:                期限
  default:                 默认
  delay:                   延迟
  delay_status:            延迟状态
  delayed_tasks:           延迟任务
  delete:                  删除
  department_master:       主要部门
  desc:                    降序
  dependency:              依赖
  difference2:             偏离
  difference_reason:       偏离原因
  difference_reason_class: 请选择偏离原因
  difference_reason_memo:  备注
  difference_setting:      偏离设置
  differences_admin_self:  只看自己
  discard:                 丢弃
  notif_destination:       通知目标
  department:              部门
  disconnect:              电梯
  disconnect_integration:  断线整合
  department_name:         部门名
  display:                 显示
  display_by_hierarchy:    按层次显示
  display_order:           显示顺序
  display_settings:        显示设置
  display_period:          显示期间
  download:                下载
  duplicate:               复制
  edit:                    编辑
  edit_category:           编辑类别
  edit_budget:             修改预算
  edit_notification:       编辑通知
  end:                     结尾
  end_date:                结束日期
  end_date_shorten:        结束日期
  end_of_month:            月末
  end_time:                结束时间
  email:                   邮箱
  employ_type:             雇用类型
  employ_type_master:      主要雇用类型
  employment_status:       入学状况
  example:                 例
  excel:                   Excel
  expand:                  传播
  expense:                 费用
  expense_management:      费用管理
  export:                  导出
  export_all:              导出全部
  export_type:             导出种类
  id:                      ID
  integer:                 整数
  integration:             外部联动
  integration_name:        整合名称
  integrations_settings:   外部联动
  integrations_services:   外部合作服务
  integrated_attendance:   外部考勤联动
  failure:                 失败
  favorite:                最喜欢的
  feature:                 特征
  filter:                  过滤
  filter_search:           精细搜索
  gantt:                   甘特图
  ganttchart:              甘特图
  general:                 一般的
  generate_summary:        生成摘要
  greater_than:            超过
  google_extension_ver:    Google Chrome版
  gross_profit:            销售总利润
  help:                    帮助
  highlight:               突出
  holiday_master:          主要节日
  holiday_type:            假期类型
  home:                    首页
  hour:                    时
  hour_or_less:            小时或更少
  time_unit:               h
  try_it:                  试一试
  hours:                   小时
  history:                 歷史
  in_progress:             进行中
  input_attendance:        输入出勤率
  input_rate:              输入率
  input_work:              输入工作
  difference:              差分
  import:                  进口
  items_to_be_exported:    要输出的项目
  ja_extended:             日本语 (EUC-JP)
  ja_shift:                日本语 (Shift-JIS)
  job_popup:               工作弹出窗口
  kanban:                  看板
  key:                     钥匙
  keyword:                 关键字
  last_day:                末尾日
  last_updatetime:         最后输入日期和时间
  later:                   后
  laern_more:              了解更多
  less_than:               未满
  level_n:                 第{{level}}级
  list:                    列表
  logout:                  注销
  lower_row:               下排
  ongoing:                 正在进行中
  other:                   其他
  other_functions:         其他有用的功能
  owner:                   所有者
  owner_code:              所有者代码
  mail:                    邮件
  man_day:                 每人每日
  man_hour:                每人每时
  man_minute:              每人每分
  man_month:               每人每月
  manage:                  管理
  manage_api:              管理API
  management_code:         管理代码
  management_code_shorten: 管理代码
  management_code_update:  管理代码更新
  master_setting:          主要设置
  match:                   比赛
  member:                  成员
  member_summary:          成员概要
  member_department:       会员部门
  member_fields:           会员物品
  memo:                    备忘录
  menu:                    菜单
  microsoft_extension_ver: Microsoft Edge版
  milestone:               &milestone 里程碑
  milestone_add:           添加里程碑
  milestone_name:          里程碑名称
  milestone_edit:          编辑里程碑
  minute:                  分钟
  minute_unit:             m
  month:                   月
  month_unit:              个月
  monthly:                 每月
  ms_teams:                Microsoft Teams
  multi_gantt:             多个甘特图
  multi_pie_chart:         多饼形图
  my_attendance_export:    我的出勤导出
  my_pattern:              我的模式
  my_report:               我的汇报
  my_timesheet_export:     我的时间表汇报导出
  my_timesheet_report:     我的时间表汇报
  my_work_report:          我的工作报表汇报
  name:                    名
  member_name:             成员名字
  narrow_down:             收窄
  normal:                  普通的
  notif_content:           通知内容
  new:                     新
  new_notification:        新通知
  new_ganttchart:          新甘特图
  new_process_group:       工序组
  no_category:             无类别
  no_operation:            无操作
  not_active:              不活跃
  not_allow:               不准
  not_displayed:           不显示
  not_now:                 现在不要
  not_selected:            未选择
  not_sorted:              无排序
  notice:                  注意
  not_use:                 不使用
  notice_admin:            通知管理
  notifications:           通知
  notified_events:         通知事件
  notification_name:       通知名称
  notification_type:       通知类型
  notification_delete:     删除通知
  notification_settings:   通知设置
  notification_subject:    主题
  number:                  数字
  numbers_of_per_page:     每页的数量
  ok:                      决定
  old:                     旧
  on_time:                 无延迟
  overdue:                 延迟
  participants:            参与者
  pc_log:                  电脑日志
  pc_management:           电脑管理
  pc_management_top:       电脑管理TOP
  performer:               从业者
  period:                  周期
  permission_master:       授权主管
  personal_report:         我的工时汇报
  personal_setting:        个人设置
  personal_work:           导出我的工时
  pie_chart:               饼形图
  predecessor:             前任
  process:                 工序
  process_1:               工序1
  process_2:               工序2
  process_3:               工序3
  process_category:        进程类别
  process_delete:          删除进程
  process_group_delete:    删除进程组
  process_category_delete: 删除类别
  process_group:           工序组
  process_group_name:      工序组名称
  processes_included:      包含的流程
  process_name:            进程名称
  process_master:          主要进程
  profit:                  盈亏
  profit_and_asset:        盈亏与资产
  profit_by_customer:      特定客户盈亏
  profit_rate:             盈亏率
  progress:                进度
  progress2:               进度
  progress_rate:           进步
  progress_status:         进步
  project:                 项目
  project_business:        项目业务
  project_customer:        项目客户
  project_department:      项目部门
  project_fields:          项目项目
  project_field_master:    主要项目行
  project_list:            项目清单
  project_index:           项目一览
  project_basic_info:      项目的基本信息
  project_code:            项目代码
  project_name:            项目名称
  project_owner:           项目责任人
  project_slash_task:      项目 / 任务
  project_summary:         项目概要
  project_work:            项目工时
  project_field:           项目行
  project_type:            项目类型
  project_type_in_use:     正在使用
  project_management:      项目管理
  ratio:                   比
  register:                登记
  registered:              已登记
  rejected:                被拒绝
  rejected2:               拒绝
  rejected_comment:        被拒绝的评论
  related_functions:       相关功能
  report:                  汇报
  report_history:          报表历史记录
  remind_notif:            提醒通知
  report_name:             报告名称
  report_top:              汇报首页
  required_time:           所需的时间
  reset:                   初始化
  rest:                    休息
  rest_allday:             全天休息
  rest_am:                 上午休息
  rest_pm:                 下午休息
  result:                  实绩
  sales:                   销售
  sales_management:        销售预测和控制
  sales_status_master:     主要销售状态
  save:                    保存
  save_as:                 另存为
  save_duration:           保存期间
  save_or_export:          保存或导出
  select_department:       选择部门
  select_process:          选择 {{processName}}
  select_project:          选择项目
  select_time:             选择时间
  search:                  搜索
  search_condition:        搜寻条件
  secret_access_key:       秘密访问密钥
  send_immediately:        立即发送
  send_test:               发送测试
  settings:                设置
  show_multiple_pie_chart: 显示多饼形图
  show_only_members_with_differences: 只显示有差异的成员
  show_ruled_lines:        显示纵轴
  slack:                   Slack
  subtotal:                小计
  success:                 成功
  success_with_warning:    成功（有通知）
  sum_line_chart:          累积转移图
  stacked_bar_chart:       堆积条形图
  combo_chart:             组合图表
  sort:                    种类
  start:                   开始
  start_date:              开始日期
  start_date_shorten:      开始日期
  start_time:              开始时间
  state:                   条件
  status:                  状态
  stop_watch:              秒表
  stop_watch_start:        开始
  stop_watch_stop:         停止
  stop_watch_apply_to_tst: 计入时间表
  subscription_on:         订阅开启
  subscription_off:        订阅关闭
  successor:               接班人
  summary_of_the_report_results: 报告结果摘要
  switch_horizontal_axis:  切换水平轴
  sync:                    同步
  sync_end_datetime:       同步结束日期和时间
  sync_start_datetime:     同步开始日期和时间
  sync_attendance:         同步考勤
  sync_status:             同步状态
  sync_external_services:  同步对外合作服务
  sync_duration:           同步持续时间
  sync_with_calendar:      与日历同步
  target_period:           目标期
  task:                    &task 任务
  task_add:                &task_add 添加任务
  task_create:             创建任务
  task_edit:               编辑任务
  task_fields:             任务项目
  task_group:              &task_group 任务组
  task_group_add:          &task_group_add 添加任务组
  task_group_edit:         编辑任务组
  task_group_name:         任务组名称
  task_list:               任务列表
  task_name:               任务名称
  task_process:            任务流程
  task_process_group:      任务进程组
  this_month:              这个月
  time:                    时间
  time_required:           所需时间
  timesheet:               时间表
  timesheet_approval:      时间表批准
  timesheet_approval_flow: 批准人
  timesheet_edit_dialog:   时间表编辑对话框
  timesheet_report:        时间表报表
  timesheet_report_notif:  时间表报表通知
  timesheet_to_be_notified: 通知时间表
  timesheet_usage:         时间表使用状态
  timesheet_usage_report:  时间表使用情况报告
  title_master:            主要职务
  base_date:               基准日期
  title:                   职务
  today:                   今天
  todo:                    尚未开始
  total:
      type1:               合计
      type2:               计
  total_items:             共{{quantity}}项
  total_unit:              总计单位
  total_bar_chart:         总条形图
  type:                    种类
  unapply:                 取消时间表申请
  unicode:                 Unicode
  unit:                    单元
  unregistered:            未登记
  update:                  更新
  upper_row:               上排
  upgrade_now:             现在升级
  url:                     URL
  use:                     使用
  use_count:               使用计数
  user:                    成员
  user_contract:           主要雇用类型
  user_code:               会员代码
  user_customer:           客户
  user_department:         会员部门
  user_title:              职务
  unmatch:                 无与伦比
  webhook:                 Webhook
  week:                    周
  weekly_timesheet_report: 每周时间表报告
  work:                    工时
  work_add:                加倍努力
  work_and_budget:         工时/预算
  work_and_budget_prev:    以前的工時儀表板
  work_attendance_match:   考勤匹配
  work_budget_actual:      工时并预算
  work_cost:               工时成本
  work_content:            工作内容
  work_output_unit:        工时输出单元
  work_management:         工作预算管理
  work_budget:             工作预算
  work_report:             工作报表
  work_report_save:        保存工作报表
  work_report_sync_date:   工作报表更新于
  work_report_sync:        更新工作报表
  work_time:               工时
  work_total:              合计
  work_in_total:           工时总计
  worktime_input_omission_remind_notif: 工时输入遗漏提醒通知
  working:                 在职的
  working_hours:           工作时间
  year:                    年
  zh_simplified:           简体中文
  whole_day_off:           全天休息
  morning_day_off:         早上放假
  afternoon_day_off:       下午休息日
  not_specified_day_off: 沒有任何
common:
  service_name: CrowdLog
msg:
  add_dimension_locked: 您可以从第二排开始使用高级计划
  add_external_integration_service: 添加外部集成服务
  assigned_user_is_invalid: 已设置无效用户。
  atleast_one: "{{this}}或{{that}}"
  attendance_differences:
    difference_between_start_and_end_is_focus: 开始和结束时间之间的差异是重点。
    attendance_or_pclog_is_not_collected1: 考勤或PCLog的状态尚未确定
    attendance_or_pclog_is_not_collected2: 考勤：请输入退勤时间
    attendance_or_pclog_is_not_collected3: PCLog：请登录您的电脑
  browser_extensions_convenience: 浏览器扩展让您更方便！
  chart:
    error:
      no_data: 生成图表时出错。 没有数据。
  check_all: 选择所有
  click_generate_summary: "点击「{{button}}」以总结工作报告。"
  click_generate_summary_to_regenerate: '一些参数已经更新。点击「{{button}}」生成工作报告的新摘要。'
  copied_data: "复制<0>{{data}}</0>"
  create_notification: "点击{{button}}以创建新的通知。"
  custom_report:
    fetch_failed: 未能获取自定义报告。
    restore_failed: 由于选择了一个不存在的自定义报告，恢复失败。
    change_settings_success: 自定义报告设置已成功更新。
    change_settings_failed: 未能更新自定义报告设置。
    update_success: 自定义报告已成功更新。
    update_failed: 未能更新自定义报告。
    save_success: 自定义报告已成功保存。
    save_failed: 未能保存自定义报告。
    not_save: 目前不提供
    delete_success: 自定义报告已成功删除。
    delete_failed: 无法删除自定义报告。
    report_limit: 可保存的自定义报告数量最多为 {{max}} 个报告。
    fetch_failed_fav: 未能获取收藏报告。
    save_success_fav: 已成功添加到收藏夹。
    save_failed_fav: 添加收藏夹失败。
    delete_success_fav: 收藏报告已成功删除。
    delete_failed_fav: 无法删除收藏报告。
    already_marked: 自定义报告已被标记为收藏。
    sort_custom_success: 您已成功对自定义报告进行排序。
    sort_custom_failed: 未能对自定义报告进行排序。
    sort_favorite_success: 收藏报告已成功排序。
    sort_favorite_failed: 未能对收藏的报告进行排序。
    example_case: 的情况下
    scope_of_aggregation: "统计范围："
    example_december: "12 月"
    example_january: "1 月"
    monthly_available_only: 只适用于月次
  delete_selected_notifications: 删除选定的通知
  default_api_error:   发生服务器错误。
  delete_confirm:      确定要删除吗?
  delete_confirm_with_name_and_date: 您想删除{{name}}的{{date}}数据吗？
  delete_confirmation_for_report: 删除已保存的报告 “{{data}}”？
  delete_process_category_confirm:
    line1: 您想删除“{{data}}”类别吗？
    line2: 删除后，关联流程的类别将变为“{{empty}}”。
  delete_process_confirm: 您想删除{{userName}}的{{date}}数据吗？
  deleted:             已删除。
  deleted_data:        "删除的<0>{{data}}</0>"
  delete_failed:       删除失败。
  detail_search:       按详细的标准进行细化
  display_work_budget: 展示工作预算
  display_work_budget_requirements:
    dimension:
      must_select_either: '会员相关项目或 {{dimension}}'
      must_not_selected: '{{dimension}} 未选择'
      must_not_selected_two: '{{dimension1}} 和 {{dimension2}} 未选择'
  do_not_reload_page:  请不要页面转换或重新加载。
  do_not_show_this_notice_again: 不再显示此公告
  end_project_process_budget: 非常抱歉，此功能计划于 2024年9月5日 星期四停止。有关更多信息，请参阅<helpLink>此处</helpLink>
  enter_integration_info: 输入整合信息。
  export:
    failed:            导出失败
    succeed:           导出完成
    exporting:         导出中
    not_authorized:    您无权导出
    limit_per_page:    可导出的记录数最多为 {{max}} 条。 仅导出前 {{max}} 条记录。
  failed_copy_data:    "复制<0>{{data}}</0>失败"
  failed_delete_data:  "删除<0>{{data}}</0>失败"
  failed_save_data:    "保存<0>{{data}}</0>失败"
  feature_not_available: 此功能尚不可用
  form:
    confirmation:
      discard_updates: 确定放弃更新吗？
  generate_summary_failed: 生成摘要失败。
  generate_summary_succeed: 摘要已生成。
  integrations_settings:
    auto_integ:
      form_title: 自动同步设置
      form_details:
        - 使用时，系统会在每天05:00自动同步所有受试者近40天的考勤信息。
        - ※您可以随时更改此设置。
      info: 联动后，系统每天05:00自动同步近40天的考勤信息。 您可以随时通过单击屏幕上的“设置”按钮来更改此设置。
      setup_finished:
        jobcan: 更新了ジョブカン设置 ({{settingName}})。
        kot: 更新了KING OF TIME设置 ({{settingName}})。
        kinkakuji: 更新了勤革時设置 ({{settingName}})。
        hrmos: 更新了HRMOS设置 ({{settingName}})。
      unable_to_update: 当前系统错误阻止更改此设置。 请稍后重试。
    connected: 与{{name}} ({{settingName}}) 合作了。
    connection_failed: 与{{name}}合作失败。
    connected_detail:
      jobcan: 考勤可以从管理菜单中的“考勤”屏幕同步。
      kot: 考勤可以从管理菜单中的“考勤”屏幕同步。
      kinkakuji: 考勤可以从管理菜单中的“考勤”屏幕同步。
      hrmos: 考勤可以从管理菜单中的“考勤”屏幕同步。
    desc:
      jobcan: 现在可以同步ジョブカン中注册的考勤，并查看同步执行历史列表。
      kot: 现在可以同步KING OF TIME中注册的考勤，并查看同步执行历史列表。
      kinkakuji: 现在可以同步勤革時中注册的考勤，并查看同步执行历史列表。
      hrmos: 现在可以同步HRMOS中注册的考勤，并查看同步执行历史列表。
    disconnected: 与{{name}}的我取消了合作。
    disconnection:
      jobcan:
        - 如果取消联动，将无法同步考勤。
        - 过去已同步的考勤不会改变。
      kot:
        - 如果取消联动，将无法同步考勤。
        - 过去已同步的考勤不会改变。
      kinkakuji:
        - 如果取消联动，将无法同步考勤。
        - 过去已同步的考勤不会改变。
      hrmos:
        - 如果取消联动，将无法同步考勤。
        - 过去已同步的考勤不会改变。
    disconnection_failed: 取消关联与{{name}}的失败。
    form_instruction: 下記の情報を入力し、{{name}}と連携してください。
    form_id_placeholder: 在 {{name}} 中设置的 ID
    form_secret_key_placeholder: '{{name}} 中设置的秘密访问密钥'
    limit_has_been_reached: 无法注册 {{sys}} 设置，因为已达到注册限制。 <br />
                            最多可以注册 {{num}} 个 {{sys}} 设置。
    unable_to_connect_jobcan: 链接失败。ID 或密钥无效。
    unable_to_connect_kot: 无法连接。无效的访问令牌。
    unable_to_connect_hrmos: 无法连接。URL 中的公司名称无效或 API KEY 无效。
  integration_attendance:
    summaries:
      failure: 由于系统错误，无法执行同步。 有关详细信息，请参阅<helplink>帮助页面</helplink>。
      running: 同步
      success_with_warning: 可能包含意外的同步结果。 有关详细信息，请参阅<helplink>帮助页面</helplink>。
    errors:
      cannot_convert_attendance: "出勤率无法转换"
      attendance_synchronized_by_another_condition_existed: "在其他条件下已经同步出席的情况也存在"
      integration_attendance_access_prohibit_error: "「.+」在「.+」之间不可用"
  following_projects_cannot_be_deleted: 以下项目不能被删除，因为你没有被授予项目编辑权。
  following_projects_cannot_be_exported: 以下项目不能被导出，因为他们没有必要的权限。
  force_delete_keyword_mismatch: 由于你输入的确认字符串不正确，删除被取消了。
  force_delete_project:
    introduction: 您要强行删除吗？
    prompt_confirmation: "删除后，信息将无法恢复。 检查以下项目并执行强制删除。"
    warnings:
      - 所选项目的注册销售将被删除。
      - 所选项目的注册成本将被删除。
      - 所选项目的注册费用预算将被删除。
      - 所选项目的注册费用结果将被删除。
      - 甘特图中所选项目的注册任务将被删除。
      - 所选项目的注册时间表（工时数据）将被删除。
      - 所选项目的注册工作预算将被删除。
      - 所选项目的已注册工作成本结果将被删除。
    if_not_want_to_delete_data: 如果您不想删除，请停用项目。
    requirement: 要删除项目，客户必须是系统管理员或在“权限主”中具有“角色权限”。
    prompt_input_delete: "输入 'DELETE' 然后确认"
  update_external_integration_service: 更新外部集成服务
  integrated_attendance:
    now_integrated_with: "考勤信息正与{{targetSystem}}保持数据协同。"
    input_manually: "手动输入"
    attendance_tips: "手动输入的考勤信息，只要不进行审批申请，就会在执行与{{targetSystem}}的数据协同时被更新。"
    attendance_diff_exists: "与协同获取的考勤信息存在差异"
    jobcan: "jobcan"
    kot: "KING OF TIME"
    kinkakuji: "Kinkakuji"
    hrmos: "HRMOS"
    integration_attendance_failed: "无法启动考勤同步。"
    integration_attendance_is_submitted: "考勤的同步化已经开始。 在这个过程完成之前，不能进行下一次的考勤同步。"
  mark_as_favorite: 标记为收藏
  move_days_direction: "{{days}}{{unit}}{{direction}}搬家"
  multi_pie_chart:
    display_condition: 多饼形图要求
  outlook_extension:
    calendar_sync:
      no_sync_code: This event cannot be synchronized, because the management code is not set. ({{data}})
  project_delete:
    failed:            有一个项目未能被删除
    succeed:           已成功删除
  projects_that_could_not_be_deleted: 无法删除的项目
  projects_that_could_not_be_exported: 无法导出的项目
  download:
    failed:    下载失败。请过一会儿再执行。
    preparing: 准备下载
  filter_start_end_date_any_period: 按任何时间段过滤开始/结束日期
  new_feature_released: 新功能已发布
  no_available_items: 没有可用的 {{name}}
  no_matches_found: 找不到匹配项
  no_matching_results_found: 没有发现匹配的搜索结果
  no_member_assigned: 未分配任何成员
  no_options: 无选择
  no_options_business: 请输入企业名称
  no_options_customer: 请输入类别名称
  no_options_department: 请输入部门名称
  no_options_obj: "请输入{{obj}}名称"
  no_options_project: 请输入项目名称
  no_options_process: 请输入进程名称
  no_options_task_status:  ※请选择一个项目
  no_permission:
    header: 您没有权限访问此页面。
    line1:  必须在权限主机中设置权限。
    line2:  进一步了解 %title_key。
  not_allowed_to_view_data:    "不允许查看数据"
  not_found:
    header: Not Found
    line1:  找不到页面。
    line2:  请确保输入的URL正确。
  pj_timeline_advertising_title: 项目预测和管理<br />压倒性的简单和方便。
  pj_timeline_advertising_items:
    - 了解整个项目的预算和绩效
    - 预算和实际执行情况随时间变化
    - 对项目开始和结束的时间以及发生工时的直观理解
  plan:
    integration_attendance_title: 具有外部考勤联动功能<br />更精准的工时管理！
    integration_attendance_details:
      - 通过与正在使用的考勤管理系统连接，将考勤信息与云日志同步
      - 根据实际工时更精准的工时管理
  process:
    success_save: 已保存。
    no_data: 进程可以从 "新建"中创建。
    delete_modal_message: 您想删除进程吗？<br />进程一旦被删除，就无法恢复。
    success_delete: 进程已成功删除。
    category_success_delete: 进程类别已被删除。
    failed_delete: 无法删除进程，因为主进程正在使用中。
    failed_update_active: 您不能禁用操作组中使用的操作。
    new_process_name: 新进程名称
    table_update_refresh: 一些表行已更新。 请重新加载页面。
  process_group:
    no_data: 可以从 “新建” 菜单创建流程组
    delete_modal_message: 是否要删除进程组？<br />进程组一旦删除就无法恢复。
    success_delete: 进程组删除成功。
    failed_delete: 无法删除进程组，因为主进程正在使用中。
    add_process: 添加{{process}}
    form_search: 按进程名称或类别名称搜索
  prepend_api_error:     出了些问题。
  process_not_active: 进程处于非活动状态。
  process_not_assigned_to_project: 该流程未分配给该项目。
  remaining_number_of_generation: "本月剩余发电量: {{remainingUsage}} / {{limit}}"
  remaining_number_of_generation_description: 代数计入公司内部总数
  reset_to_default:      重置为默认设置
  restrictions:
    lets_upgrade: 升级您的计划并享受功能！
    plan_restricted: 您当前的计划中不提供此功能。
  save_data:             保存{{data}}
  saved_data:            "你已经拯救了<0>{{data}}</0>。"
  saved:                 保存成功
  save_confirm:          确定要保存吗?
  save_failed:           保存失败。
  saving:                保存
  select_date: 选择日期
  select_display_settings: 选择你想在列表中显示的项目。
  select_end_date: 选择结束日期
  select_integration_service: 选择要链接的服务。
  select_one_or_more: 请选择一项或多项。
  select_start_date: 选择开始日期
  send_remind_result_to_title_user: 向职位负责人发送摘要通知
  send_remind_result_to_title_user_desc: 摘要通知允许您查看哪些成员收到了提醒通知。 <br />此通知仅包含有关您部门成员的信息。
  send_immediately_in_bulk: 立即批量发送
  success_send_immediately: 成功发送立即。
  failure_send_immediately: 发送立即失败。
  success_send_immediately_in_bulk: 成功发送立即批量。
  failure_send_immediately_in_bulk: 发送立即批量失败。
  show_assigned_project: 只显示你被分配到的项目
  show_only_active_members: 仅显示当前成员
  show_only_in_progress_projects: 仅显示活跃项目
  summarize_n_or_more_data_as_other: 将 {{dataLimit}} 个或更多数据汇总为其他数据
  combo_chart_title: "{{dimension2nd}}占{{dimension1st}}的百分比"
  combo_chart_search_condition_mismatch: "请在搜索表中为 {{dimension2nd}} 和 {{dimension1st}} 各选择一个项目"
  combo_chart_dimensions_condition_mismatch: 请选择两个汇总轴
  combo_chart_condition_mismatch: 抱歉，当前数据无法显示图表
  guide_to_help_pages: 欲了解更多信息
  switch_to_old_design:  切换到旧设计
  synchronizing_attendance:
  synchronized_attendance:
  sync_attendance:
    success: 同步考勤。
    failed: 同步考勤失败。
    synchronizing: 正在进行同步
    synchronizing_detail:
      - 时间同步正在进行中。
      - 在该过程完成之前，无法执行下一次同步运行。
      - 请稍等片刻，直到该过程完成。
  unmark_as_favorite: 取消标记为收藏
  updated_data:            "<0>{{data}}</0>已被更新。"
  notification_send_test:
    success:    测试消息已成功发送。
    in_progress: 发送测试信息。
    failed:     测试信息发送失败。
  or_confirm_our_plan: 或者，<0>查看计划</0>
  out_of_project_period: 在项目期之外。
  out_of_user_period: 在成员的工作时间之外。
  timesheet:
    input_daily_report: 输入每日报告
    add_memo: 添加注释
    apply_failed: 无法申请时间表
    apply_message: 申请以下时间表
    apply_success: 时间表申请成功
    apply_cancel_success: 时间表申请取消成功
    apply_cancel_fail: 时间表申请取消失败
    apply_warning: 有无法申请的时间表
    apply_this_date: 您想申请该日期的时间表吗？
    unapply_this_date: 您想取消该日期的时间表申请吗？
    calendar_sync_not_determined: 日历同步尚未确定
    delete_memo: 删除备忘录
    executing_fill_available_time: 执行填充可用时间
    save_success: 时间表已成功保存
    save_failed: 未能删除时间表
    delete_success: 时间表已成功删除
    delete_failed: 未能删除时间表
    no_permission: 您无权查看此用户的时间表
    cannot_used_work: 无效的{{work}}已包括
    is_approved: 这是批准的时间表，不能进行编辑
    is_pending: 由于正在申请时间表，因此无法进行编辑
    is_unmatch: 有一张时间表，其中出勤和工时不匹配
    calendar_sync:
      success: 成功地同步了日历
      failed: 日历同步失败
      no_events: 没有要同步的事件
      synchronizing: 同步日历
      has_temp_work: 日历同步尚未确定
      has_temp_work_body: 有一些事件没有输入日历同步代码。 请输入项目和流程或任务。
      select_account: 与另一个账户同步
      no_sync_code: 尚未设置管理代码 ({{data}})
    copy: 拷贝
    copy_from: 拷贝源
    copy_to: 拷贝至
    copy_to_date: 目的地日期
    copy_not_executed: 拷贝未执行
    copy_no_more_date: 无法添加更多日期
    copy_completed: 拷贝已完成
    copy_date_select: 请选择要拷贝至哪个日期
    copy_add_date: 添加一个要拷贝到的日期
    copy_overwrite_attendance: 通过副本覆盖休息日、出勤和休息
    process_not_selected: 未选择进程
    try_new_timesheet_now: 立即尝试新的时间表
    questionnaire:
      button_label_1: 单击此处查询
      button_label_2: 有关新时间表的信息
    stop_watch:
      sw_add_new: 添加秒表
      sw_status_conflict: 页面状态过旧。请刷新页面。
      sw_select_work_and_apply: 无法将计时时长计入时间表。请设定工作内容并点击计入时间表按钮。
      sw_correct_memo_and_apply: 请将注释的字数设为200字以内，并点击计入时间表按钮。
      sw_apply_to_tst_successfully: 工时计入时间表成功
      sw_need_to_reset_manually: 工时计入时间表成功。要开始下一次计时，请单击重置按钮。
      sw_cannot_start_timing: 。无法开始秒表计时。
      sw_fail_to_add_new: 添加秒表失败。
      sw_limit_max_stop_watch_count: "秒表最多能添加到{{num}}个。"
      sw_exists_another_with_timing: 有其他秒表正在计时。请刷新页面。
    shortcut_guide:
      title: 快捷键指南
      change_date_to_today: 将日期更改为今天
      change_to_date_view: 更改为日期视图
      change_to_week_view: 更改为周视图
      change_to_month_view: 更改为月视图
      show_next_time_range: 显示下一个时间范围
      show_previous_time_range: 显示之前的时间范围
      change_to_calendar_view: 更改为日历视图
      change_to_list_view: 更改为列表视图
      zoom_in_or_out_time_slot: 放大或缩小时间段
    timekeeper_integration:
      register_message: 你想要登记这个工作项目吗？
      resgister: 登记
      not_register: 不登记
    use_work_suggestion: 使用工时建议功能
  timesheet_detail_fail: 未能获取时间表详细信息
  there_are_data_without_permission: 无权限，有<0>{{data}}</0>可查
  this_feature_is_available_in_premium: 该功能在高级计划中可用
  user_not_assigned_to_project: 用户没有被分配到这个项目。
  unapproved_attendance: 有不明打卡记录。
  unapproved_timesheet:  有未经证实的时间表审批。
  upgrade_premium_plan: 只在高级计划中
  validation:
    at_least_one_checked: 至少必须检查一次。
    greater_than:         输入值大于 {{num}}。
    greater_than_or_equal: 输入值大于或等于 {{num}}。
    less_than_or_equal:   输入值小于或等于 {{num}}。
    input_obj:            请输入{{obj}}
    invalid_url:          无效的网址。
    invalid_input:        无效的输入值。
    invalid_start_end:    無效開始結束範圍
    invalid_work_memo:    备注长度必须为 200 个字符
    max_length:           "{{data}}长度不能超过{{length}}个字符。"
    must_be_between:      输入值必须在{{from}}到{{to}}范围内。
    out_of_range_time:    超出範圍的輸入時間
    invalid_work_time:    工作時間單位無效
    less_than_work_time_unit: 输入小于工作时间单位时间
    gte_date:             "请将{{end}}设置为不早于{{start}}的日期"
    required:             这是必填栏。
    starts_with:          它必须以“%s”开头。
  work_check_error: 检查人工预算，实际人工，实际人工成本和实际人工成本之一。
  work_budget:
    display_condition: 工作预算要求
    not_available: 不支持该图表
    cannot_edit_due_to_gantt: 使用甘特图时无法编辑
    change_setting_unit: 变更工时预算的设置单位
    confirm_change_to_member: 是否将工时预算的设置单位变更为「成员」？
  work_report_sync:
    for_details: 详细请点击
    real_time_report: 实时统计
    sync_rules_first: 工时报告展示提前统计好的数据
    sync_rules_second: 当按下"更新"按钮时，重新统计数据
    sync_is_in_progress: 更新正在进行中。请在更新完成后再试一次。
    sync_has_started: 更新已开始
    sync_failed_to_start: 更新处理失败
  workflow_timesheet_setting:
    can_be_added_by_clicking_member: 单击 "添加成员 "可添加排除在外的成员。
    success_add_excluded_user: 已添加到排除在外的成员。
    failed_add_excluded_user: 添加某些成员 ({{n}}) 失败。请重新设置添加排除的成员。
    success_delete_excluded_user: 从排除在外的成员中删除。
    failures_delete_excluded_user: 删除某些成员 ({{n}}) 失败。 请再次尝试删除排除在外的成员。
placeholder:
  all_belong: 所有附属机构
  all_departments: 所有部门
  all_employ_types: 所有雇佣类型
  all_titles: 所有标题
  all_members: 所有成员
  all_process: 所有流程
  all_process_categories: 所有流程类别
  attendance_difference_filter: 成员名字, 邮箱, 成员代码
  entity_used_in_name: "用于{{name}}的{{entity}}"
  identification_name: 区分名称
  input_comment: 填写评论
  input_milestone_name: 输入里程碑名称
  input_data: 输入{{data}}
  input_search_conditions: 输入搜索条件
  input_task_group_name: 输入任务组名称
  input_task_name: 任务名称输入
  member_filter: 代码、会员名称、电子邮件地址
  timesheet_usage_filter: 名，代码，邮箱
  process_filter: 进程名称、控制代码
  project_filter:   代码、项目名称
  notification_filter: 按通知名称搜索
  task_filter: 代码，任务名称
  timesheet_report_filter: 会员姓名、邮箱地址、会员代码
  excluded_modal_filter: 会员代码, 会员姓名、 邮箱地址
  sign_on_url: 输入登录 URL
  select_member: 选择成员
  select_task_group: 选择任务组
  select_task_dependency: 选择任务或里程碑
price_plan:
  bsc: { name: 基本的 }
  dem: { name: 演示 }
  prm: { name: 保费 }
  trl: { name: 试用 }
timesheet:
  add_more_work_to_batch_register: 将更多工作添加到批量注册
  add_rest: 添加休息
  apply: 申请
  unapply: 取消申请
  available_time: 有空时间
  batch_register: 批量登记
  batch_registration_of_work: 工作批量登记
  batch_registration_method: 批量注册方式
  fill_available_time: 填补空白
  stop_watch: 秒表
  delete_with_including_attendance_and_rest_and_dayoff: 删除包括出勤、休息和休息日
  delete_timesheet_question: "您是否删除{{date}}的时间表？"
  unapply_timesheet_question: "您是否取消申请{{date}}的时间表？"
  specify_ratio: 指定比率
  specify_time: 指定时间
  registered_time: 注册时间
  target_dates: 目标日期
  date_not_be_updated: 没有输入出勤或没有可用时间
  select_from: "从{{name}}中选择"
  select_from_or: "选择{{name1}}或{{name2}}"
  select_from_my_pattern: "从MyPattern中选择"
  select_from_history: "从历史记录中选择"
timesheet_report:
  checkbox:
    unmatch: "只显示有差异(<0>{{data}}</0>)的成员"
  option:
    highlight_greater_than: 突出显示多余
    highlight_less_than: 突出显示小于
global_notices:
  2023_07_ts_improve: 新的时间表已得到改进。
  2023_08_design_update: 云日志的标志设计已更新。
  2023_08_work_history_and_cancel_applicant: 【功能更新】工时历史功能、申请人取消申请功能等
  2023_09_work_budget_on_work_report: 未来的预算现在可以显示在工作报告中。
  2023_10_ts_improve: 【功能更新】新时间表更新（作业弹出、日历同步...等）
work_budget:
  about: 关于工时预算显示
  display: 查看工作预算
  label:
    user_display: 会员展示
  radio:
    with: 有预算
    none: 没有预算
  select:
    cumulative: 成员
    direct: 项目
  project:
    setting_unit: 工时预算的设置单位
    project_budget: 工时预算
    unallocated_project_budget: 未分配的工时预算
    allocated_project_budget: 所有成员的工时预算
  msgs:
    detail: 显示内容根据项目类型而改变。
    process: 在此页面输入的工程预算将被显示。如果您有编辑权限，您可以编辑它。
    gantt: 将显示每个月为任务设置的总工作预算。您不能编辑它。
    subtotal: 以上两个值的总和将被显示。<br>* 只有当同时选择过程和甘特图作为项目类型时，才会显示此选项。
workflow_timesheet_setting:
  add_excluded_user: 添加排除在外的成员
  add_user: 添加成员
  application_permission_setting: 应用程序权限设置
  confirm_delete_modal_message: 是否要从 “排除的成员” 中删除选定的成员？
  delete_excluded_user: 删除排除在外的成员
  list_set_excluded_members: 列出并设置排除在外的成员
  n_users_selected: "{{n}} 人已被选中"
  worktime_discrepancy_permissibility: 申请工时和出勤差异的可用性
attendance_difference:
  highlight_difference_greater_than: 突出显示偏差多余
  highlight_if_member_has_difference: 如果成员有差异则突出显示
  not_required: 不需要(无差异)
  reason_class:
    others: 其他（自由填写）
    pc_used_not_for_work: 有业务用途以外的电脑使用
    pc_unnecessarily: 有不需要使用电脑的业务（包括外出或出行等）
    pc_log_unreadable: 电脑日志获取异常（网络故障或系统异常等）
    pc_off_omission: 电脑未正确关闭（未关机或未锁屏等）
symbol:
  asterisk: ※
  bracket_open: 【
  bracket_close: 】
  quotation_open: 「
  quotation_close: 」
period_up_to:
  today: 直到今天
  yesterday: 直到昨天
  end_of_last_week: 直到上周末
  end_of_last_month: 直到上个月月底
feedback:
  opinion_request: 对新{{feature}}的意见和要求
  opinion_request_for_this_feature: 对此功能的反馈
outlook_extension:
  open_timesheet: 打开时间表界面
  saml_setting_title: SAML 配置
project_management_gantt:
  group_delete_unavailable: 无法删除包含任务的任务组。
  context_menu:
    add_same_level: 添加到同一级别
    add_same_submenu:
      task: *task
      task_group: *task_group
      milestone: *milestone
    add_submenu:
      task: *task
      task_group: *task_group
      milestone: *milestone
