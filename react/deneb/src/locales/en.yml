word:
  account:                 Account
  action:                  Action
  active:                  Active
  activity_log:            Activity Logs
  add:                     Add
  add_column:              Add column
  add_new_category:        Add new category
  admin:                   Admin
  admin_menu:              Admin
  aggregate_axis:          Dimension
  ago:                     Ago
  all:                     All
  all_download_zip:        All Download(ZIP)
  allow:                   Allow
  answer_questionnaire:    Answer questionnaire
  any_period:              Any period
  api_key:                 API KEY
  apply_availability:      Availability
  apply_available:         Available
  apply_unavailable:       Unavailable
  apply_unavailable_cause: Reason of Unavailable
  apply_permission_setting: Apply Permission Setting
  approval:                Approval
  approved:                Approved
  approve_pending:         Pending
  approve_unapplied:       Unapplied
  asc:                     Asc
  assign:                  Assign
  assignee:                Assignee
  assigned_department:     Assigned Department
  assign_and_budget:       Assign & Budget
  attendance:              Attendance
  attendance_and_pc_logs:  Attendance and PC Logs
  attendance_end:          Attendance End
  attendance_set_to_worktime: Fit to work time
  attendance_menu:         Attendance Menu
  attendance_start:        Attendance Start
  attendance_start_end:    Attendance
  attendance_time:         Attendance Time
  attendance_total:        Attn'd Total
  attendance_work_unmatch: Unmatch between work and attendance
  authorization_settings:  Authorization Settings
  auto_sync:               Auto Sync
  basic_setting:           Basic Settings
  back_to_top:             Back To Top
  back_to_previous_page:   Back to Previous Page
  bar_chart:               Bar chart
  batch_insert:            Batch insert
  before_start:            Before start
  belong:                  Company Belonged
  beta_version:            β Ver.
  budget:                  Budget
  budget_digestion:        Budget Digestion
  business:                Business
  business_master:         Business Master
  calendar:                Calendar
  calendar_view:           Calendar view
  cancel:                  Cancel
  cases:                   Cases
  cash:                    Cash
  cash_admin:              Cash Admin
  chart_type:              Chart Type
  category_name: Category Name
  category_management_code: Category Management Code
  change_password:         Change Password
  change_settings:         Change Settings
  clone:                   Clone
  close:                   Close
  closing_admin:           Closing Admin
  closing_day:             Closing Day
  create:                  Create
  check_all:               Check All
  check_details:           Check Details
  change:                  Change
  code:                    Code
  comment:                 Comment
  company_key:             Company name in login URL
  company_settings:        Company Settings
  completed:               Completed
  confirm_delete:          Confirm Delete
  connect:                 Connect
  connected:               Connected
  connected_service:       Connected service
  copied:                  Copied
  copy:                    Copy
  contact_us:              Contact Us
  cost:                    Cost
  cost_management:         Cost Management
  custom_field:            Custom Field
  custom_report:           Custom Report
  customer:                Customer
  daily:                   Daily
  daily_report:            Daily Report
  daily_work:              Daily Work
  dashboard:               Work and Budget (Previously Work Dashboard)
  data_manage:             Data Manage
  date:                    Date
  day:                     Day
  days:                    Days
  day_of_week:             Day of week
  day_of_week_short:       DoTW
  day_unit:                Days
  deadline:                Due Date
  default:                 Default
  delay:                   Delay
  delay_status:            Delay status
  delayed_tasks:           Delayed tasks
  delete:                  Delete
  department_master:       Department Master
  desc:                    Desc
  dependency:              Dependency
  difference2:             Difference
  difference_reason:       Difference Reason
  difference_reason_class: Choose Difference Reason
  difference_reason_memo:  Remarks
  difference_setting:      Difference Setting
  differences_admin_self:  Differences of mine
  discard:                 Discard
  notif_destination:       Notification Destination
  department:              Department
  disconnect:              Disconnect
  disconnect_integration:  Disconnect Integration
  department_name:         Department Name
  display:                 Display
  display_by_hierarchy:    Display by hierarchy
  display_order:           Display Order
  display_settings:        Display Settings
  display_period:          Display Period
  download:                Download
  duplicate:               Duplicate
  edit:                    Edit
  edit_category:           Edit Category
  edit_budget:             Edit Budget
  edit_notification:       Edit Notification
  end:                     End
  end_date:                End Date
  end_date_shorten:        End
  end_of_month:            End Of Month
  end_time:                End Time
  email:                   Email
  employ_type:             Employ Type
  employ_type_master:      Employ Type Master
  employment_status:       Employment status
  example:                 Ex.
  excel:                   Excel
  expand:                  Expand
  expense:                 Expense
  expense_management:      Expense Management
  export:                  Export
  export_all:              Export All
  export_type:             Export Type
  id:                      ID
  integer:                 Integer
  integration:             Integration
  integration_name:        Integration Name
  integrations_settings:   Integrations Settings
  integrations_services:   Integrations Services
  integrated_attendance:   External Integrated Attendance
  failure:                 Failure
  favorite:                Favorite
  feature:                 Feature
  filter:                  Filter
  filter_search:           Filter search
  gantt:                   Gantt
  ganttchart:              Gantt Chart
  general:                 General
  generate_summary:        Generate summary
  greater_than:            Greater Than
  google_extension_ver:    Google Chrome ver.
  gross_profit:            Gross Profit
  help:                    Help
  highlight:               Highlight
  holiday_master:          Holiday Master
  holiday_type:            Holiday Type
  home:                    Home
  hour:                    Hour
  hour_or_less:            Hour or less
  time_unit:               h
  try_it:                  Try it
  history:                 History
  hours:                   Hours
  input_attendance:        Input Attendance
  input_rate:              Input Rate
  in_progress:             In Progress
  input_work:              Input Work
  difference:              Difference
  import:                  Import
  items_to_be_exported:    Items to be exported
  ja_extended:             Japanese (EUC-JP)
  ja_shift:                Japanese (Shift-JIS)
  job_popup:               Job popup
  kanban:                  Kanban
  key:                     Key
  keyword:                 Keyword
  last_day:                Last Day
  last_updatetime:         Last Input Time
  later:                   Later
  laern_more:              Learn more
  less_than:               Less Than
  level_n:                 Level {{level}}
  list:                    List
  logout:                  Sign out
  lower_row:               Lower Line
  ongoing:                 Ongoing
  other:                   Other
  other_functions:         Other functions
  owner:                   Owner
  owner_code:              Owner Code
  mail:                    Mail
  man_day:                 Man-Day
  man_hour:                Man-Hour
  man_minute:              Man-Minute
  man_month:               Man-Month
  manage:                  Manage
  manage_api:              Manage API
  management_code:         Management Code
  management_code_shorten: Manag't Code
  management_code_update:  Manag't Code Update
  master_setting:          Master Settings
  match:                   Match
  member:                  Member
  member_summary:          Member Summary
  member_department:       Member Department
  member_fields:           Member fields
  memo:                    Memo
  menu:                    Menu
  microsoft_extension_ver: Microsoft Edge ver.
  milestone:               &milestone Milestone
  milestone_add:           Add Milestone
  milestone_name:          Milestone Name
  milestone_edit:          Edit Milestone
  minute:                  Minute
  minute_unit:             m
  month:                   Month
  month_unit:              Months
  monthly:                 Monthly
  more:                    More
  ms_teams:                Microsoft Teams
  multi_gantt:             Multiple Gantt Chart
  multi_pie_chart:         Multiple Pie Chart
  my_attendance_export:    My Attendance Export
  my_pattern:              My Pattern
  my_report:               My Report
  my_timesheet_export:     My Timesheet Export
  my_timesheet_report:     My Timesheet Report
  my_work_report:          My Work Report
  name:                    Name
  member_name:             Member Name
  narrow_down:             Filter
  normal:                  Normal
  notif_content:           Notification Content
  new:                     New
  new_notification:        New Notification
  new_ganttchart:          New Gantt Chart
  new_process_group:       New Process Group
  no_category:             No category
  no_operation:            No operation
  not_active:              Inactive
  not_allow:               Not Allow
  not_displayed:           Not displayed
  not_now:                 Now now
  not_selected:            Not selected
  not_sorted:              Not sorted
  notice:                  Notice
  not_use:                 Not use
  notice_admin:            Notice Admin
  notifications:           Notifications
  notified_events:         Notified Events
  notification_name:       Notification Name
  notification_type:       Notification Type
  notification_delete:     Delete Notification
  notification_settings:   Notification Settings
  notification_subject:    Subject
  number:                  Number
  numbers_of_per_page:     Per page
  ok:                      OK
  old:                     Old
  on_time:                 On Time
  overdue:                 Overdue
  participants:            Participants
  pc_log:                  PC Log
  pc_management:           PC Manage
  pc_management_top:       PC Manage Top
  performer:               Performer
  period:                  Period
  permission_master:       Permission Master
  personal_report:         My Work Report
  personal_setting:        Personal Settings
  personal_work:           My Work Export
  pie_chart:               Pie chart
  predecessor:             Predecessor
  process:                 Process
  process_1:               Process 1
  process_2:               Process 2
  process_3:               Process 3
  process_category:        Process Category
  process_delete:          Delete Process
  process_group_delete:    Delete Process Group
  process_category_delete: Delete Process Category
  process_group:           Process Group
  process_group_name:      Process Group Name
  processes_included:      Processes Included
  process_name:            Process Name
  process_master:          Process Master
  profit:                  Profit and Loss
  profit_and_asset:        Profit and Asset
  profit_by_customer:      Profit by Customer
  profit_rate:             Profit Rate
  progress:                Progress
  progress2:               Progress
  progress_rate:           Progress
  progress_status:         Progress status
  project:                 Project
  project_business:        Project Business
  project_customer:        Project Customer
  project_department:      Project Department
  project_fields:          Project fields
  project_field_master:    Project Field Master
  project_list:            Project list
  project_index:           Project Index
  project_basic_info:      Project Basic info
  project_code:            Project Code
  project_name:            Project Name
  project_owner:           Project Owner
  project_slash_task:      Project / Task
  project_summary:         Project Summary
  project_work:            Project Work
  project_field:           Project Field
  project_type:            Project Type
  project_type_in_use:     In use
  project_management:      Project Management
  ratio:                   Ratio
  register:                Register
  registered:              Registered
  rejected:                Rejected
  rejected2:               Rejected
  rejected_comment:        Rejected comment
  related_functions:       Related functions
  remind_notif:            Remind Notification
  report:                  Report
  report_history:          Report History
  report_name:             Report Name
  report_top:              Report Top
  required_time:           Required time
  reset:                   Reset
  rest:                    Rest
  rest_allday:             Day off
  rest_am:                 Morning Off
  rest_pm:                 Afternoon Off
  result:                  Result
  sales:                   Sales
  sales_management:        Sales Management
  sales_status_master:     Sales Status Master
  save:                    Save
  save_as:                 Save As
  save_duration:           Save duration
  save_or_export:          Save Or Export
  select_department:       Select Department
  select_process:          Select {{processName}}
  select_project:          Select project
  select_time:             Select Time
  search:                  Search
  search_condition:        Search Condition
  secret_access_key:       Secret access key
  send_immediately:        Send immediately
  send_test:               Send test
  settings:                Settings
  show_multiple_pie_chart: Show multiple pie chart
  show_only_members_with_differences: Show only members with differences
  show_ruled_lines:        Show ruled lines
  slack:                   Slack
  subtotal:                Subtotal
  success:                 Success
  success_with_warning:    Success (with warning)
  sum_line_chart:          Sum line chart
  stacked_bar_chart:       Stacked bar chart
  combo_chart:             Combo chart
  sort:                    Sort
  start:                   Start
  start_date:              Start Date
  start_date_shorten:      Start
  start_time:              Start Time
  state:                   State
  status:                  Status
  stop_watch:              Stop Watch
  stop_watch_start:        Start
  stop_watch_stop:         Stop
  stop_watch_apply_to_tst: Apply to timesheet
  subscription_on:         Subscription On
  subscription_off:        Subscription Off
  successor:               Successor
  summary_of_the_report_results: Summary of the report results
  switch_horizontal_axis:  Switch horizontal axis
  sync:                    Sync
  sync_end_datetime:       Sync end date and time
  sync_start_datetime:     Sync start date and time
  sync_attendance:         Sync attendance
  sync_external_services:  Sync external services
  sync_status:             Sync status
  sync_duration:           Sync duration
  sync_with_calendar:      Sync with calendar
  target_period:           Target period
  task:                    &task Task
  task_add:                Add Task
  task_create:             Create Task
  task_edit:               Edit Task
  task_fields:             Task fields
  task_group:              &task_group Task Group
  task_group_add:          Add Task Group
  task_group_edit:         Edit Task Group
  task_group_name:         Task Group Name
  task_list:               Task List
  task_name:               Task Name
  task_process:            Task Process
  task_process_group:      Task Process Group
  this_month:              This month
  time:                    Time
  time_required:           Time Required
  timesheet:               Timesheet
  timesheet_approval:      Timesheet Approval
  timesheet_approval_flow: Approver
  timesheet_edit_dialog:   Timesheet edit dialog
  timesheet_report:        Timesheet Report
  timesheet_report_notif:  Timesheet Report Notification
  timesheet_to_be_notified: Timesheet to be notified
  timesheet_usage:         Timesheet Usage
  timesheet_usage_report:  Timesheet Usage Report
  title_master:            Title Master
  base_date:               Base Date
  title:                   Title
  today:                   Today
  todo:                    To-Do
  total:
      type1:               Total
      type2:               Total
  total_items:             Total {{quantity}} items
  total_unit:              Total Unit
  total_bar_chart:         Total bar chart
  type:                    Type
  unapply:                 Cancel My Apply
  unicode:                 Unicode
  unit:                    Unit
  unregistered:            Unregistered
  update:                  Update
  upper_row:               Upper Line
  upgrade_now:             Upgrade Now
  url:                     URL
  use:                     Use
  use_count:               Use Count
  user:                    User
  user_contract:           Contract
  user_code:               Member Code
  user_customer:           Customer
  user_department:         Member Department
  user_title:              Title
  unmatch:                 Unmatch
  webhook:                 Webhook
  week:                    Week
  weekly_timesheet_report: Weekly Timesheet Report
  work:                    Work
  work_add:                Add work
  work_and_budget:         Work and budget
  work_and_budget_prev:    Previously Work Dashboard
  work_attendance_match:   Work-Attendance match
  work_budget_actual:      Work and Budget
  work_cost:               Work Cost
  work_content:            Work Content
  work_output_unit:        Work Output Unit
  work_management:         Work Budget Management
  work_budget:             Work Budget
  work_report:             Work Report
  work_report_save:        Save Work Report
  work_report_sync_date:   Work Report Sync Date
  work_report_sync:        Sync Work Report
  work_time:               Work Time
  work_total:              Total
  work_in_total:           Work Total
  worktime_input_omission_remind_notif: Worktime Input Remind Notification
  working:                 Working
  working_hours:           Working Hours
  year:                    Year
  zh_simplified:           Chinese (Simplified)
  whole_day_off:           Day Off
  morning_day_off:         Morning Off
  afternoon_day_off:       Afternoon Off
  not_specified_day_off: None
common:
  service_name: CrowdLog
msg:
  add_dimension_locked: You need to have Premium plan to use 2 or more dimensions
  add_external_integration_service: Add external integration service
  assigned_user_is_invalid: Assigned user is invalid
  atleast_one: "{{this}} or {{that}}"
  attendance_differences:
    difference_between_start_and_end_is_focus: The difference between the start and end times is the focus.
    attendance_or_pclog_is_not_collected1: Attendance or PClog is not yet finalized
    attendance_or_pclog_is_not_collected2: Attendance：Please enter the end time
    attendance_or_pclog_is_not_collected3: PCLog：Please log in to your PC
  browser_extensions_convenience: Browser extensions for more convenience!
  chart:
    error:
      no_data: Error generating chart. There is no data.
  check_all: Check all
  click_generate_summary: 'Click "{{button}}" to summarize the work report.'
  click_generate_summary_to_regenerate: 'Some parameters have been updated. Click "{{button}}" to generate a new summary of the work report.'
  copied_data: "Copied <0>{{data}}</0>"
  create_notification: "Click {{button}} to create a new notification."
  custom_report:
    fetch_failed: Failed to fetch Custom Reports.
    restore_failed: Restore failed because a non-existent custom report was selected.
    change_settings_success: Custom Report settings has been successfully changed.
    change_settings_failed: Failed to change Custom Report settings.
    update_success: Custom Report has been successfully updated.
    update_failed: Failed to update Custom Report.
    save_success: Custom Report has been successfully saved.
    save_failed: Failed to save Custom Report.
    not_save: Currently unavailable.
    delete_success: Custom Report has been successfully deleted.
    delete_failed: Failed to delete Custom Report.
    report_limit: The number of reports that can be saved is up to {{max}} reports only.
    fetch_failed_fav: Failed to fetch Favorite Reports.
    save_success_fav: Successfully added to Favorites.
    save_failed_fav: Failed to add in Favorites.
    delete_success_fav: Favorite Report has been successfully removed.
    delete_failed_fav: Failed to remove Favorite Report.
    already_marked: Custom Report is already marked as favorite.
    sort_custom_success: Custom Report has been successfully sorted.
    sort_custom_failed: Failed to sort Custom Reports.
    sort_favorite_success: Favorite Report has been successfully sorted.
    sort_favorite_failed: Failed to sort Favorite Reports.
    example_case: represents for
    scope_of_aggregation: "Scope of aggregation: "
    example_december: "Dec "
    example_january: "Jan "
    monthly_available_only: Only available for monthly
  delete_selected_notifications: Delete selected notifications.
  default_api_error:   Server error happened.
  delete_confirm:      Are you sure to delete?
  delete_confirm_with_name_and_date: "Do you want to delete {{name}}'s {{date}} data?"
  delete_confirmation_for_report: Are you sure to delete the data of {{userName}}'s {{date}}?
  delete_process_category_confirm:
    line1: Do you want to delete "{{data}}" category?
    line2: After deletion, the category of the linked process will be changed to "{{empty}}"
  delete_process_confirm: "Do you want to delete {{userName}}'s {{date}} data?"
  deleted:             Deleted.
  deleted_data:        "Deleted <0>{{data}}</0>"
  delete_failed:       Failed to delete.
  detail_search:       Filter by detailed criteria
  display_work_budget: Display Work budget
  display_work_budget_requirements:
    dimension:
      must_select_either: 'Either Member related items or {{dimension}}'
      must_not_selected: '{{dimension}} Not selected'
      must_not_selected_two: '{{dimension1}} and {{dimension2}} Not selected'
  do_not_reload_page:  Please do not reload the page.
  do_not_show_this_notice_again: Do not show this notice again
  end_project_process_budget: Very sorry, but this feature is scheduled to be discontinued on Thursday, September 5, 2024. For more information, please see <helpLink>here</helpLink>
  enter_integration_info: Enter the integration information.
  export:
    failed:            Failed exported.
    succeed:           Successfully exported.
    exporting:         Export is in progress.
    not_authorized:    You do not have permission to export.
    limit_per_page:    The number of records that can be exported is up to {{max}} records. Only the first {{max}} records are exported.
  failed_copy_data:            "Failed to copy <0>{{data}}</0>."
  failed_delete_data:          "Failed to delete <0>{{data}}</0>."
  failed_save_data:            "Failed to save <0>{{data}}</0>."
  feature_not_available: This feature is not yet available
  form:
    confirmation:
      discard_updates: "Are you sure to discard updates？"
  generate_summary_failed: Failed to generate summary.
  generate_summary_succeed: Summary has been generated.
  integrations_settings:
    auto_integ:
      form_title: Automatic sync settings
      form_details:
        - When used, attendance information for the past 40 days of all subjects will be automatically synchronized on the system side at 05:00 every day.
        - ※This setting can be changed at any time.
      info: When connected, the system automatically synchronizes attendance information for the past 40 days at 05:00 every day. You can change this setting at any time by clicking the "Settings" button on the screen.
      setup_finished:
        jobcan: You have updated ジョブカン setting ({{settingName}}).
        kot: You have updated King of Time setting ({{settingName}}).
        kinkakuji: You have updated Kinkakuji setting ({{settingName}}).
        hrmos: You have updated HRMOS setting ({{settingName}}).
      unable_to_update: A system error is currently preventing this setting from being changed. Please try again after a while.
    connected: Connected with {{sysName}} ({{settingName}}).
    connection_failed: Failed to connect with {{name}}.
    connected_detail:
      jobcan: Attendance can be synchronized from the "Attendance" screen in the administration menu.
      kot: Attendance can be synchronized from the "Attendance" screen in the administration menu.
      kinkakuji: Attendance can be synchronized from the "Attendance" screen in the administration menu.
      hrmos: Attendance can be synchronized from the "Attendance" screen in the administration menu.
    desc:
      jobcan: This enables to sync attendance registered in ジョブカン and to view sync execution history.
      kot: This enables to sync attendance registered in King of Time and to view sync execution history.
      kinkakuji: This enables to sync attendance registered in Kinkakuji and to view sync execution history.
      hrmos: This enables to sync attendance registered in HRMOS and to view sync execution history.
    disconnected: Disconnected with {{name}}
    disconnection:
      jobcan:
        - If you disconnect, you will not be able to synchronize attendance.
        - Attendance that has been synced in the past will not change.
      kot:
        - If you disconnect, you will not be able to synchronize attendance.
        - Attendance that has been synced in the past will not change.
      kinkakuji:
        - If you disconnect, you will not be able to synchronize attendance.
        - Attendance that has been synced in the past will not change.
      hrmos:
        - If you disconnect, you will not be able to synchronize attendance.
        - Attendance that has been synced in the past will not change.
    disconnection_failed: Failed to disconnect with {{name}}.
    form_instruction: Please enter the information below to work with {{name}}.
    form_id_placeholder: ID set in {{name}}
    form_secret_key_placeholder: Secret access key set in {{name}}
    limit_has_been_reached: Cannot register setting of {{sys}} because registration limit has been reached.<br />
                            Up to {{num}} of {{sys}} setting can be registered.
    unable_to_connect_jobcan: Unable to connect. Invalid ID or Secret access key.
    unable_to_connect_kot: Unable to connect. Invalid access token.
    unable_to_connect_hrmos: Unable to connect. Invalid company name in URL or API KEY.
  integration_attendance:
    summaries:
      failure: Synchronization could not be executed due to a system error. See the <helplink>help page</helplink> for details.
      running: Synchronizing
      success_with_warning: May contain unexpected synchronization results. See the <helplink>help page</helplink> for details.
    errors:
      cannot_convert_attendance: "Cannot convert attendance"
      attendance_synchronized_by_another_condition_existed: "Attendance synchronized by another condition existed"
      integration_attendance_access_prohibit_error: "「.+」is unavailable between 「.+」"
  following_projects_cannot_be_deleted: The following projects cannot be deleted because they have not been provided with project editing permissions.
  following_projects_cannot_be_exported: The following projects cannot be exported because the necessary permissions for export have not been provided.
  force_delete_keyword_mismatch: Keyword Does Not Match. Force Delete Was Cancelled.
  force_delete_project:
    introduction: Are you sure you want to force delete selected projects?
    prompt_confirmation: "By confirming the force delete, the customer is aware of the possibility that this may result in loss of data related to the selected projects and have taken note of the following:"
    warnings:
      - Registered sales of selected projects will be deleted.
      - Registered costs of selected projects will be deleted.
      - Registered expense budgets of selected projects will be deleted.
      - Registered expense results of selected projects will be deleted.
      - Registered tasks of selected projects in gantt chart will be deleted.
      - Registered timesheets(man-hour data) of selected projects will be deleted.
      - Registered work budgets of selected projects will be deleted.
      - Registered work cost results of selected projects will be deleted.
    if_not_want_to_delete_data: Please deactivate projects if you don't want to delete.
    requirement: To delete a project, the customer must be a System Administrator or has Role Permission in the Permission Master.
    prompt_input_delete: "Enter 'DELETE' then confirm"
  update_external_integration_service: Update external integration service
  integrated_attendance:
    now_integrated_with: "Attendance information is getting integrated with {{targetSystem}}."
    input_manually: "Input manually"
    attendance_tips: "Input Attendances get updated with {{targetSystem}} through integrating unless you create an apply."
    attendance_diff_exists: "There are differences between attendance and integrated attendance information"
    jobcan: "jobcan"
    kot: "KING OF TIME"
    kinkakuji: "Kinkakuji"
    hrmos: "HRMOS"
    integration_attendance_failed: "Attendance synchronization could not be started."
    integration_attendance_is_submitted: "Attendance synchronization is started. The next synchronization will not be performed until this process is completed."
  mark_as_favorite: Mark as favorite
  move_date_direction: "Move {{date}} {{unit}} {{direction}}"
  multi_pie_chart:
    display_condition: Multiple Pie Chart Requirements
  project_delete:
    failed:            There is a project that failed to be deleted
    succeed:           Successfully deleted
  projects_that_could_not_be_deleted: projects that could not be deleted
  projects_that_could_not_be_exported: projects that could not be exported
  download:
    failed:    Download failed. Please execute after a while.
    preparing: Preparing to download
  filter_start_end_date_any_period: Filter start/end date by any period
  new_feature_released: New feature has been released.
  no_available_items: There are no {{name}} available
  no_matches_found: No matches found
  no_matching_results_found: No matching results found
  no_member_assigned: No member has been assigned
  no_options: No options
  no_options_business: Please enter the name of the business
  no_options_customer: Please enter the name of the customer
  no_options_department: Please enter the name of the department
  no_options_obj: "Please enter the name of the {{obj}}"
  no_options_project: Please enter the name of the project
  no_options_process: Please enter the name of the process
  no_permission:
    header: You do not have enough permission to access this page.
    line1:  You need to set the permission in the Permission Master page.
    line2:  Learn more about %title_key.
  no_options_task_status:  ※Please select a project
  not_allowed_to_view_data: "Not allowed to view data"
  not_found:
    header: Not Found
    line1:  Page cannot be found.
    line2:  Please make sure the entered URL is correct.
  outlook_extension:
    calendar_sync:
      no_sync_code: This event cannot be synchronized, because the management code is not set. ({{data}})
  pj_timeline_advertising_title:  Manage project budget and actual<br />in easy and convenient way.
  pj_timeline_advertising_items:
    - Tracking budget and performance throughout the project
    - Time-series budget and actual performance
    - Visual understanding of project start/end and timing of man-hours incurred
  plan:
    integration_attendance_title: With the external attendance integration function<br />More accurate man-hour management!
    integration_attendance_details:
      - Synchronize attendance information with the CrowdLog by linking with the attendance management system in use
      - More accurate man-hour management according to actual working hours
  process:
    success_save: Saved.
    no_data: Processes can be created from the "New" menu.
    delete_modal_message: Do you want to delete a process?<br />Once a process is deleted, it cannot be restored.
    success_delete: "Process has been succesfully deleted."
    category_success_delete: The process category has been successfully deleted.
    failed_delete: "Process can not be deleted because the master is in use."
    failed_update_active: "Process used in Process Group cannot be inactive"
    new_process_name: New Process Name
    table_update_refresh: Some table rows have been updated, please reload the page.
  process_group:
    no_data: Process Group can be created from the "New" menu.
    delete_modal_message: Do you want to delete a process group?<br />Once a process group is deleted, it cannot be restored.
    success_delete: "Process Group has been succesfully deleted."
    failed_delete: "Process Group can not be deleted because the master is in use."
    add_process: Add {{process}}
    form_search: Search by process name or category name.
  prepend_api_error:     Something went wrong.
  process_not_active: The process is inactive.
  process_not_assigned_to_project: The process is not assigned to this project.
  remaining_number_of_generation: "Remaining number of generation for this month: {{remainingUsage}} / {{limit}}"
  remaining_number_of_generation_description: The number of generation is counted in the total within the company
  reset_to_default:      Reset to default settings
  restrictions:
    lets_upgrade: Upgrade your plan to activate this feature!
    plan_restricted: Your plan does not support this feature.
  save_data:             Save {{data}}
  saved_data:            "Saved <0>{{data}}</0>."
  saved:                 Saved successfully
  save_confirm:          Are you sure to save?
  save_failed:           Save failed.
  saving:                Saving
  select_date: Select date
  select_display_settings: Select the items you want to display in the list.
  select_end_date: Select end date
  select_integration_service: Select integration service.
  select_one_or_more: Please select one or more items.
  select_start_date: Select start date
  send_remind_result_to_title_user: Send reminder results to title user
  send_remind_result_to_title_user_desc: Reminder result notifications allow you to see which members were sent reminder. <br />This notification only includes information about members of your department.
  send_immediately_in_bulk: Send immediately in bulk
  success_send_immediately: Successfully sent immediately.
  failure_send_immediately: Failed to send immediately.
  success_send_immediately_in_bulk: Successfully sent immediately in bulk.
  failure_send_immediately_in_bulk: Failed to send immediately in bulk.
  show_assigned_project: Only projects assigned to you
  show_only_active_members: Show only active members
  show_only_in_progress_projects: Show only in progress projects
  summarize_n_or_more_data_as_other: Summarize {{dataLimit}} or more data as other
  combo_chart_title: "Ratio of {{dimension2nd}} to {{dimension1st}}"
  combo_chart_search_condition_mismatch: "Please select one item for {{dimension2nd}} and one for {{dimension1st}} in search form"
  combo_chart_dimensions_condition_mismatch: Please select two aggregation axes
  combo_chart_condition_mismatch: Sorry, the graph cannot be displayed with the current data
  guide_to_help_pages: For more information
  switch_to_old_design: Switch to old design
  sync_attendance:
    success: Synchronized attendance.
    failed: Failed to synchronize attendance.
    synchronizing: Synchronization in progress
    synchronizing_detail:
      - Synchronization is in progress.
      - The next synchronization run cannot be performed until the process is completed.
      - Please wait for a while until the process is completed.
  unmark_as_favorite: Unmark as favorite
  updated_data:            "updated <0>{{data}}</0>."
  notification_send_test:
    success:     Test message successfully sent.
    in_progress: Sending test message.
    failed:      Test message failed to send.
  or_confirm_our_plan: Or, <0>confirm our plan</0>
  out_of_project_period: Out of project period.
  out_of_user_period: Out of user period.
  timesheet:
    input_daily_report: Input daily report
    add_memo: Add memo
    apply_failed: Failed to apply for timesheet.
    apply_success: Success to applied for timesheet.
    apply_cancel_success: Success to cancel the apply for timesheet.
    apply_cancel_fail: Fail to cancel the apply for timesheet.
    apply_message: Apply for the following timesheets.
    apply_warning: There are timesheets that could not be applied for.
    apply_this_date: Do you want to apply timesheet for this date?
    unapply_this_date: Do you want to cancel the timesheet application for this date?
    calendar_sync_not_determined: Calendar sync is not determined
    delete_memo: Delete memo
    executing_fill_available_time: Executing Fill Available Time
    save_success: Timesheet has been successfully saved.
    save_failed: Failed to timesheet save
    delete_success: Timesheet has been successfully deleted.
    delete_failed: Failed to timesheet delete
    no_permission: You do not have permission to see this user's timesheet
    cannot_used_work: It's a {{work}} that cannot currently be used
    is_approved: Can not edit timesheet because it is approved.
    is_pending: Can not edit timesheet because it is being applied.
    is_unmatch: There is a timesheet where attendance and man-hours do not match.
    calendar_sync:
      success: Synchronized calendar
      failed: Failed to synchronize calendar
      no_events: No events to synchronize
      synchronizing: Synchronizing calendar
      has_temp_work: Some events are not yet finalized
      has_temp_work_body: There is an event for which no calendar sync code has been entered. Please enter the project and process or task.
      select_account: Synchronization with another account
      no_sync_code: Missing management code ({{data}})
    copy: Copy
    copy_from: Copy from
    copy_to: Copy to
    copy_to_date: Destination date
    copy_not_executed: Copy is not executed
    copy_no_more_date: Cannot add more date to copy
    copy_completed: Copy is completed
    copy_date_select: Please choose the dates where copy to
    copy_add_date: Add a date where copy to
    copy_overwrite_attendance: Overwrite dayoff, attendance, rests through copy
    process_not_selected: Process is not selected
    try_new_timesheet_now: Try new timesheet now
    questionnaire:
      button_label_1: Click here for questionnaire
      button_label_2: about new timesheet
    stop_watch:
      sw_add_new: Add a StopWatch
      sw_status_conflict: This site is outdated. Please reload.
      sw_select_work_and_apply: Cannot apply the timing result to timesheet. Please set your work content and click apply to timesheet button.
      sw_correct_memo_and_apply: Please make your memo not longer then 200 characters and click apply to timesheet button.
      sw_apply_to_tst_successfully: Successfully saved the work to your timesheet.
      sw_need_to_reset_manually: Successfully saved the work to your timesheet. Please push the reset button for another timer run.
      sw_cannot_start_timing: " .Cannot start timing with stop watches. "
      sw_fail_to_add_new: Failed to add a new stop watch.
      sw_limit_max_stop_watch_count: "You can only add {{num}} stop watches at most."
      sw_exists_another_with_timing: Another stop watch is in timing. Please reload.
    shortcut_guide:
      title: Shortcut key guide
      change_date_to_today: Change date to today
      change_to_date_view: Change to date view
      change_to_week_view: Change to week view
      change_to_month_view: Change to month view
      show_next_time_range: Show next time range
      show_previous_time_range: Show previous time range
      change_to_calendar_view: Change to calendar view
      change_to_list_view: Change to list view
      zoom_in_or_out_time_slot: Zoom in or out time slot
    timekeeper_integration:
      register_message: Do you want to register this work item?
      resgister: register
      not_register: not register
    use_work_suggestion: Use work suggestion feature
  timesheet_detail_fail: Failed to get timesheet detail
  there_are_data_without_permission: There are <0>{{data}}</0> without permission.
  this_feature_is_available_in_premium: This feature is available in the Premium plan
  user_not_assigned_to_project: The user is not assigned to this project.
  unapproved_attendance: There is an indeterminate attendance.
  unapproved_timesheet:  There is an indeterminate timesheet.
  upgrade_premium_plan: Only in Premium Plan
  validation:
    at_least_one_checked: At least one must be checked.
    greater_than:         Input greater than {{num}}.
    greater_than_or_equal: Input greater than or equal to {{num}}.
    less_than_or_equal:   Input less than or equal to {{num}}.
    input_obj:            Input {{obj}}
    invalid_url:          Invalid URL.
    invalid_input:        Invalid input.
    invalid_start_end:    Invalid Start End Range
    invalid_work_memo:    Memo max length is 200 characters
    max_length:           "{{data}} must be a maximum of {{length}} characters in length"
    must_be_between:      Input must be between {{from}} and {{to}}.
    out_of_range_time:    Out of range time input
    invalid_work_time:    Invalid Work Time Unit
    less_than_work_time_unit: Input is less than the worktime unit time
    gte_date:             "{{end}} must be on or after {{start}}"
    required:             This field is required.
    starts_with:          It must start with "%s".
  work_check_error: Check one of the work budget, work result, work cost budget, and work cost result.
  work_budget:
    display_condition: Work Budget Requirements
    not_available: This graph is not supported
    cannot_edit_due_to_gantt: Cannot edit when using gantt chart
    change_setting_unit: Change setting unit of work budget
    confirm_change_to_member: Is it ok to change the setting unit of work budget to "member" ?
  work_report_sync:
    for_details: For More Details
    real_time_report: Real Time Report
    sync_rules_first: The work report displays pre-aggregated data.
    sync_rules_second: Press the UPDATE button to re-aggregate.
    sync_is_in_progress: The update is in progress. Please try again after it is completed.
    sync_has_started: The update process has started
    sync_failed_to_start: The update process has failed to start
  workflow_timesheet_setting:
    can_be_added_by_clicking_member: Excluded members can be added by clicking Add Member.
    success_add_excluded_user: Add excluded members.
    failed_add_excluded_user: Failed to add {{n}} members. Please try again to add the member to the list of excluded members.
    success_delete_excluded_user: Delete excluded members.
    failures_delete_excluded_user: Failed to delete {{n}} members. Please try again to delete the member to the list of excluded members.
placeholder:
  all_belong: All companies belonged
  all_departments: All departments
  all_employ_types: All employ types
  all_titles: All titles
  all_members: All members
  all_process: All process
  all_process_categories: All process categories
  attendance_difference_filter: Member name, Mail address, Member code
  entity_used_in_name: "{{entity}} used in {{name}}"
  identification_name: Identification Name
  input_comment: Input comment
  input_data: Input {{data}}
  input_milestone_name: Input milestone name
  input_search_conditions: Input search conditions
  input_task_group_name: Input Task Group name
  input_task_name: Input task name
  member_filter: Code, Member name, Email
  timesheet_usage_filter: Name, Code, Email
  process_filter: Process name, Management code
  project_filter:   Code, Project name
  notification_filter: Search by notification name.
  task_filter: Code, Task name
  timesheet_report_filter: Member name, Mail address, Member code
  excluded_modal_filter: Member code, Member name, Email
  sign_on_url: Input sign-on URL
  select_member: Select Member
  select_task_group: Select Task Group
  select_task_dependency: Select Task or Milestone
price_plan:
  bsc: { name: BASIC }
  dem: { name: DEMO }
  prm: { name: PREMIUM }
  trl: { name: TRIAL }
timesheet:
  add_more_work_to_batch_register: Add more work to batch register
  add_rest: Add Rest
  apply: Apply
  unapply: Cancel Apply
  available_time: Available time
  batch_register: Batch Register
  batch_registration_of_work: Batch Registration of Work
  batch_registration_method: Registration Method
  delete_with_including_attendance_and_rest_and_dayoff: Delete with including attendance and rest and dayoff
  delete_timesheet_question: "Do you delete timesheet of {{date}}?"
  unapply_timesheet_question: "Do you cancel the apply for timesheet of {{date}}?"
  fill_available_time: Fill available time
  stop_watch: Stop Watch
  specify_ratio: Specify by %
  specify_time: Specify Total Time
  registered_time: Registered time
  target_dates: Target Dates
  date_not_be_updated: No attendance has been entered or there is no available time.
  select_from: "Select from {{name}}"
  select_from_or: "Select {{name1}} or {{name2}}"
  select_from_my_pattern: "Select from MyPattern"
  select_from_history: "Select from History"
timesheet_report:
  checkbox:
    unmatch: "Show only members with Unmatch(<0>{{data}}</0>)"
  option:
    highlight_greater_than: Highlights if greater
    highlight_less_than: Highlights if less
global_notices:
  2023_07_ts_improve: New timesheet has been improved.
  2023_08_design_update: The logo design of CrowdLog has been renewed.
  2023_08_work_history_and_cancel_applicant: 【Feature Update】Work history feature, application cancellation by applicant feature, etc.
  2023_09_work_budget_on_work_report: Future budgets can now be displayed in Work Report.
  2023_10_ts_improve: 【Feature Update】Updates for new timesheet (Job popup, Calendar sync ... etc)
work_budget:
  about: About Work Budget Display
  display: Display of Work Budget
  label:
    user_display: Member Display
  radio:
    with: With Budget
    none: No Budget
  select:
    cumulative: By Member
    direct: By Project
  project:
    setting_unit: Work Budget Setting Unit
    project_budget: Amount Of Work Budget
    unallocated_project_budget: Amount Of Not Assigned Work Budget
    allocated_project_budget: Amount Of Work Budget Of All Members
  msgs:
    detail: The display content changes depending on the project type.
    process: The work budget entered in this page will be displayed. If you have editing permission, you can edit it.
    gantt: The total work budget set for the task will be displayed for each month. You cannot edit it.
    subtotal: The total of the above two values will be displayed. <br />* This is only displayed when both Process and Gantt Chart are selected as the project type.
workflow_timesheet_setting:
  add_excluded_user: Add Excluded Members
  add_user: Add Member
  application_permission_setting: Application Permission Setting
  confirm_delete_modal_message: Do you want to remove the selected member from “Excluded Members”?
  delete_excluded_user: Delete Excluded Members
  list_set_excluded_members: List and Set Excluded Members
  n_users_selected: "{{n}} Members Selected"
  worktime_discrepancy_permissibility: Permissibility of application for discrepancy between worktime and attendance
attendance_difference:
  highlight_difference_greater_than: Highlights if difference is greater
  highlight_if_member_has_difference: Highlights if member has difference
  not_required: No required(No diff)
  reason_class:
    others: Other (Free text)
    pc_used_not_for_work: PC has been used for non-work purposes
    pc_unnecessarily: Tasks that did not require PC use (outside visits or travel)
    pc_log_unreadable: PC log acquisition failure (network issues or system errors)
    pc_off_omission: PC was not properly off (shutdown or screen lock was missed)
symbol:
  asterisk: ※
  bracket_open: 【
  bracket_close: 】
  quotation_open: '"'
  quotation_close: '" '
period_up_to:
  today: Until today
  yesterday: Until yesterday
  end_of_last_week: Until last week
  end_of_last_month: Until last month
feedback:
  opinion_request: New {{feature}} Feedback
  opinion_request_for_this_feature: Feedback for this feature
outlook_extension:
  open_timesheet: Open Timesheet
  saml_setting_title: SAML Setting
project_management_gantt:
  group_delete_unavailable: Task group containing task can not be deleted.
  context_menu:
    add_same_level: Add to same level
    add_same_submenu:
      task: *task
      task_group: *task_group
      milestone: *milestone
    add_submenu:
      task: *task
      task_group: *task_group
      milestone: *milestone

