word:
  account:                 アカウント
  action:                  アクション
  active:                  アクティブ
  activity_log:            アクティビティログ
  add:                     追加
  add_column:              列を追加
  add_new_category:        カテゴリの新規追加
  admin:                   管理
  admin_menu:              管理メニュー
  aggregate_axis:          集計軸
  ago:                     前
  all:                     全て
  all_download_zip:        全データダウンロード(ZIP)
  allow:                   許可する
  answer_questionnaire:    アンケートに回答する
  any_period:              任意の期間
  api_key:                 API KEY
  apply_availability:      申請可否
  apply_available:         申請可能
  apply_unavailable:       申請不可
  apply_unavailable_cause: 申請不可の理由
  apply_permission_setting: 申請可否設定
  approval:                承認
  approved:                承認済
  approve_pending:         承認待
  approve_unapplied:       未申請
  asc:                     昇順
  assign:                  アサイン
  assignee:                担当者
  assigned_department:     アサイン部署
  assign_and_budget:       アサインと見積もり
  attendance:              勤怠
  attendance_and_pc_logs:  勤怠とPCログ
  attendance_end:          退社
  attendance_menu:         勤怠メニュー
  attendance_set_to_worktime: 工数に合わせる
  attendance_start:        出社
  attendance_start_end:    出退勤
  attendance_time:         勤務時間
  attendance_total:        勤務合計
  attendance_work_unmatch: 工数と勤怠の不一致
  authorization_settings:  権限の設定
  auto_sync:               自動同期
  basic_setting:           基本設定
  back_to_top:             ページの先頭へ戻る
  back_to_previous_page:   前のページに戻る
  bar_chart:               棒グラフ
  batch_insert:            一括登録
  before_start:            開始前
  belong:                  所属
  beta_version:            β版
  budget:                  予算
  budget_digestion:        予算消化
  business:                カテゴリ
  business_master:         プロジェクトカテゴリマスタ
  calendar:                カレンダー
  calendar_view:           カレンダービュー
  cancel:                  キャンセル
  cases:                   件
  cash:                    キャッシュ
  cash_admin:              キャッシュ入力
  chart_type:              グラフの種類
  category_name:           カテゴリ名
  category_management_code: カテゴリ管理コード
  change_password:         パスワード変更
  change_settings:         設定の変更
  clone:                   複製
  close:                   閉じる
  closing_admin:           締め管理
  closing_day:             締め日
  create:                  新規作成
  check_all:               全てチェック
  check_details:           詳細を確認する
  change:                  変更
  code:                    コード
  comment:                 コメント
  company_key:             ログインURLの企業名
  company_settings:        基本設定
  completed:               完了
  confirm_delete:          削除する
  connect:                 連携
  connected:               連携済
  connected_service:       連携サービス
  copied:                  コピーしました
  copy:                    コピー
  contact_us:              お問い合わせ
  cost:                    原価
  cost_management:         原価の管理
  custom_field:            カスタム項目
  custom_report:           カスタムレポート
  customer:                取引先
  daily:                   日次
  daily_report:            日報
  daily_work:              実績
  dashboard:               工数予実 (旧工数ダッシュボード)
  data_manage:             データ管理
  date:                    日付
  day:                     日
  days:                    日間
  day_of_week:             曜日
  day_of_week_short:       曜日
  day_unit:                日
  deadline:                期限
  default:                 デフォルト
  delay:                   遅延
  delay_status:            遅延ステータス
  delayed_tasks:           遅延タスク
  delete:                  削除
  department_master:       部署マスタ
  desc:                    降順
  difference2:             乖離
  difference_reason:       乖離理由
  difference_reason_class: 乖離理由を選択
  difference_reason_memo:  備考
  difference_setting:      乖離設定
  differences_admin_self:  自分のみ
  discard:                 破棄する
  notif_destination:       通知先
  department:              部署
  dependency:              依存関係
  disconnect:              解除
  disconnect_integration:  連携解除
  department_name:         部署名
  display:                 表示
  display_by_hierarchy:    階層別で表示
  display_order:           表示順
  display_settings:        表示設定
  display_period:          表示期間
  download:                ダウンロード
  duplicate:               複製
  edit:                    編集
  edit_category:           カテゴリの編集
  edit_budget:             予算を編集
  edit_notification:       通知の編集
  end:                     終了
  end_date:                終了日
  end_date_shorten:        終了日
  end_of_month:            月末
  end_time:                終了時刻
  email:                   メールアドレス
  employ_type:             雇用形態
  employ_type_master:      雇用形態マスタ
  employment_status:       在籍状況
  example:                 例
  excel:                   Excel
  expand:                  広げる
  expense:                 経費
  expense_management:      経費の管理
  export:                  エクスポート
  export_all:              全件エクスポート
  export_type:             エクスポート種別
  id:                      ID
  integer:                 整数
  integration:             外部連携
  integration_name:        連携名
  integrations_settings:   外部連携
  integrations_services:   外部連携サービス
  integrated_attendance:   外部勤怠連携
  failure:                 失敗
  favorite:                お気に入り
  feature:                 機能
  filter:                  フィルタ
  filter_search:           絞り込み検索
  gantt:                   ガント
  ganttchart:              ガントチャート
  general:                 全般
  generate_summary:        要約を生成
  greater_than:            超過
  google_extension_ver:    Google Chrome版
  gross_profit:            売上総利益
  help:                    ヘルプ
  highlight:               色付け表示
  holiday_master:          祝日マスタ
  holiday_type:            休暇種別
  home:                    ホーム
  hour:                    時
  hour_or_less:            時間未満
  time_unit:               時間
  try_it:                  試してみる
  history:                 履歴
  hours:                   時間
  in_progress:             進行中
  input_attendance:        勤怠情報を入力
  input_rate:              入力率
  input_work:              工数を入力
  difference:              差分
  import:                  インポート
  items_to_be_exported:    エクスポートする項目
  ja_extended:             日本語 (EUC-JP)
  ja_shift:                日本語 (Shift-JIS)
  job_popup:               ジョブポップアップ
  kanban:                  カンバン
  key:                     キー
  keyword:                 キーワード
  last_day:                末日
  last_updatetime:         最終入力日時
  later:                   後
  laern_more:              詳細を確認
  less_than:               未満
  level_n:                 '{{level}}階層'
  list:                    リスト
  logout:                  ログアウト
  lower_row:               下段
  ongoing:                 進行中
  other:                   その他
  other_functions:         その他の便利機能
  owner:                   責任者
  owner_code:              責任者コード
  mail:                    メール
  man_day:                 人日
  man_hour:                人時
  man_minute:              人分
  man_month:               人月
  manage:                  管理
  manage_api:              APIを管理する
  management_code:         管理コード
  management_code_shorten: 管理コード
  management_code_update:  管理コード更新
  master_setting:          マスタ設定
  match:                   一致
  member:                  メンバー
  member_summary:          メンバーサマリ
  member_department:       メンバー部署
  member_fields:           メンバーの項目
  memo:                    メモ
  menu:                    メニュー
  microsoft_extension_ver: Microsoft Edge版
  milestone:               &milestone マイルストーン
  milestone_add:           マイルストーンを追加
  milestone_name:          マイルストーン名
  milestone_edit:          マイルストーンを編集
  minute:                  分
  minute_unit:             分
  month:                   月
  month_unit:              ヶ月
  monthly:                 月次
  more:                    以上
  ms_teams:                Microsoft Teams
  multi_gantt:             マルチガントチャート
  multi_pie_chart:         複数円グラフ
  my_attendance_export:    マイ勤怠エクスポート
  my_pattern:              マイパターン
  my_report:               マイレポート
  my_timesheet_export:     マイタイムシートエクスポート
  my_timesheet_report:     マイタイムシートレポート
  my_work_report:          マイ工数レポート
  name:                    名前
  member_name:             メンバー名
  narrow_down:             絞り込み
  normal:                  通常
  notif_content:           通知する内容
  new:                     新
  new_notification:        新しい通知
  new_ganttchart:          新ガントチャート
  new_process_group:       工程グループ
  no_category:             カテゴリなし
  no_operation:            無操作
  not_active:              非アクティブ
  not_allow:               許可しない
  not_displayed:           非表示
  not_now:                 今はしない
  not_selected:            未選択
  not_sorted:              並び替えなし
  notice:                  お知らせ
  not_use:                 使用しない
  notice_admin:            お知らせ管理
  notifications:           通知
  notified_events:         通知するイベント
  notification_name:       通知名
  notification_type:       種別
  notification_delete:     通知の削除
  notification_settings:   通知設定
  notification_subject:    件名
  number:                  数字
  numbers_of_per_page:     表示件数
  ok:                      決定
  old:                     旧
  on_time:                 遅延なし
  overdue:                 遅延あり
  participants:            関係者
  pc_log:                  PCログ
  pc_management:           PC管理
  pc_management_top:       PC管理TOP
  performer:               実行者
  period:                  期間
  permission_master:       権限マスタ
  personal_report:         マイ工数レポート
  personal_setting:        個人設定
  personal_work:           マイ工数エクスポート
  pie_chart:               円グラフ
  predecessor:             先行
  process:                 工程
  process_1:               工程1
  process_2:               工程2
  process_3:               工程3
  process_category:        工程カテゴリ
  process_delete:          工程の削除
  process_group_delete:    工程グループの削除
  process_category_delete: カテゴリの削除
  process_group:           工程グループ
  process_group_name:      工程グループ名
  processes_included:      含まれている工程
  process_name:            工程名
  process_master:          工程マスタ
  profit:                  損益
  profit_and_asset:        損益・資産
  profit_by_customer:      顧客別損益
  profit_rate:             損益率
  progress:                進行状況
  progress2:               進捗
  progress_rate:           進捗率
  progress_status:         進行状況
  project:                 プロジェクト
  project_business:        プロジェクトカテゴリ
  project_customer:        プロジェクト取引先
  project_department:      プロジェクト部署
  project_fields:          プロジェクトの項目
  project_field_master:    プロジェクト項目マスタ
  project_list:            プロジェクト一覧
  project_index:           プロジェクト一覧
  project_basic_info:      プロジェクトの基本情報
  project_code:            プロジェクトコード
  project_name:            プロジェクト名
  project_owner:           プロジェクト責任者
  project_slash_task:      プロジェクト / タスク
  project_summary:         プロジェクトサマリ
  project_work:            プロジェクト工数
  project_field:           プロジェクト項目
  project_type:            プロジェクトの種類
  project_type_in_use:     あり
  project_management:      プロジェクト管理
  ratio:                   入力率
  register:                登録
  registered:              登録済
  rejected:                差戻
  rejected2:               却下
  rejected_comment:        差戻コメント
  related_functions:       関連機能
  remind_notif:            リマインド通知
  report:                  レポート
  report_history:          レポート履歴
  report_name:             レポート名
  report_top:              レポートトップ
  required_time:           所用時間
  reset:                   リセット
  rest:                    休憩
  rest_allday:             全休
  rest_am:                 午前休
  rest_pm:                 午後休
  result:                  実績
  sales:                   売上
  sales_management:        売上の管理
  sales_status_master:     売上ステータスマスタ
  save:                    保存
  save_as:                 別名で保存
  save_duration:           期間を保存
  save_or_export:          保存やエクスポート
  select_department:       部署を選択
  select_process:          "{{processName}} を選択"
  select_project:          プロジェクトを選択
  select_time:             時間を選択
  search:                  検索
  search_condition:        検索条件
  secret_access_key:       シークレットアクセスキー
  send_immediately:        即時送信
  send_test:               テスト送信
  settings:                設定
  show_multiple_pie_chart: 複数円グラフを表示
  show_only_members_with_differences: 乖離があるメンバーのみを表示
  show_ruled_lines:        縦軸を表示
  slack:                   Slack
  subtotal:                小計
  success:                 成功
  success_with_warning:    成功（通知事項あり）
  successor:               後続
  sum_line_chart:          累積推移グラフ
  stacked_bar_chart:       積み上げ棒グラフ
  combo_chart:             折れ線グラフ
  sort:                    並び替え
  sort_asc:                昇順で並べ替え
  sort_desc:               降順で並べ替え
  start:                   開始
  start_date:              開始日
  start_date_shorten:      開始日
  start_time:              開始時刻
  state:                   状態
  status:                  ステータス
  stop_watch:              ストップウォッチ
  stop_watch_start:        スタート
  stop_watch_stop:         ストップ
  stop_watch_apply_to_tst: タイムシートへ反映する
  subscription_on:         受け取る
  subscription_off:        受け取らない
  summary_of_the_report_results: レポート結果の要約
  switch_horizontal_axis:  横軸の切り替え
  sync:                    同期
  sync_end_datetime:       同期終了日時
  sync_start_datetime:     同期開始日時
  sync_attendance:         勤怠を同期
  sync_external_services:  同期する外部連携サービス
  sync_status:             同期処理ステータス
  sync_duration:           同期する期間
  sync_with_calendar:      カレンダーと同期
  target_period:           対象期間
  task:                    &task タスク
  task_add:                &task_add タスクの追加
  task_create:             タスクの作成
  task_edit:               タスクの編集
  task_fields:             タスクの項目
  task_group:              &task_group タスクグループ
  task_group_add:          &task_group_add タスクグループの追加
  task_group_edit:         タスクグループの編集
  task_group_name:         タスクグループ名
  task_list:               タスク一覧
  task_name:               タスク名
  task_process:            タスク工程
  task_process_group:      タスク工程グループ
  this_month:              今月
  time:                    時間
  time_required:           所要時間
  timesheet:               タイムシート
  timesheet_approval:      タイムシート承認
  timesheet_approval_flow: 承認フロー
  timesheet_edit_dialog:   タイムシート編集画面
  timesheet_report:        タイムシートレポート
  timesheet_report_notif:  タイムシートレポート通知
  timesheet_to_be_notified: 通知対象のタイムシート
  timesheet_usage:         タイムシート利用状況
  timesheet_usage_report:  タイムシート利用状況レポート
  title_master:            役職マスタ
  base_date:               基準日
  title:                   役職
  today:                   今日
  todo:                    未着手
  total:
      type1:               合計
      type2:               計
  total_items:             合計{{quantity}}件
  total_unit:              集計単位
  total_bar_chart:         合計棒グラフ
  type:                    種別
  unapply:                 取消
  unicode:                 Unicode
  unit:                    単位
  unregistered:            未登録
  update:                  更新
  upper_row:               上段
  upgrade_now:             アップグレード
  url:                     URL
  use:                     使用する
  use_count:               件数取得
  user:                    メンバー
  user_contract:           雇用形態
  user_code:               社員コード
  user_customer:           所属
  user_department:         メンバー部署
  user_title:              役職
  unmatch:                 不一致
  webhook:                 Webhook
  week:                    週
  weekly_timesheet_report: 週次タイムシートレポート
  work:                    工数
  work_add:                工数を追加
  work_and_budget:         工数・予算
  work_and_budget_prev:    旧工数ダッシュボード
  work_attendance_match:   勤怠工数一致
  work_budget_actual:      工数予実
  work_cost:               工数原価
  work_content:            作業内容
  work_output_unit:        工数出力単位
  work_management:         工数予算の管理
  work_budget:             工数予算
  work_report:             工数レポート
  work_report_save:        レポートを保存
  work_report_sync_date:   レポートの更新日
  work_report_sync:        レポートを更新
  work_time:               工数
  work_total:              合計
  work_in_total:           工数合計
  worktime_input_omission_remind_notif: 入力漏れリマインド通知
  working:                 勤務
  working_hours:           労働時間
  year:                    年
  zh_simplified:           中国語 (簡体字)
  whole_day_off:           全休
  morning_day_off:         午前休
  afternoon_day_off:       午後休
  not_specified_day_off: なし
common:
  service_name: クラウドログ
msg:
  add_dimension_locked: 2列目以降はプレミアムプランでご利用いただけます
  add_external_integration_service: 外部連携サービスを追加
  assigned_user_is_invalid: 無効なユーザーが設定されています
  atleast_one: "{{this}}もしくは{{that}}"
  attendance_differences:
    difference_between_start_and_end_is_focus: 開始時刻・終了時刻の乖離が対象です。
    attendance_or_pclog_is_not_collected1: 勤怠もしくはPCログが未確定の状態です
    attendance_or_pclog_is_not_collected2: 勤怠：終了時刻を入力してください
    attendance_or_pclog_is_not_collected3: PCログ：ご利用のPCにログインしてください
  browser_extensions_convenience: ブラウザ拡張機能でより便利に！
  chart:
    error:
      no_data: チャート生成エラー。 データが存在しません。
  check_all: 全てチェック
  click_generate_summary: '「{{button}}」をクリックしてを工数レポートを要約してください。'
  click_generate_summary_to_regenerate: 'いくつかのパラメータが更新されました。「{{button}}」をクリックして、工数レポートの新しい要約を生成してください。'
  copied_data: "<0>{{data}}</0>をコピーしました"
  create_notification: "{{button}}をクリックして、新しい通知を作成します。"
  custom_report:
    fetch_failed: カスタムレポートの取得に失敗しました。
    restore_failed: 存在しないカスタムレポートが選択されたため、復元に失敗しました。
    change_settings_success: カスタムレポートの設定を正常に変更しました。
    change_settings_failed: カスタムレポートの設定の変更に失敗しました。
    update_success: カスタムレポートが正常に更新されました。
    update_failed: カスタムレポートの更新に失敗しました。
    save_success: カスタムレポートが正常に保存されました。
    save_failed: カスタムレポートの保存に失敗しました。
    not_save: 現在ご利用いただけません
    delete_success: カスタムレポートが正常に削除されました。
    delete_failed: カスタムレポートの削除に失敗しました。
    report_limit: 保存できるカスタムレポートの数は、最大 {{max}} 件のレポートのみです。
    fetch_failed_fav: お気に入りのレポートを取得できませんでした。
    save_success_fav: お気に入りに追加されました。
    save_failed_fav: お気に入りに追加できませんでした。
    delete_success_fav: お気に入りレポートは正常に削除されました。
    delete_failed_fav: お気に入りレポートの削除に失敗しました。
    already_marked: このレポートはすでにお気に入りに追加されています。
    sort_custom_success: カスタムレポートの並び順を変更しました。
    sort_custom_failed: カスタムレポートの並べ替えに失敗しました。
    sort_favorite_success: お気に入りの並び順を変更しました。
    sort_favorite_failed: お気に入りレポートの並べ替えに失敗しました。
    example_case: の場合
    scope_of_aggregation: "集計範囲："
    example_december: "12 月"
    example_january: "1 月"
    monthly_available_only: 月次の場合のみ有効です
  delete_selected_notifications: 選択した通知を削除します。
  default_api_error:   サーバエラーが発生しました。
  delete_confirm:      削除します。よろしいですか?
  delete_confirm_with_name_and_date: "{{name}}の{{date}}のデータを削除しますか？"
  delete_confirmation_for_report: 保存したレポート「{{data}}」を削除しますか？
  delete_process_category_confirm:
    line1: 「{{data}}」のカテゴリを削除しますか？
    line2: 削除後、紐づく工程のカテゴリは「{{empty}}」に変更になります。
  delete_process_confirm: "{{userName}}さんの{{date}}のデータを削除しますか？"
  deleted:             削除しました。
  deleted_data:        "<0>{{data}}</0>を削除しました。"
  delete_failed:       削除に失敗しました。
  detail_search:       詳細な条件で絞り込む
  display_work_budget: 予算を表示
  display_work_budget_requirements:
    dimension:
      must_select_either: 'メンバー関連の項目と{{dimension}}のどちらか一方'
      must_not_selected: '{{dimension}}が未指定'
      must_not_selected_two: '{{dimension1}}および{{dimension2}}が未指定'
  do_not_reload_page:  ページ遷移やリロードをしないで下さい。
  do_not_show_this_notice_again: このお知らせを今後表示しない
  end_project_process_budget: 大変申し訳ありません、この機能は2024年9月5日(木)に廃止予定となっております。詳細は<helpLink>こちら</helpLink>をご確認ください
  enter_integration_info: 連携する情報を入力してください。
  export:
    failed:            エクスポートに失敗しました。
    succeed:           エクスポートが完了しました
    exporting:         エクスポートを実行中です
    not_authorized:    エクスポートする権限がありません
    limit_per_page:    エクスポート可能なデータの最大件数は{{max}}件です。先頭から{{max}}件までエクスポートしました。
  failed_copy_data:    "<0>{{data}}</0>のコピーに失敗しました"
  failed_delete_data:    "<0>{{data}}</0>の削除に失敗しました"
  failed_save_data:    "<0>{{data}}</0>の保存に失敗しました"
  feature_not_available: この機能はまだご利用できません
  form:
    confirmation:
      discard_updates: 更新されたデータが破棄されますがよろしいですか？
  generate_summary_failed: 要約の生成に失敗しました。
  generate_summary_succeed: 要約が生成されました。
  integrations_settings:
    auto_integ:
      form_title: 自動同期の設定
      form_details:
        - 使用すると、全対象者の過去40日分の勤怠情報を、システム側で毎日05:00に自動で同期します。
        - ※この設定はいつでも変更できます。
      info: 連携すると、全対象者の過去40日分の勤怠情報を、システム側で毎日05:00に自動で同期します。この設定は、画面上の「設定」ボタンよりいつでも変更できます。
      setup_finished:
        jobcan: ジョブカン設定 ({{settingName}}) を更新しました。
        kot: KING OF TIME設定 ({{settingName}}) を更新しました。
        kinkakuji: 勤革時の設定 ({{settingName}}) を更新しました。
        hrmos: HRMOSの設定 ({{settingName}}) を更新しました。
      unable_to_update: システムエラーにより現在この設定は変更できません。恐れ入りますが、しばらく経ってから再度お試しください。
    connected: '{{sysName}}設定 ({{settingName}}) と連携しました。'
    connection_failed: '{{name}}との連携に失敗しました。'
    connected_detail:
      jobcan: 管理メニュー内「勤怠」画面より勤怠の同期が行なえます。
      kot: 管理メニュー内「勤怠」画面より勤怠の同期が行なえます。
      kinkakuji: 管理メニュー内「勤怠」画面より勤怠の同期が行なえます。
      hrmos: 管理メニュー内「勤怠」画面より勤怠の同期が行なえます。
    desc:
      jobcan: ジョブカンの勤怠を同期、勤怠同期の実行履歴一覧の閲覧が可能になります。
      kot: KING OF TIMEの勤怠を同期、勤怠同期の実行履歴一覧の閲覧が可能になります。
      kinkakuji: 勤革時の勤怠を同期、勤怠同期の実行履歴一覧の閲覧が可能になります。
      hrmos: HRMOSの勤怠を同期、勤怠同期の実行履歴一覧の閲覧が可能になります。
    disconnected: '{{name}}との連携を解除しました。'
    disconnection:
      jobcan:
        - 連携を解除すると勤怠の同期は行えません。
        - 過去に同期済みの勤怠は変わりません。
      kot:
        - 連携を解除すると勤怠の同期は行えません。
        - 過去に同期済みの勤怠は変わりません。
      kinkakuji:
        - 連携を解除すると勤怠の同期は行えません。
        - 過去に同期済みの勤怠は変わりません。
      hrmos:
        - 連携を解除すると勤怠の同期は行えません。
        - 過去に同期済みの勤怠は変わりません。
    disconnection_failed: '{{name}}との連携解除に失敗しました。'
    form_instruction: 下記の情報を入力し、{{name}}と連携してください。
    form_id_placeholder: '{{name}}で設定したID'
    form_secret_key_placeholder: '{{name}}で設定したシークレットアクセスキー'
    limit_has_been_reached: 登録上限に達しているため、{{sys}}設定を登録できません。<br />
                            登録可能な{{sys}}設定は{{num}}個までです。
    unable_to_connect_jobcan: 連携に失敗しました。IDもしくはSecretが正しくありません。
    unable_to_connect_kot: 連携に失敗しました。アクセストークンが正しくありません。
    unable_to_connect_hrmos: 連携に失敗しました。URLの会社名もしくはAPI KEYが正しくありません。
  integration_attendance:
    summaries:
      failure: システム上のエラーにより同期処理を実行できませんでした。 詳細は<helplink>ヘルプページ</helplink>を参照してください。
      running: 同期処理中
      success_with_warning: 予期しない同期結果が含まれている可能性があります。 詳細は<helplink>ヘルプページ</helplink>を参照してください。
    errors:
      cannot_convert_attendance: "勤怠を変換できませんでした"
      attendance_synchronized_by_another_condition_existed: "他の条件で同期済みの勤怠が存在していました"
      integration_attendance_access_prohibit_error: "「.+」は.+の間利用できません"
  following_projects_cannot_be_deleted: 以下のプロジェクトは、プロジェクト編集権限が付与されていないため、削除できません。
  following_projects_cannot_be_exported: 以下のプロジェクトは、エクスポートに必要な権限が付与されていないため、エクスポートできません。
  force_delete_keyword_mismatch: 入力した確認用文字列に誤りがあるため、削除はキャンセルされました。
  force_delete_project:
    introduction: 選択されたプロジェクトを強制的に削除します。よろしいですか?
    prompt_confirmation: 一度削除した情報は復元することはできません。以下の項目を確認のうえ、削除を実行してください。
    warnings:
      - 選択されたプロジェクトのすべての売上が削除されます。
      - 選択されたプロジェクトのすべての原価が削除されます。
      - 選択されたプロジェクトのすべての経費予算が削除されます。
      - 選択されたプロジェクトのすべての経費実績が削除されます。
      - 選択されたプロジェクトのすべてのガントチャートタスクが削除されます。
      - 選択されたプロジェクトのすべてのタイムシート（工数実績）が削除されます。
      - 選択されたプロジェクトのすべての工数予算が削除されます。
      - 選択されたプロジェクトのすべての工数原価実績が削除されます。
    if_not_want_to_delete_data: 登録されたデータを削除したくない場合はプロジェクトを非アクティブにしてください。
    requirement: 削除するには権限マスタで権限が付与されているか、システム管理者である必要があります。
    prompt_input_delete: "'DELETE'を入力してください"
  update_external_integration_service: 外部連携サービスを更新
  integrated_attendance:
    now_integrated_with: "勤怠情報が{{targetSystem}}と連携されています。"
    input_manually: "手動で入力する"
    attendance_tips: "手動で入力した勤怠情報は、申請を行わない限り、{{targetSystem}}との連携実行時更新されます。"
    attendance_diff_exists: "連携された勤怠情報と差分があります"
    jobcan: "ジョブカン"
    kot: "KING OF TIME"
    kinkakuji: "勤革時"
    hrmos: "HRMOS"
    integration_attendance_failed: "勤怠の同期を開始できませんでした。"
    integration_attendance_is_submitted: "勤怠の同期を開始しました。この処理が完了するまで、次の勤怠の同期は行なえません。"
  mark_as_favorite: お気に入りに追加
  move_date_direction: "{{date}}{{unit}}{{direction}}に移動します"
  multi_pie_chart:
    display_condition: 複数円グラフを表示できる条件
  project_delete:
    failed:            削除に失敗したプロジェクトがあります
    succeed:           削除が成功しました。
  projects_that_could_not_be_deleted: 削除できなかったプロジェクト
  projects_that_could_not_be_exported: エクスポートできなかったプロジェクト
  download:
    failed:    ダウンロードに失敗しました。時間をおいてから実行してください。
    preparing: ダウンロード準備中
  filter_start_end_date_any_period: 開始日/終了日を任意の期間で絞り込み
  new_feature_released: 新しい機能がリリースされました。
  no_available_items: 利用可能な{{name}}がありません
  no_matches_found: 一致するものが見つかりません
  no_matching_results_found: 一致する検索結果はありませんでした
  no_member_assigned: メンバーが設定されていません
  no_options: データがありません
  no_options_business: カテゴリ名を入力してください
  no_options_customer: 取引先名を入力してください
  no_options_department: 部署名を入力してください
  no_options_obj: "{{obj}}を入力してください"
  no_options_project: プロジェクト名を入力してください
  no_options_process: プロセス名を入力してください
  no_options_task_status:  ※プロジェクトを選択してください
  no_permission:
    header: このページにアクセスするための権限がありません。
    line1:  権限マスタにて、権限の設定が必要となります。
    line2:  "%title_key についてもっと知る。"
  not_allowed_to_view_data: "表示する権限がありません"
  not_found:
    header: Not Found
    line1:  ページが見つかりません。
    line2:  入力したURLが正しいことを確認してください。
  outlook_extension:
    calendar_sync:
      no_sync_code: この工数は同期できません。管理コードが設定されていません。 ({{data}})
  pj_timeline_advertising_title: プロジェクト毎の予実管理が<br />圧倒的に簡単・便利に。
  pj_timeline_advertising_items:
    - プロジェクト全体での予算・実績の把握
    - 時系列での予算・実績の把握
    - プロジェクトの開始・終了と工数の発生タイミングをビジュアルで把握
  plan:
    integration_attendance_title: 外部勤怠連携機能で<br />工数管理をより正確に！
    integration_attendance_details:
      - 利用中の勤怠管理システムと連携し、勤怠情報をクラウドログに同期
      - 実労働時間に合わせて工数管理をより正確に
  process:
    success_save: 保存しました。
    no_data: 新規作成より工程を作成できます。
    delete_modal_message: 工程を削除しますか？<br />一度削除した工程は復元することが出来ません。
    success_delete: 工程を削除しました。
    category_success_delete: 工程カテゴリを削除しました。
    failed_delete: マスタが使用中のため、工程を削除できません。
    failed_update_active: 工程グループで使用している工程を非アクティブにすることはできません。
    new_process_name: 新しい工程名
    table_update_refresh: 画面をリロードすると、マスタを最新状態にします。
  process_group:
    no_data: 新規作成より工程グループを作成できます。
    delete_modal_message: 工程グループを削除しますか？<br />一度削除した工程グループは復元することが出来ません。
    success_delete: 工程グループは削除に成功しました。
    failed_delete: マスターが使用中のため、工程グループを削除できません。
    add_process: "{{process}}を追加"
    form_search: 工程名、カテゴリ名で検索
  prepend_api_error:     問題が発生しました。
  process_not_active: 非アクティブの工程です。
  process_not_assigned_to_project: このプロジェクトにアサインされていない工程です。
  remaining_number_of_generation: "今月の残りの生成回数: {{remainingUsage}} / {{limit}}"
  remaining_number_of_generation_description: 生成数は企業内の合計でカウントされます
  reset_to_default:      初期設定に戻す
  restrictions:
    lets_upgrade: アップグレードして機能を利用しましょう！
    plan_restricted: 現在のプランではこの機能を利用できません。
  save_data:             "{{data}}を保存"
  saved_data:            "<0>{{data}}</0>を保存しました。"
  saved:                 保存しました。
  save_confirm:          保存します。よろしいですか?
  save_failed:           保存に失敗しました。
  saving:                保存しています
  select_date: 日付を選択
  select_display_settings: 一覧に表示したい項目を選択してください。
  select_end_date: 終了日を選択
  select_integration_service: 連携するサービスを選択してください。
  select_one_or_more: 項目を一つ以上選択してください。
  select_start_date: 開始日を選択
  send_remind_result_to_title_user: 役職者へサマリ通知を送信
  send_remind_result_to_title_user_desc: サマリ通知では、リマインド通知が送信されたメンバーを確認できます。<br />この通知には、所属部署のメンバーに関する情報のみ含まれます。
  send_immediately_in_bulk: まとめて即時送信
  success_send_immediately: 即時送信が完了しました。
  failure_send_immediately: 即時送信に失敗しました。
  success_send_immediately_in_bulk: まとめて即時送信が完了しました。
  failure_send_immediately_in_bulk: まとめて即時送信に失敗しました。
  show_assigned_project: 自分がアサインされているプロジェクトのみ
  show_only_active_members: 在籍メンバーのみ表示する
  show_only_in_progress_projects: 現在進行中のプロジェクトのみ表示する
  summarize_n_or_more_data_as_other: "{{dataLimit}}件以上はその他にまとめる"
  combo_chart_title: "{{dimension1st}} に対する {{dimension2nd}} の割合"
  combo_chart_search_condition_mismatch: "{{dimension1st}}に一つ、{{dimension2nd}}にも一つ項目を検索フォームで選択してください"
  combo_chart_dimensions_condition_mismatch: 集計軸を二つ選択してください
  combo_chart_condition_mismatch: 申し訳ございません、現在のデータではグラフの表示を行えません
  guide_to_help_pages: 詳しくはこちら
  switch_to_old_design:  旧デザインに戻す
  sync_attendance:
    success: 勤怠を同期しました。
    failed: 出席の同期に失敗しました。
    synchronizing: 同期の処理中
    synchronizing_detail:
      - 勤怠同期の処理中です。
      - 処理が完了するまでは次の同期の実行は行なえません。
      - 処理が完了するまでしばらくお待ち下さい。
  unmark_as_favorite: お気に入りから削除
  updated_data:            "<0>{{data}}</0>を更新しました。"
  notification_send_test:
    success:    テストメッセージが正常に送信されました。
    in_progress: テストメッセージを送信しています。
    failed:     テストメッセージの送信に失敗しました。
  or_confirm_our_plan: または、<0>プランを確認</0>
  out_of_project_period: プロジェクト期間外です。
  out_of_user_period: ユーザー期間外です。
  timesheet:
    input_daily_report: 日報を入力
    add_memo: メモを追加
    apply_failed: タイムシートの申請に失敗しました。
    apply_message: 以下のタイムシートを申請します。
    apply_success: タイムシートの申請に成功しました。
    apply_cancel_success: タイムシート申請の取消に成功しました。
    apply_cancel_fail: タイムシート申請の取消に失敗しました。
    apply_warning: 申請できなかったタイムシートがあります。
    apply_this_date: この日のタイムシートを申請しますか？
    unapply_this_date: この日のタイムシート申請を取り消しますか？
    calendar_sync_not_determined: カレンダー同期が未確定です
    delete_memo: メモを削除
    executing_fill_available_time: まとめて登録を実行中です
    save_success: タイムシートの保存に成功しました
    save_failed: タイムシートの保存に失敗しました
    delete_success: タイムシートの削除に成功しました
    delete_failed: タイムシートの削除に失敗しました
    no_permission: このユーザーのタイムシートを閲覧する権限がありません
    cannot_used_work: 現在この{{work}}は使用できません
    is_approved: 承認済のタイムシートのため編集できません。
    is_pending: 申請中のタイムシートのため編集できません。
    is_unmatch: 勤怠と工数が一致していないタイムシートがあります。
    calendar_sync:
      success: カレンダー同期が完了しました。
      failed: カレンダー同期に失敗しました。
      no_events: 同期する予定がありませんでした。
      synchronizing: カレンダーを同期しています。
      has_temp_work: 未確定の工数があります。
      has_temp_work_body: カレンダー連携コードが入力されていない工数があります。プロジェクト・工程またはタスクを入力してください。
      select_account: 別のアカウントで同期
      no_sync_code: 管理コードが設定されていません。({{data}})
    copy: コピー
    copy_from: コピー元
    copy_to: コピー先
    copy_to_date: コピー先の日付
    copy_not_executed: コピーが実行されませんでした
    copy_no_more_date: これ以上日付を追加できません
    copy_completed: コピーが完了しました
    copy_date_select: コピー先の日付を選択してください
    copy_add_date: コピー先の日付を追加
    copy_overwrite_attendance: 休暇・出退勤・休憩を上書きする
    process_not_selected: 工程が選択されていません
    try_new_timesheet_now: 新タイムシートを試してみる
    questionnaire:
      button_label_1: 新タイムシートに関する
      button_label_2: ご意見はこちら
    stop_watch:
      sw_add_new: ストップウォッチを追加
      sw_status_conflict: 状態が古くなっています。画面をリロードしてください。
      sw_select_work_and_apply: 計測内容をタイムシートへ反映できません。作業内容を設定し「タイムシートへ反映する」ボタンを押してください。
      sw_correct_memo_and_apply: メモの長さを200文字以内に設定し、タイムシートへ反映してください。
      sw_apply_to_tst_successfully: タイムシートへの保存に成功しました
      sw_need_to_reset_manually: タイムシートへの保存に成功しました。次の計測を開始するためには、リセットボタンを押してください。
      sw_cannot_start_timing: 。ストップウォッチはスタートできません。
      sw_fail_to_add_new: ストップウォッチの追加に失敗しました。
      sw_limit_max_stop_watch_count: "ストップウォッチの追加は最大{{num}}個までです。"
      sw_exists_another_with_timing: ほかの計測中のストップウォッチがあります。画面をリロードしてください。
    shortcut_guide:
      title: ショートカットキーガイド
      change_date_to_today: 日付を今日に変更
      change_to_date_view: 日ビューに変更
      change_to_week_view: 週ビューに変更
      change_to_month_view: 月ビューに変更
      show_next_time_range: 次の期間を表示
      show_previous_time_range: 前の期間を表示
      change_to_calendar_view: カレンダービューに変更
      change_to_list_view: リストビューに変更
      zoom_in_or_out_time_slot: メモリをズームインまたはアウト
    timekeeper_integration:
      register_message: この内容で工数登録しますか？
      resgister: 登録する
      not_register: 登録しない
    use_work_suggestion: 工数提案機能を使う
  timesheet_detail_fail: タイムシート詳細の取得に失敗しました
  there_are_data_without_permission: 権限がなく、<0>{{data}}</0>があります。
  this_feature_is_available_in_premium: この機能は、プレミアムプランでお使いいただけます
  user_not_assigned_to_project: このプロジェクトにアサインされていないユーザーです。
  unapproved_attendance: 未確定の打刻があります。
  unapproved_timesheet:  未確定のタイムシート承認があります。
  upgrade_premium_plan: プレミアムプランのみ
  validation:
    at_least_one_checked: 1つ以上選択してください。
    greater_than:         "{{num}}より大きい数字の値を入力してください。"
    greater_than_or_equal: "{{num}}以上の値を入力してください。"
    less_than_or_equal:   "{{num}}以下の値を入力してください。"
    input_obj:            "{{obj}}を入力してください。"
    invalid_url:          無効なURLです。
    invalid_input:        無効な入力値です。
    invalid_start_end:    開始時刻と終了時刻の範囲が無効です
    invalid_work_memo:    メモの長さは最大でも200文字でなければなりません
    max_length:           "{{data}}の長さは最大でも{{length}}文字でなければなりません。"
    must_be_between:       入力値は{{from}}〜{{to}}の範囲内である必要があります。
    out_of_range_time:    入力可能時間の範囲外です
    invalid_work_time:    無効な工数入力単位です
    less_than_work_time_unit: 工数入力単位未満の時間になっています
    gte_date:             "{{end}}は{{start}}以降の日付にしてください"
    required:             必須項目です。
    starts_with:          「%s」から始まる必要があります。
  work_check_error: 工数予算、工数実績、工数原価予算、工数原価実績のうちどれか一つをチェックしてください。
  work_budget:
    display_condition: 予算を表示出来る条件
    not_available: このグラフには対応していません
    cannot_edit_due_to_gantt: ガントを利用中のため編集できません
    change_setting_unit: 工数予算の設定単位を変更
    confirm_change_to_member: 工数予算の設定単位を「メンバー」に変更しますか？
  work_report_sync:
    for_details: 詳細はこちら
    real_time_report: リアルタイム集計
    sync_rules_first: 工数レポートは事前に集計したデータを表示しています
    sync_rules_second: 「更新」ボタンを押すと再集計します
    sync_is_in_progress: 更新の処理中です。完了後、再度実行してください。
    sync_has_started: 更新処理を開始しました
    sync_failed_to_start: 更新処理に失敗しました
  workflow_timesheet_setting:
    can_be_added_by_clicking_member: メンバー追加をクリックして、除外メンバーを追加できます。
    success_add_excluded_user: 除外メンバーに追加しました。
    failed_add_excluded_user: 一部メンバーの追加に失敗しました({{n}}件)。 再度、除外メンバーへの追加設定をおこなってください。
    success_delete_excluded_user: 除外メンバーから削除しました。
    failures_delete_excluded_user: 一部メンバーの削除に失敗しました({{n}}件)。再度、除外メンバーの削除設定をおこなってください。
placeholder:
  all_belong: すべての所属
  all_departments: すべての部署
  all_employ_types: すべての雇用形態
  all_titles: すべての役職
  all_members: すべてのメンバー
  all_process: すべての工程
  all_process_categories: すべての工程カテゴリ
  attendance_difference_filter: メンバー名、メールアドレス、社員コード
  entity_used_in_name: "{{name}}で利用する{{entity}}"
  identification_name: 識別用の名前
  input_comment: コメントを記入
  input_milestone_name: マイルストーン名を入力
  input_data: "{{data}}を入力"
  input_search_conditions: 検索条件を入力
  input_task_group_name: タスクグループ名を入力
  input_task_name: タスク名を入力
  member_filter: コード、メンバー名、メールアドレス
  timesheet_usage_filter: 名前、管理コード、メール
  process_filter: 工程名、管理コード
  project_filter:   コード、プロジェクト名
  notification_filter: 通知名を検索
  task_filter: コード、タスク名
  timesheet_report_filter: メンバー名、メールアドレス、メンバーコード
  excluded_modal_filter: 社員コード、メンバー名、メールアドレス
  sign_on_url: サインオンURLを入力
  select_member: メンバーを選択
  select_task_group: タスクグループを選択
  select_task_dependency: タスクまたはマイルストーンを選択
price_plan:
  bsc: { name: ベーシック }
  dem: { name: デモ }
  prm: { name: プレミアム }
  trl: { name: トライアル }
timesheet:
  add_more_work_to_batch_register: 一括登録する内容を追加
  add_rest: 休憩を追加
  apply: 申請
  unapply: 申請の取消
  available_time: 空き時間
  batch_register: 一括登録
  batch_registration_of_work: 工数を一括登録
  batch_registration_method: 登録方法
  delete_with_including_attendance_and_rest_and_dayoff: 休暇・出退勤・休憩を含めて削除
  delete_timesheet_question: "{{date}}のタイムシートを削除しますか?"
  unapply_timesheet_question: "{{date}}のタイムシート申請を取り消しますか?"
  fill_available_time: まとめて登録
  stop_watch: ストップウォッチ
  specify_ratio: "%を指定"
  specify_time: 合計時間を指定
  registered_time: 登録時間
  target_dates: 対象日
  date_not_be_updated: 出退勤が入力されていない、または空き時間がありません。
  select_from: "{{name}}を選択"
  select_from_or: "{{name1}}または{{name2}}から選択"
  select_from_my_pattern: "マイパターンから選択"
  select_from_history: "履歴から選択"
timesheet_report:
  checkbox:
      unmatch: "不一致(<0>{{data}}</0>)があるメンバーのみを表示"
  option:
    highlight_greater_than: 超過をハイライト
    highlight_less_than: 未満をハイライト
global_notices:
  2023_07_ts_improve: 新タイムシートが使いやすくなりました。
  2023_08_design_update: クラウドログのロゴデザインをリニューアルしました。
  2023_08_work_history_and_cancel_applicant: 【機能更新】工数の履歴機能、申請者による申請取り消し機能　他
  2023_09_work_budget_on_work_report: 工数レポートで未来の予算が表示可能になりました。
  2023_10_ts_improve: 【機能更新】新タイムシートの改善を行いました（ジョブポップアップ改善、カレンダー同期機能改善　他）
work_budget:
  about: 工数予算の表示について
  display: 工数予算の表示
  label:
    user_display: メンバー表示
  radio:
    with: 予算がある
    none: 予算が無い
  select:
    cumulative: メンバー
    direct: プロジェクト
  project:
    setting_unit: 工数予算の設定単位
    project_budget: 工数予算
    unallocated_project_budget: 未割り当ての工数予算
    allocated_project_budget: 全メンバーの工数予算
  msgs:
    detail: プロジェクトの種類に応じて表示内容が切り替わります。
    process: この画面で入力した工数予算が表示されます。編集権限を持っている場合は編集できます。
    gantt: タスクに設定されている工数予算の合計値が月ごとに表示されます。編集はできません。
    subtotal: 上記２つの値を合算した値が表示されます。<br />※プロジェクトの種類に工程とガントチャートの両方を選択している場合のみ表示されます。
workflow_timesheet_setting:
  add_excluded_user: 除外メンバーの追加
  add_user: メンバー追加
  application_permission_setting: 申請の許可設定
  confirm_delete_modal_message: 選択したメンバーを「除外メンバー」から削除しますか？
  delete_excluded_user: 除外メンバーの削除
  list_set_excluded_members: 除外メンバーの一覧と設定
  n_users_selected: "{{n}}人選択中"
  worktime_discrepancy_permissibility: 工数と勤怠が不一致の場合の申請制限機能
attendance_difference:
  highlight_difference_greater_than: 以上の乖離をハイライト
  highlight_if_member_has_difference: 乖離があるメンバーをハイライト
  not_required: 不要(乖離なし)
  reason_class:
    others: その他（自由記述）
    pc_used_not_for_work: 業務外でPCの利用があった
    pc_unnecessarily: PC操作が不要な業務があった（外出・移動を含む等）
    pc_log_unreadable: PCログ取得不具合（ネットワーク不良、システム不具合等）
    pc_off_omission: PC停止漏れ（シャットダウン/画面ロック等）
symbol:
  asterisk: ※
  bracket_open: 【
  bracket_close: 】
  quotation_open: 「
  quotation_close: 」
period_up_to:
  today: 今日まで
  yesterday: 昨日まで
  end_of_last_week: 先週末まで
  end_of_last_month: 先月末まで
feedback:
  opinion_request: 新しい{{feature}}へのご意見・ご要望
  opinion_request_for_this_feature: この機能へのご意見・ご要望
outlook_extension:
  open_timesheet: タイムシート画面を開く
  saml_setting_title: SAML設定
project_management_gantt:
  group_delete_unavailable: タスクを含むタスクグループは削除できません。
  context_menu:
    add_same_level: 同階層に追加
    add_same_submenu:
      task: *task
      task_group: *task_group
      milestone: *milestone
    add_submenu:
      task: *task
      task_group: *task_group
      milestone: *milestone


