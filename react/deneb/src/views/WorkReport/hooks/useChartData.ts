import * as React from 'react';
import { useTranslation } from 'react-i18next';
import { isSameDay, format, parseISO } from 'date-fns';

import WorkReportData from '@/interfaces/WorkReportData';
import WorkReportTotal from '@/interfaces/WorkReportTotal';
import { ChartData } from '@/views/WorkReport/interfaces/ChartData';
import { DataLimitKey } from '@/views/WorkReport/interfaces/DataLimit';
import { WorktimeUnit } from '@/views/WorkReport/interfaces/WorktimeUnit';
import { WorkDimension } from '@/views/WorkReport/interfaces/WorkDimension';
import { useWorktimeConv } from '@/views/WorkReport/hooks/useWorktimeConv';
import * as Colors from '@/styles/colors';
import { DurationInterval } from '@/interfaces/DurationInterval';
import { useDurationInterval } from '@/views/WorkReport/hooks/useDurationInterval';
import { MultiplePieChartData } from '@/views/WorkReport/interfaces/MultiplePieChartData';
import { ChartTypeKey } from '@/interfaces/ChartType';

type Props = {
  workReportData: WorkReportData[];
  workReportTotal: WorkReportTotal | undefined;
  worktimeUnit: WorktimeUnit;
  dimensions: WorkDimension[];
  since: Date;
  until: Date;
  durationInterval: DurationInterval;
  dataLimit: DataLimitKey;
  totalOfRecords: number;
  isWorkBudgetDisplayed: boolean;
  showDateAsXAxis: boolean;
  chartType: ChartTypeKey;
  comboWorkReport: WorkReportData[] | undefined;
  comboFetched: boolean;
};

export const useChartData = ({
  workReportData,
  workReportTotal,
  worktimeUnit,
  dimensions,
  since,
  until,
  durationInterval,
  dataLimit,
  totalOfRecords,
  isWorkBudgetDisplayed,
  showDateAsXAxis,
  chartType,
  comboWorkReport,
  comboFetched,
}: Props) => {
  const { t } = useTranslation();
  const labelOfOther = t('other');
  const labelOfBudget = t('budget');
  const labelOfResult = t('result');
  const worktimeUnitConv = useWorktimeConv(worktimeUnit);
  const uniqueLabels = genUniqueLabels(dimensions, workReportData, chartType);

  const generatePieChartData = React.useCallback(
    (wReportData: WorkReportData[], totalRecords: number, isMulti: boolean) => {
      const backgroundColor = isMulti ? [] : Colors.MultiColor21;

      const chartData: ChartData = {
        labels: [],
        datasets: [
          {
            data: [],
            backgroundColor,
          },
        ],
      };

      const workTimesWithUnit: number[] = [];

      const updateChartData = (concatedLegends: string, concatedLabels: string, data: number) => {
        chartLegends.push(concatedLegends);
        chartData.labels.push(concatedLabels);
        chartData.datasets[0].data.push(data);

        if (!isMulti) {
          return;
        }

        const colorIndex = uniqueLabels.findIndex((label) => label === concatedLabels);
        const colorCode = getColor(colorIndex);
        chartData.datasets[0].backgroundColor = Array.isArray(chartData.datasets[0].backgroundColor)
          ? [...chartData.datasets[0].backgroundColor, colorCode]
          : [chartData.datasets[0].backgroundColor, colorCode];
      };

      const updateWorkTimesWithUnit = (workTimeWithUnit: number) =>
        workTimesWithUnit.push(workTimeWithUnit);

      const chartLegends: string[] = [];
      wReportData.forEach((data) => {
        const { concatedLegends, concatedLabels } = isMulti
          ? genLabelsForXAxis(dimensions, data, chartType)
          : genLabels(dimensions, data);
        updateChartData(concatedLegends, concatedLabels, data.total.mins);
        updateWorkTimesWithUnit(data.total[worktimeUnitConv]);
      });

      const isExistingOtherData = totalRecords >= dataLimit;

      if (isExistingOtherData) {
        // delete the tail's data to display chart data for the number of dataLimit including Other
        chartLegends.pop();
        chartData.labels.pop();
        chartData.datasets[0].data.pop();
        workTimesWithUnit.pop();

        const totalOfAllWorkTimeMins = workReportTotal?.total?.mins || 0;
        const totalOfDisplayedWorkTimeMins = wReportData.reduce(
          (prev, curr) => prev + curr.total.mins,
          0
        );

        const isTotalOfRecordsSameAsDataLimit = totalRecords === dataLimit;
        const smallestWorktimeMins = wReportData.length
          ? wReportData[wReportData.length - 1].total.mins
          : 0;

        const otherWorkTimeMins = isTotalOfRecordsSameAsDataLimit
          ? // If totalOfRecords and dataLimit are the same, the dataLimit-th value is used as Other data
            // to display chart data for the number of dataLimit including Other
            smallestWorktimeMins
          : totalOfAllWorkTimeMins - totalOfDisplayedWorkTimeMins + smallestWorktimeMins;

        const totalOfAllWorkTimeUnit = workReportTotal?.total?.[worktimeUnitConv] || 0;
        const totalOfDisplayedDataWorkTimeUnit = wReportData.reduce(
          (prev, curr) => prev + curr.total[worktimeUnitConv],
          0
        );

        const smallestWorkTimeUnit = wReportData.length
          ? wReportData[wReportData.length - 1].total[worktimeUnitConv]
          : 0;
        const otherWorkTimeUnit = isTotalOfRecordsSameAsDataLimit
          ? smallestWorkTimeUnit
          : totalOfAllWorkTimeUnit - totalOfDisplayedDataWorkTimeUnit + smallestWorkTimeUnit;

        updateChartData(labelOfOther, labelOfOther, otherWorkTimeMins);
        updateWorkTimesWithUnit(otherWorkTimeUnit);
      }

      const dataWithUnit = {
        labels: chartData.labels,
        values: workTimesWithUnit,
      };

      return { chartData, chartLegends, dataWithUnit };
    },
    [
      dataLimit,
      dimensions,
      labelOfOther,
      workReportTotal?.total,
      worktimeUnitConv,
      chartType,
      uniqueLabels,
    ]
  );

  const {
    chartData: pieChartData,
    chartLegends: pieChartLegendsLabels,
    dataWithUnit: pieDataWithUnit,
  } = generatePieChartData(workReportData, totalOfRecords, false);

  const {
    chartData: pieChartDataBudget,
    chartLegends: pieChartLegendsLabelsBudget,
    dataWithUnit: pieDataWithUnitBudget,
  } = React.useMemo(() => {
    const chartData: ChartData = {
      labels: [],
      datasets: [
        {
          data: [],
          backgroundColor: Colors.MultiColor21,
        },
      ],
    };

    const workBudgetsWithUnit: number[] = [];

    const updateChartData = (concatedLegends: string, concatedLabels: string, data: number) => {
      chartLegends.push(concatedLegends);
      chartData.labels.push(concatedLabels);
      chartData.datasets[0].data.push(data);
    };

    const updateWorkBudgetsWithUnit = (workBudgetWithUnit: number) =>
      workBudgetsWithUnit.push(workBudgetWithUnit);

    const chartLegends: string[] = [];
    workReportData.forEach((data) => {
      const { concatedLegends, concatedLabels } = genLabels(dimensions, data);
      updateChartData(concatedLegends, concatedLabels, data.total_budget?.mins || 0);
      updateWorkBudgetsWithUnit(data.total_budget?.[worktimeUnitConv] || 0);
    });

    const isExistingOtherData = totalOfRecords >= dataLimit;

    if (isExistingOtherData) {
      // delete the tail's data to display chart data for the number of dataLimit including Other
      chartLegends.pop();
      chartData.labels.pop();
      chartData.datasets[0].data.pop();
      workBudgetsWithUnit.pop();

      const totalOfAllWorkBudgetMins = workReportTotal?.total_budget?.mins || 0;
      const totalOfDisplayedWorkBudgetMins = workReportData.reduce(
        (prev, curr) => prev + (curr.total_budget?.mins || 0),
        0
      );

      const isTotalOfRecordsSameAsDataLimit = totalOfRecords === dataLimit;
      const smallestWorkBudgetMins =
        (workReportData.length && workReportData[workReportData.length - 1].total_budget?.mins) ||
        0;

      const otherWorkBudgetMins = isTotalOfRecordsSameAsDataLimit
        ? // If totalOfRecords and dataLimit are the same, the dataLimit-th value is used as Other data
          // to display chart data for the number of dataLimit including Other
          smallestWorkBudgetMins
        : totalOfAllWorkBudgetMins - totalOfDisplayedWorkBudgetMins + smallestWorkBudgetMins;

      const totalOfAllWorkBudgetUnit = workReportTotal?.total_budget?.[worktimeUnitConv] || 0;
      const totalOfDisplayedDataWorkBudgetUnit = workReportData.reduce(
        (prev, curr) => prev + (curr.total_budget?.[worktimeUnitConv] || 0),
        0
      );

      const smallestWorkBudgetUnit =
        (workReportData.length &&
          workReportData[workReportData.length - 1].total_budget?.[worktimeUnitConv]) ||
        0;
      const otherWorkBudgetUnit = isTotalOfRecordsSameAsDataLimit
        ? smallestWorkBudgetUnit
        : totalOfAllWorkBudgetUnit - totalOfDisplayedDataWorkBudgetUnit + smallestWorkBudgetUnit;

      updateChartData(labelOfOther, labelOfOther, otherWorkBudgetMins);
      updateWorkBudgetsWithUnit(otherWorkBudgetUnit);
    }

    const dataWithUnit = {
      labels: chartData.labels,
      values: workBudgetsWithUnit,
    };

    return { chartData, chartLegends, dataWithUnit };
  }, [
    dataLimit,
    dimensions,
    labelOfOther,
    totalOfRecords,
    workReportData,
    workReportTotal?.total_budget,
    worktimeUnitConv,
  ]);

  const getTotalWorkTimeOfDimensions = React.useCallback(
    (dimension1Names: string[]) =>
      dimension1Names.map((dimensionName) => {
        const workReportDataOfDimension = workReportData.filter(
          (data, i) => i + 1 < dataLimit && data.dimension_1.name === dimensionName
        );
        const totalWorkTimeOfDimension = workReportDataOfDimension.reduce(
          (prev, curr) => prev + curr.total[worktimeUnitConv],
          0
        );
        return totalWorkTimeOfDimension;
      }),
    [workReportData, worktimeUnitConv, dataLimit]
  );

  const { dates } = useDurationInterval({ since, until, durationInterval });

  const totalBarChartData = React.useMemo(() => {
    const chartData: ChartData = {
      labels: [],
      datasets: [{ data: [], backgroundColor: Colors.Priority.Primary }],
    };

    if (showDateAsXAxis) {
      dates.forEach((date, index) => {
        chartData.labels.push(format(date, 'yyyy-MM-dd'));
        chartData.datasets[0].data.push(0);
        const twt = workReportTotal?.worktime.find((wt) => isSameDay(parseISO(wt.date), date));
        if (twt) {
          chartData.datasets[0].data[index] += twt[worktimeUnitConv];
        }
      });
    } else {
      const uniqueDimensionNames = genUniqueLabelsGroupedByDimension1(workReportData, dataLimit);
      chartData.labels = uniqueDimensionNames;

      const totalWorkTimeByDimension = getTotalWorkTimeOfDimensions(uniqueDimensionNames);
      chartData.datasets[0].data = totalWorkTimeByDimension;

      const displayedWorktime = totalWorkTimeByDimension.reduce((prev, curr) => prev + curr, 0);

      const totalWorktime = workReportTotal?.total[worktimeUnitConv] || 0;
      const isExistingOtherData = totalOfRecords >= dataLimit;

      if (isExistingOtherData) {
        const otherWorktime = totalWorktime - displayedWorktime;
        chartData.labels.push(labelOfOther);
        chartData.datasets[0].data.push(otherWorktime);
      }
    }

    return chartData;
  }, [
    dates,
    worktimeUnitConv,
    workReportTotal,
    showDateAsXAxis,
    workReportData,
    labelOfOther,
    getTotalWorkTimeOfDimensions,
    totalOfRecords,
    dataLimit,
  ]);

  // Create combo chart percentage array
  const comboChartData = React.useMemo(() => {
    const calcCombo = comboWorkReport ? comboWorkReport[0]?.worktime : undefined;
    const dimension1st = {
      type: 'line' as const,
      data: [],
      backgroundColor: Colors.CLRed[600],
      borderColor: Colors.CLRed[600],
      yAxisID: 'y1',
    };
    const chartData: ChartData = {
      labels: [],
      datasets: [
        { data: [], backgroundColor: Colors.Priority.Primary, yAxisID: 'y' },
        dimension1st,
      ],
    };

    const showCombo =
      workReportData.length === 1 && dimensions.length === 2 && !isWorkBudgetDisplayed;
    if (!showCombo) {
      return chartData;
    }

    dates.forEach((date, index) => {
      chartData.labels.push(format(date, 'yyyy-MM-dd'));
      chartData.datasets[0].data.push(0);
      chartData.datasets[1].data.push(0);
      const twt = workReportTotal?.worktime.find((wt) => isSameDay(parseISO(wt.date), date));
      if (twt) {
        chartData.datasets[0].data[index] += twt[worktimeUnitConv];
        if (calcCombo) {
          const pwt = calcCombo.find((cwt) => isSameDay(parseISO(cwt.date), date));
          if (pwt) {
            const calcPercent =
              Math.floor((chartData.datasets[0].data[index] / pwt[worktimeUnitConv]) * 10000) / 100;
            chartData.datasets[1].data[index] += calcPercent;
          }
        }
      }
    });

    // Change the display priority of graphs
    const reverseDatasetsChartData = chartData;
    reverseDatasetsChartData.datasets = chartData.datasets.reverse();

    return reverseDatasetsChartData;
  }, [
    dates,
    worktimeUnitConv,
    workReportTotal,
    comboWorkReport,
    workReportData,
    dimensions,
    isWorkBudgetDisplayed,
  ]);

  const isComboChartAlert: boolean = React.useMemo(
    () =>
      chartType === 'combo_chart' &&
      comboChartData.datasets[0].data.length === 0 &&
      comboFetched &&
      (workReportData.length !== 1 || dimensions.length !== 2) &&
      workReportData.length !== 0,
    [chartType, comboChartData, workReportData, dimensions, comboFetched]
  );

  const totalBarChartDataWithBudget = React.useMemo(() => {
    const chartData: ChartData = {
      labels: [],
      datasets: [
        { label: labelOfBudget, data: [], backgroundColor: '#28A746' },
        { label: labelOfResult, data: [], backgroundColor: Colors.Priority.Primary },
      ],
    };

    if (showDateAsXAxis) {
      dates.forEach((date, index) => {
        chartData.labels.push(format(date, 'yyyy-MM-dd'));

        chartData.datasets[0].data.push(0);
        const twb = workReportTotal?.workbudget?.find((wb) => isSameDay(parseISO(wb.date), date));
        if (twb) {
          chartData.datasets[0].data[index] += twb[worktimeUnitConv];
        }

        chartData.datasets[chartData.datasets.length - 1].data.push(0);
        const twt = workReportTotal?.worktime.find((wt) => isSameDay(parseISO(wt.date), date));
        if (twt) {
          chartData.datasets[chartData.datasets.length - 1].data[index] += twt[worktimeUnitConv];
        }
      });
    } else {
      const uniqueDimensionNames = genUniqueLabelsGroupedByDimension1(workReportData, dataLimit);
      chartData.labels = uniqueDimensionNames;

      const totalWorkTimeByDimension = getTotalWorkTimeOfDimensions(uniqueDimensionNames);
      chartData.datasets[1].data = totalWorkTimeByDimension;

      const displayedWorktime = totalWorkTimeByDimension.reduce((prev, curr) => prev + curr, 0);

      const totalWorktime = workReportTotal?.total[worktimeUnitConv] || 0;
      const isExistingOtherData = totalOfRecords >= dataLimit;
      const isExistingOtherWorktime = isExistingOtherData && totalWorktime > displayedWorktime;

      const totalWorkBudgetByDimension = uniqueDimensionNames.map((dimensionName) => {
        const workReportDataOfDimension = workReportData.filter(
          (data) => data.dimension_1.name === dimensionName
        );
        const totalWorkBudgetOfDimension = workReportDataOfDimension.reduce(
          (prev, curr) => prev + (curr.total_budget?.[worktimeUnitConv] || 0),
          0
        );
        return totalWorkBudgetOfDimension;
      });

      chartData.datasets[0].data = totalWorkBudgetByDimension;

      const displayedWorkBudget = totalWorkBudgetByDimension.reduce((prev, curr) => prev + curr, 0);

      const totalWorkBudget = workReportTotal?.total_budget?.[worktimeUnitConv] || 0;
      const isExistingOtherBudget = isExistingOtherData && totalWorkBudget > displayedWorkBudget;

      if (isExistingOtherWorktime || isExistingOtherBudget) {
        chartData.labels.push(labelOfOther);
        chartData.datasets[0].data.push(0);
        chartData.datasets[1].data.push(0);
      }

      if (isExistingOtherBudget) {
        const otherWorkBudget = totalWorkBudget - displayedWorkBudget;
        chartData.datasets[0].data[chartData.datasets[0].data.length - 1] = otherWorkBudget;
      }

      if (isExistingOtherWorktime) {
        const otherWorktime = totalWorktime - displayedWorktime;
        chartData.datasets[1].data[chartData.datasets[1].data.length - 1] = otherWorktime;
      }
    }
    return chartData;
  }, [
    dates,
    labelOfBudget,
    labelOfResult,
    workReportTotal,
    worktimeUnitConv,
    showDateAsXAxis,
    labelOfOther,
    workReportData,
    getTotalWorkTimeOfDimensions,
    dataLimit,
    totalOfRecords,
  ]);

  const totalBarChartLegendsLabelsWithBudget = React.useMemo(
    () => [labelOfBudget, labelOfResult],
    [labelOfBudget, labelOfResult]
  );

  const { chartData: stackedBarChartData, chartLegends: stackedBarChartLegendsLabels } =
    React.useMemo(() => {
      const chartData: ChartData = {
        labels: [],
        datasets: [],
      };

      const updateChartData = (
        concatedLegends: string,
        concatedLabels: string,
        dataIndex: number
      ) => {
        if (chartData.datasets[dataIndex] === undefined) {
          chartData.datasets.push({
            label: concatedLabels,
            data: dates.map(() => 0),
            backgroundColor: getColor(chartData.datasets.length),
          });

          chartLegends.push(concatedLegends);
        }
      };

      const chartLegends: string[] = [];

      if (showDateAsXAxis) {
        dates.forEach((date, index) => {
          chartData.labels.push(format(date, 'yyyy-MM-dd'));

          workReportData.forEach((data, dataIndex) => {
            const { concatedLegends, concatedLabels } = genLabels(dimensions, data);
            updateChartData(concatedLegends, concatedLabels, dataIndex);
            const wt = data.worktime.find((w) => isSameDay(parseISO(w.date), date));
            if (wt) {
              chartData.datasets[dataIndex].data[index] = wt[worktimeUnitConv];
            }
          });

          const isExistingOtherData = totalOfRecords >= dataLimit;

          if (isExistingOtherData) {
            // delete the tail's data to display chart data for the number of dataLimit including Other
            chartData.datasets.pop();
            chartLegends.pop();

            const sumOfAllWorkTimesByDates = dates.map((d) => {
              let sumOfWorktime = 0;
              workReportTotal?.worktime.forEach((wt) => {
                if (isSameDay(d, parseISO(wt.date))) {
                  sumOfWorktime = wt[worktimeUnitConv];
                }
              });
              return sumOfWorktime;
            });

            const sumOfDisplayedWorkTimes = chartData.datasets.reduce(
              (prev, curr) => curr.data.map((d, i) => d + (prev[i] || 0)),
              [] as number[]
            );

            const otherData = sumOfDisplayedWorkTimes.map(
              (sumOfDisplayedWorkTime, i) => sumOfAllWorkTimesByDates[i] - sumOfDisplayedWorkTime
            );

            const indexOfOther = chartData.datasets.length;
            updateChartData(labelOfOther, labelOfOther, indexOfOther);

            chartData.datasets[indexOfOther].data = otherData;
          }
        });
      } else {
        const uniqueDimensionNames = genUniqueLabelsGroupedByDimension1(workReportData, dataLimit);
        chartData.labels = uniqueDimensionNames;

        workReportData.forEach((data, dataIndex) => {
          if (dataIndex + 1 >= dataLimit) {
            return;
          }

          if (dimensions.length === 1) {
            const { concatedLegends, concatedLabels } = genLabelsForXAxis(
              dimensions,
              data,
              chartType
            );
            const dataset = {
              label: concatedLabels,
              data: uniqueDimensionNames.map(() => 0),
              backgroundColor: getColor(dataIndex),
            };
            chartLegends.push(concatedLegends);
            const matchedIndex = uniqueDimensionNames.findIndex(
              (name) => name === data.dimension_1.name
            );
            dataset.data[matchedIndex] = data.total[worktimeUnitConv];
            chartData.datasets.push(dataset);
          } else {
            const dimension2Name = data.dimension_2?.name || undefined;
            const dimension3Name = data.dimension_3?.name || undefined;
            const dimension4Name = data.dimension_4?.name || undefined;

            const workReportDataHavingSameName = workReportData.filter(
              (d) =>
                d.dimension_2?.name === dimension2Name &&
                d.dimension_3?.name === dimension3Name &&
                d.dimension_4?.name === dimension4Name
            );
            if (
              workReportDataHavingSameName[0] &&
              workReportDataHavingSameName[0].dimension_1.name !== data.dimension_1.name
            ) {
              return;
            }

            const dataset = {
              label: '',
              data: uniqueDimensionNames.map(() => 0),
              backgroundColor: getColor(dataIndex),
            };
            const { concatedLegends, concatedLabels } = genLabelsForXAxis(
              dimensions,
              data,
              chartType
            );
            dataset.label = concatedLabels;
            const matchedIndex = uniqueDimensionNames.findIndex(
              (name) => name === data.dimension_1.name
            );
            dataset.data[matchedIndex] = data.total[worktimeUnitConv];
            chartLegends.push(concatedLegends);

            workReportDataHavingSameName.forEach((d) => {
              const matchedIndex2 = uniqueDimensionNames.findIndex(
                (name) => name === d.dimension_1.name
              );
              dataset.data[matchedIndex2] = d.total[worktimeUnitConv];
            });

            chartData.datasets.push(dataset);
          }
        });

        const sumOfDisplayedWorkTime = chartData.datasets
          .reduce((prev, curr) => curr.data.map((d, i) => d + (prev[i] || 0)), [] as number[])
          .reduce((prev, curr) => prev + curr, 0);

        const totalWorktime = workReportTotal?.total[worktimeUnitConv] || 0;
        const isExistingOtherData = totalOfRecords >= dataLimit;

        if (isExistingOtherData) {
          const otherData = totalWorktime - sumOfDisplayedWorkTime;
          chartData.labels.push(labelOfOther);
          const otherDataset = {
            label: labelOfOther,
            data: chartData.labels.map(() => 0),
            backgroundColor: getColor(chartData.datasets.length),
          };
          otherDataset.data[otherDataset.data.length - 1] = otherData;
          chartData.datasets.push(otherDataset);
          chartLegends.push(labelOfOther);
        }
      }

      return { chartData, chartLegends };
    }, [
      dates,
      workReportData,
      workReportTotal,
      totalOfRecords,
      dataLimit,
      dimensions,
      worktimeUnitConv,
      labelOfOther,
      showDateAsXAxis,
      chartType,
    ]);

  const {
    chartData: stackedBarChartDataWithBudget,
    chartLegends: stackedBarChartLegendsLabelsWithBudget,
  } = React.useMemo(() => {
    const chartDataResult: ChartData = {
      labels: [],
      datasets: [],
    };
    const chartDataBudget: ChartData = {
      labels: [],
      datasets: [],
    };

    const updateChartData = (
      concatedLegends: string,
      concatedLabels: string,
      dataIndex: number
    ) => {
      if (chartDataResult.datasets[dataIndex] === undefined) {
        chartDataResult.datasets.push({
          label: concatedLabels,
          data: dates.map(() => 0),
          backgroundColor: getColor(chartDataResult.datasets.length),
        });
        chartDataBudget.datasets.push({
          label: concatedLabels,
          data: dates.map(() => 0),
          backgroundColor: getColor(chartDataBudget.datasets.length),
        });

        chartLegends.push(concatedLegends);
      }
    };

    const chartLegends: string[] = [];
    dates.forEach((date, index) => {
      chartDataResult.labels.push(format(date, 'yyyy-MM-dd'));

      workReportData.forEach((data, dataIndex) => {
        const { concatedLegends, concatedLabels } = genLabels(dimensions, data);
        updateChartData(concatedLegends, concatedLabels, dataIndex);
        const wt = data.worktime.find((w) => isSameDay(parseISO(w.date), date));
        if (wt) {
          chartDataResult.datasets[dataIndex].data[index] = wt[worktimeUnitConv];
        }

        const wb = data.workbudget?.find((w) => isSameDay(parseISO(w.date), date));
        if (wb) {
          chartDataBudget.datasets[dataIndex].data[index] = wb[worktimeUnitConv];
        }
      });

      const isExistingOtherData = totalOfRecords >= dataLimit;

      if (isExistingOtherData) {
        // delete the tail's data to display chart data for the number of dataLimit including Other
        chartDataResult.datasets.pop();
        chartDataBudget.datasets.pop();
        chartLegends.pop();

        const sumOfAllWorkTimesByDates = dates.map((d) => {
          let sumOfWorktime = 0;
          workReportTotal?.worktime.forEach((wt) => {
            if (isSameDay(d, parseISO(wt.date))) {
              sumOfWorktime = wt[worktimeUnitConv];
            }
          });
          return sumOfWorktime;
        });

        const sumOfAllWorkBudgetsByDates = dates.map((d) => {
          let sumOfWorkbudget = 0;
          workReportTotal?.workbudget?.forEach((wb) => {
            if (isSameDay(d, parseISO(wb.date))) {
              sumOfWorkbudget = wb[worktimeUnitConv];
            }
          });
          return sumOfWorkbudget;
        });

        const sumOfDisplayedWorkTimes = chartDataResult.datasets.reduce(
          (prev, curr) => curr.data.map((d, i) => d + (prev[i] || 0)),
          [] as number[]
        );

        const sumOfDisplayedWorkBudgets = chartDataBudget.datasets.reduce(
          (prev, curr) => curr.data.map((d, i) => d + (prev[i] || 0)),
          [] as number[]
        );

        const otherDataResult = sumOfDisplayedWorkTimes.map(
          (sumOfDisplayedWorkTime, i) => sumOfAllWorkTimesByDates[i] - sumOfDisplayedWorkTime
        );

        const otherDataBudget = sumOfDisplayedWorkBudgets.map(
          (sumOfDisplayedWorkBudget, i) => sumOfAllWorkBudgetsByDates[i] - sumOfDisplayedWorkBudget
        );

        const indexOfOtherResult = chartDataResult.datasets.length;
        const indexOfOtherBudget = chartDataBudget.datasets.length;
        updateChartData(labelOfOther, labelOfOther, indexOfOtherResult);

        chartDataResult.datasets[indexOfOtherResult].data = otherDataResult;
        chartDataBudget.datasets[indexOfOtherBudget].data = otherDataBudget;
      }
    });

    const chartData: ChartData = {
      labels: chartDataResult.labels,
      datasets: [...chartDataBudget.datasets, ...chartDataResult.datasets],
    };

    return { chartData, chartLegends };
  }, [
    dates,
    workReportData,
    workReportTotal,
    totalOfRecords,
    dataLimit,
    dimensions,
    worktimeUnitConv,
    labelOfOther,
  ]);

  const barChartData = React.useMemo(() => stackedBarChartData, [stackedBarChartData]);
  const barChartLegendsLabels = React.useMemo(
    () => stackedBarChartLegendsLabels,
    [stackedBarChartLegendsLabels]
  );

  const { chartData: sumLineChartData, chartLegends: sumLineChartLegendsLabels } =
    React.useMemo(() => {
      const chartData: ChartData = {
        labels: [],
        datasets: [],
      };
      const chartLegends: string[] = [];

      chartData.labels = dates.map((date) => format(date, 'yyyy-MM-dd'));
      workReportData.forEach((data, dataIndex) => {
        const { concatedLabels, concatedLegends } = genLabels(dimensions, data);
        if (chartData.datasets[dataIndex] === undefined) {
          chartData.datasets.push({
            label: concatedLabels,
            data: dates.map(() => 0),
            backgroundColor: getColor(dataIndex),
            borderColor: getColor(dataIndex),
          });
          chartLegends.push(concatedLegends);
        }

        dates.forEach((date, index) => {
          const wt = data.worktime.find((w) => isSameDay(parseISO(w.date), date));
          if (wt) {
            chartData.datasets[dataIndex].data[index] = wt[worktimeUnitConv];
          }
        });
      });

      const isExistingOtherData = totalOfRecords >= dataLimit;

      if (isExistingOtherData) {
        // delete the tail's data to display chart data for the number of dataLimit including Other
        chartData.datasets.pop();
        chartLegends.pop();

        const sumOfAllWorkTimesByDates = dates.map((d) => {
          let sumOfWorktime = 0;
          workReportTotal?.worktime.forEach((wt) => {
            if (isSameDay(d, parseISO(wt.date))) {
              sumOfWorktime = wt[worktimeUnitConv];
            }
          });
          return sumOfWorktime;
        });

        const sumOfDisplayedWorkTimes = chartData.datasets.reduce(
          (prev, curr) => curr.data.map((d, i) => d + (prev[i] || 0)),
          [] as number[]
        );

        const otherData = sumOfDisplayedWorkTimes.map(
          (sumOfDisplayedWorkTime, i) => sumOfAllWorkTimesByDates[i] - sumOfDisplayedWorkTime
        );

        const otherDataset = {
          label: labelOfOther,
          data: otherData,
          backgroundColor: getColor(chartData.datasets.length),
          borderColor: getColor(chartData.datasets.length),
        };
        chartData.datasets.push(otherDataset);
        chartLegends.push(labelOfOther);
      }

      chartData.datasets.forEach((dataset, i) => {
        const { data } = dataset;
        const cumulativeData = data.reduce(
          (prev, curr, idx) => [...prev, curr + (prev[idx - 1] || 0)],
          [] as number[]
        );

        chartData.datasets[i] = { ...chartData.datasets[i], data: cumulativeData };
      });

      return { chartData, chartLegends };
    }, [
      dates,
      workReportData,
      dimensions,
      worktimeUnitConv,
      dataLimit,
      totalOfRecords,
      labelOfOther,
      workReportTotal,
    ]);

  const sumLineChartDataWithBudget = React.useMemo(() => {
    const chartData = {
      ...stackedBarChartDataWithBudget,
      datasets: [...stackedBarChartDataWithBudget.datasets],
    };

    const datasetLength = stackedBarChartDataWithBudget.datasets.length;

    stackedBarChartDataWithBudget.datasets.forEach((dataset, i) => {
      const { data } = dataset;
      const cumulativeData = data.reduce(
        (prev, curr, idx) => [...prev, curr + (prev[idx - 1] || 0)],
        [] as number[]
      );

      chartData.datasets[i] = { ...chartData.datasets[i], data: cumulativeData };
      const colorIdx = i < datasetLength / 2 ? i : i - datasetLength / 2;
      chartData.datasets[i].borderColor = getColor(colorIdx);
      if (i < datasetLength / 2) {
        chartData.datasets[i].borderDash = [6, 5];
      }
    });

    return chartData;
  }, [stackedBarChartDataWithBudget]);

  const sumLineChartLegendsLabelsWithBudget = React.useMemo(
    () => [labelOfBudget, labelOfResult, ...stackedBarChartLegendsLabelsWithBudget],
    [labelOfBudget, labelOfResult, stackedBarChartLegendsLabelsWithBudget]
  );

  const multiplePieChartData = React.useMemo(() => {
    const totalOfWorkReportTime = workReportTotal ? workReportTotal.total?.[worktimeUnitConv] : 0;
    let totalOfDisplayedWorkReportTime = 0;

    // group by dimension_1
    const groupedData: Record<string, WorkReportData[]> = {};
    workReportData.forEach((d, index) => {
      // for summarize as other
      if (index + 1 >= dataLimit) {
        return;
      }

      if (!groupedData[d.dimension_1.name]) {
        groupedData[d.dimension_1.name] = [];
      }

      groupedData[d.dimension_1.name].push(d);
      totalOfDisplayedWorkReportTime += d.total ? d.total?.[worktimeUnitConv] : 0;
    });

    // iterate grouped data
    const multiPieChartData: MultiplePieChartData[] = [];
    const keys = Object.keys(groupedData);
    for (let i = 0; i < keys.length; i += 1) {
      const chartTitle = keys[i];
      const workReport = groupedData[keys[i]];
      const totalRecords = workReport.length;

      const {
        chartData: pieChartDataResult,
        chartLegends: pieChartLegendsLabelsResult,
        dataWithUnit: pieDataWithUnitResult,
      } = generatePieChartData(workReport, totalRecords, true);

      multiPieChartData.push({
        chartTitle,
        pieChartDataResult,
        pieChartLegendsLabelsResult,
        pieDataWithUnitResult,
        isEmpty: false,
      });
    }

    const diffTime = totalOfWorkReportTime - totalOfDisplayedWorkReportTime;

    if (diffTime > 0) {
      multiPieChartData.push({
        chartTitle: labelOfOther,
        pieChartDataResult: {
          labels: [labelOfOther],
          datasets: [
            {
              data: [diffTime],
              backgroundColor: Colors.MultiColor21,
            },
          ],
        },
        pieChartLegendsLabelsResult: [labelOfOther],
        pieDataWithUnitResult: { labels: [labelOfOther], values: [diffTime] },
        isEmpty: false,
      });
    }

    return multiPieChartData;
  }, [
    dataLimit,
    labelOfOther,
    generatePieChartData,
    workReportData,
    workReportTotal,
    worktimeUnitConv,
  ]);

  return {
    totalBarChartData: isWorkBudgetDisplayed ? totalBarChartDataWithBudget : totalBarChartData,
    totalBarChartLegendsLabels: isWorkBudgetDisplayed ? totalBarChartLegendsLabelsWithBudget : [],
    stackedBarChartData,
    stackedBarChartLegendsLabels,
    barChartData,
    barChartLegendsLabels,
    sumLineChartData: isWorkBudgetDisplayed ? sumLineChartDataWithBudget : sumLineChartData,
    sumLineChartLegendsLabels: isWorkBudgetDisplayed
      ? sumLineChartLegendsLabelsWithBudget
      : sumLineChartLegendsLabels,
    pieChartData,
    pieChartLegendsLabels,
    pieDataWithUnit,
    pieChartDataBudget,
    pieChartLegendsLabelsBudget,
    pieDataWithUnitBudget,
    multiplePieChartData,
    comboChartData,
    isComboChartAlert,
  };
};

const getColor = (pos: number) => {
  const colorLength = Colors.MultiColor.length;
  return Colors.MultiColor[pos % colorLength];
};

const genLabels = (dimensions: WorkDimension[], data: WorkReportData) => {
  const labels = [];
  for (let i = 1; i <= dimensions.length; i += 1) {
    if (i === 1) {
      labels.push(data.dimension_1.name);
      if (data.dimension_1.key === '') {
        break;
      }
    }
    if (i === 2) {
      labels.push(data.dimension_2?.name || '');
    }
    if (i === 3) {
      labels.push(data.dimension_3?.name || '');
    }
    if (i === 4) {
      labels.push(data.dimension_4?.name || '');
    }
  }

  const concatLabels: string[] = [];
  const preDimensionCharLimit = 5;
  const lastDimensionCharLimit = 30;
  labels.forEach((label, index) => {
    if (
      index + 1 === dimensions.length ||
      labels.length === 1 ||
      label.length <= preDimensionCharLimit
    ) {
      if (label.length > lastDimensionCharLimit) {
        concatLabels.push(`${label.substring(0, lastDimensionCharLimit)}...`);
      } else {
        concatLabels.push(label);
      }
    } else {
      concatLabels.push(`${label.substring(0, preDimensionCharLimit)}...`);
    }
  });

  const delimiter = ' > ';
  const concatedLegends = concatLabels.join(delimiter);
  const concatedLabels = labels.join(delimiter);

  return { concatedLegends, concatedLabels };
};

const genLabelsForXAxis = (
  dimensions: WorkDimension[],
  data: WorkReportData,
  chartType: ChartTypeKey
) => {
  const { concatedLegends, concatedLabels } = genLabels(dimensions, data);

  if (chartType === 'sum_line_chart' || dimensions.length === 1) {
    return { concatedLegends, concatedLabels };
  }

  const delimiter = ' > ';
  const splitLabels = concatedLabels.split(delimiter);
  const splitLegends = concatedLegends.split(delimiter);

  const slicedLabels = splitLabels.slice(1);
  const slicedLegends = splitLegends.slice(1);

  const reConcatedLabels = slicedLabels.join(delimiter);
  const reConcatedLegends = slicedLegends.join(delimiter);

  return { concatedLabels: reConcatedLabels, concatedLegends: reConcatedLegends };
};

const genUniqueLabelsGroupedByDimension1 = (data: WorkReportData[], dataLimit: DataLimitKey) => {
  const dimension1Names = data
    .filter((_, index) => index + 1 < dataLimit)
    .map((d) => d.dimension_1.name);
  const uniqueDimensionNames = dimension1Names.filter(
    (dimensionName, index) => dimension1Names.indexOf(dimensionName) === index
  );
  return uniqueDimensionNames;
};

const genUniqueLabels = (
  dimensions: WorkDimension[],
  data: WorkReportData[],
  chartType: ChartTypeKey
) => {
  const labels = data.map((d) => genLabelsForXAxis(dimensions, d, chartType).concatedLabels);
  return labels.filter((label, index) => labels.indexOf(label) === index);
};
