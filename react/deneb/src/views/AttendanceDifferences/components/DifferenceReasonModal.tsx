import * as React from 'react';
import { css } from '@emotion/css';
import { useTranslation } from 'react-i18next';

import { Option } from '@/interfaces/Option';
import { Input, Select, Modal } from '@/components/ui-inventory';
import { Button, Form } from '@/components/shared';
import { ErrorMessage } from '@/components/shared/RHF/ErrorMessage';
import { ReturnValues } from '@/views/AttendanceDifferences/hooks/useDifferenceReasonForm';

type Props = {
  memberName: string;
  date: string;
  isEdit: boolean;
  show: boolean;
  onHide: () => void;
  onSubmit: () => void;
  onDelete: () => void;
  register: ReturnValues['register'];
  handleClassChange: (opt: { value: number; label: string }) => void;
  errors: ReturnValues['errors'];
  isDisabled: boolean;
  isShownClassError: boolean;
  isShownReasonError: boolean;
  status?: 'error' | undefined;
  reasonClass?: number;
};

export const DifferenceReasonModal = ({
  memberName,
  date,
  isEdit,
  show,
  onHide,
  onSubmit,
  onDelete,
  register,
  handleClassChange,
  errors,
  isDisabled,
  isShownClassError,
  isShownReasonError,
  status,
  reasonClass,
}: Props) => {
  const { t } = useTranslation();
  const reasonClassOptions: Option[] = [
    {
      value: 1,
      label: t('attendance_difference:reason_class:pc_used_not_for_work'),
    },
    {
      value: 2,
      label: t('attendance_difference:reason_class:pc_unnecessarily'),
    },
    {
      value: 3,
      label: t('attendance_difference:reason_class:pc_log_unreadable'),
    },
    {
      value: 4,
      label: t('attendance_difference:reason_class:pc_off_omission'),
    },
    {
      value: 0,
      label: t('attendance_difference:reason_class:others'),
    },
  ];

  return (
    <Modal centered show={show} onHide={onHide}>
      <Modal.Header closeButton>{t('difference_reason')}</Modal.Header>
      <Form onSubmit={onSubmit}>
        <Modal.Body>
          <div className={Styles.memberName}>{`${date} ${memberName}`}</div>
          <div className={Styles.reasonClass}>
            <Select
              placeholder={t('difference_reason_class')}
              options={reasonClassOptions}
              defaultValue={reasonClassOptions.find((option) => option.value === reasonClass)}
              onChange={handleClassChange}
            />
            {isShownClassError && (
              <ErrorMessage name="reasonClass" errors={errors} className={Styles.errorMessage} />
            )}
          </div>
          <Input
            variant="multiple"
            rows={5}
            status={status}
            placeholder={t('placeholder:input_data', { data: t('difference_reason_memo') })}
            {...register('reason')}
          />
          {isShownReasonError && (
            <ErrorMessage name="reason" errors={errors} className={Styles.errorMessage} />
          )}
        </Modal.Body>
        <Modal.Footer className={Styles.footer}>
          <div>
            {isEdit && (
              <Button variant="danger" onClick={onDelete}>
                {t('delete')}
              </Button>
            )}
          </div>
          <div className={Styles.rightButtons}>
            <Button variant="secondary" className={Styles.cancel} onClick={onHide}>
              {t('cancel')}
            </Button>
            <Button variant="primary" type="submit" disabled={isDisabled}>
              {t('save')}
            </Button>
          </div>
        </Modal.Footer>
      </Form>
    </Modal>
  );
};

const Styles = {
  memberName: css`
    margin-bottom: 0.75rem;
  `,
  reasonClass: css`
    margin-bottom: 0.875rem;
  `,
  footer: css`
    justify-content: space-between;
  `,
  rightButtons: css`
    margin-left: auto;
  `,
  cancel: css`
    margin-right: 0.75rem;
  `,
  errorMessage: css`
    margin: 0.5rem 0 0 0;
  `,
};
