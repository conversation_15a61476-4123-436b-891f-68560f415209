import * as React from 'react';
import { useTranslation } from 'react-i18next';
import { css } from '@emotion/css';

import * as Colors from 'styles/colors';
import { Button, Card, Col, Container, Form, Input, Label, Row } from '@/components/shared';
import { CheckBox } from '@/components/ui-inventory';
import { ControlledAsyncSelect } from '@/components/shared/RHF/ControlledAsyncSelect';
import { UseProcessListSearchFormReturns } from '@/views/ProcessMaster/hooks/useProcessListSearchForm';
import * as testIds from '@/test-utils/test-ids';

type ProcessListSearchFormProps = {
  control: UseProcessListSearchFormReturns['control'];
  register: UseProcessListSearchFormReturns['register'];
  parentSelectProps: UseProcessListSearchFormReturns['parentSelectProps'];
  isActiveChecked: boolean | undefined;
  setIsActiveChecked: React.Dispatch<React.SetStateAction<boolean | undefined>>;
  isUseCountChecked: boolean | undefined;
  setIsUseCountChecked: React.Dispatch<React.SetStateAction<boolean | undefined>>;
  handleFormSubmit: (e: React.FormEvent) => void;
  handleFormReset: () => void;
};

export const ProcessListSearchForm = ({
  control,
  register,
  parentSelectProps,
  isActiveChecked,
  setIsActiveChecked,
  isUseCountChecked,
  setIsUseCountChecked,
  handleFormSubmit,
  handleFormReset,
}: ProcessListSearchFormProps) => {
  const { t } = useTranslation();

  return (
    <Card className={Styles.card}>
      <Form onSubmit={handleFormSubmit}>
        <Container fluid>
          <Row className={Styles.row}>
            <Col>
              <Row>
                <Label column md={3} className={Styles.spacing}>
                  {t('keyword')}
                </Label>
                <Col md={8}>
                  <Input placeholder={t('placeholder:process_filter')} {...register('search')} />
                </Col>
                <Col md={1}>{/* For reserving space */}</Col>
              </Row>
            </Col>
            <Col>
              <Row>
                <Label column md={3} className={Styles.spacing}>
                  {t('active')}
                </Label>
                <Col md={8} className={Styles.multipleChoices}>
                  <CheckBox
                    checked={isActiveChecked !== undefined && isActiveChecked}
                    inline
                    label={t('active')}
                    onChange={(e: React.ChangeEvent<HTMLInputElement>) => {
                      setIsActiveChecked(e.target.checked ? true : undefined);
                    }}
                  />
                  <CheckBox
                    name="inactive"
                    checked={isActiveChecked !== undefined && !isActiveChecked}
                    inline
                    label={t('not_active')}
                    onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
                      setIsActiveChecked(e.target.checked ? false : undefined)
                    }
                  />
                </Col>
                <Col md={1}>{/* For reserving space */}</Col>
              </Row>
            </Col>
          </Row>
          <Row className={Styles.row}>
            <Col>
              <Row>
                <Label column md={3} className={Styles.spacing}>
                  {t('process_category')}
                </Label>
                <Col md={8}>
                  <ControlledAsyncSelect
                    control={control}
                    name="parent_ids"
                    data-testid={testIds.ProcessCategorySelectBox.container}
                    isMulti
                    noOptionsMessage={t('msg:no_options')}
                    placeholder={t('placeholder:all_process_categories')}
                    closeMenuOnSelect={false}
                    loadOptions={parentSelectProps.loadOptions}
                    defaultOptions={parentSelectProps.defaultOptions}
                    defaultValue={parentSelectProps.defaultValue}
                  />
                </Col>
              </Row>
            </Col>
            <Col>
              <Row>
                <Label column md={3} className={Styles.spacing}>
                  {t('use_count')}
                </Label>
                <Col md={8} className={Styles.multipleChoices}>
                  <CheckBox
                    checked={isUseCountChecked !== undefined && isUseCountChecked}
                    inline
                    label={t('use')}
                    onChange={(e: React.ChangeEvent<HTMLInputElement>) => {
                      setIsUseCountChecked(e.target.checked ? true : undefined);
                    }}
                  />
                  <CheckBox
                    name="not_use"
                    checked={isUseCountChecked !== undefined && !isUseCountChecked}
                    inline
                    label={t('not_use')}
                    onChange={(e: React.ChangeEvent<HTMLInputElement>) => {
                      setIsUseCountChecked(e.target.checked ? false : undefined);
                    }}
                  />
                </Col>
                <Col md={1}>{/* For reserving space */}</Col>
              </Row>
            </Col>
          </Row>
        </Container>
        <hr className={Styles.hr} />
        <Container fluid>
          <Row>
            <Col className={Styles.buttonGroup}>
              <Button variant="textlink" onClick={handleFormReset}>
                {t('reset')}
              </Button>
              <Button type="submit" variant="primary">
                {t('search')}
              </Button>
            </Col>
          </Row>
        </Container>
      </Form>
    </Card>
  );
};

const Styles = {
  spacing: css`
    padding-left: 0.5rem;
  `,
  card: css`
    padding: 1rem;
    margin: 1rem 0rem;
  `,
  row: css`
    margin-bottom: 1rem;
  `,
  multipleChoices: css`
    display: flex;
    flex-wrap: wrap;
    padding-top: 0.5rem;
    padding-bottom: 0.5rem;
    justify-content: flex-start;
    gap: 1rem;
  `,
  buttonGroup: css`
    text-align: right;
    padding-right: 0;
  `,
  hr: css`
    border-color: ${Colors.Border.Default};
  `,
};
