// -------------------- Components from shared, ui-inventory --------------------
export const Alert = {
  message: 'Alert__message',
};

export const Checkbox = {
  label: 'Checkbox__label',
};

export const Select = {
  dropdownIndicator: 'Select__dropdownIndicator',
  menu: 'Select__menu',
}

export const Modal = {
  header: 'Modal__header',
};

// -------------------- Components from domain --------------------
export const WorkSelectBox = {
  container: 'WorkSelectBox',
  dropdownWrapper: 'WorkSelectBox__dropdownWrapper',
  inputWrapper: 'WorkSelectBox__inputWrapper',
};

// -------------------- Components from views --------------------
export const StopWatchCard = {
  container: 'StopWatchCard',
  trash: 'StopWatchCard__trash',
  bar: 'StopWatchCard__bar',
}

export const StopWatchCardsWrapper = {
  wrapper: 'StopWatchCardsWrapper',
}

export const ProcessCategorySelectBox = {
  container: 'ProcessCategorySelectBox',
};
