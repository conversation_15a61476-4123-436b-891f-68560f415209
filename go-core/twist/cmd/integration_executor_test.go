package cmd

import (
	"encoding/base64"
	"encoding/json"
	"errors"
	"fmt"
	"net/http"
	"strconv"
	"testing"
	"time"

	"github.com/golang/mock/gomock"
	"github.com/jarcoal/httpmock"
	"gitlab.com/innopm/deneb/common/idb"
	"gitlab.com/innopm/deneb/common/ierror"
	"gitlab.com/innopm/deneb/common/ilog"
	"gitlab.com/innopm/deneb/config"
	"gitlab.com/innopm/deneb/domain/model"
	"gitlab.com/innopm/deneb/domain/model/tbl"
	infra "gitlab.com/innopm/deneb/infra/integration"
	"gitlab.com/innopm/deneb/test"
	"gitlab.com/innopm/deneb/test/assertmodel"
	itest "gitlab.com/innopm/deneb/test/integration"
	"gitlab.com/innopm/deneb/test/mockrepo"
	"gitlab.com/innopm/deneb/util"
	"gitlab.com/innopm/deneb/util/dateutil"
	"gitlab.com/innopm/deneb/util/encutil"
	"gitlab.com/innopm/deneb/util/intutil"
	"gitlab.com/innopm/twist/twisttest"
	"gorm.io/gorm"
)

type integExecTcase struct {
	desc                string
	preComp             tbl.Company
	preEmps             []tbl.Employee
	preIntegJob         tbl.IntegrationJobcan
	preIntegKOT         tbl.IntegrationKOT
	preIntegKinkakuji   tbl.IntegrationKinkakuji
	preIntegHrmos       tbl.IntegrationHrmos
	preCmpClsng         []tbl.CompanyClosing
	preIntegAtt         []tbl.IntegrationAttendance
	expIntegAtt         []tbl.IntegrationAttendance
	preTsheet           []tbl.Timesheet
	expTsheet           []tbl.Timesheet
	preTsRest           []tbl.TimesheetRest
	expTsRest           []tbl.TimesheetRest
	preIntegdAtt        []tbl.IntegratedAttendance
	expIntegdAtt        []tbl.IntegratedAttendance
	input               integExecArgs
	expErr              twisttest.ExpReturns
	mrr                 *test.MockRepoRegistry
	mockhttpJTkn        *mockHttpRes
	mockhttpJEmp        *mockHttpRes
	mockhttpJSum        *mockHttpRes
	setupKOTHTTPMocks   func() func()
	setupHrmosHTTPMocks func() func()
	cleanDB             bool
	flexNow             *time.Time
}

type integExecTester struct{}

type mockHttpRes struct {
	status       int
	body         any
	multiRes     bool
	multiResBody []any
}

func (i *integExecTester) dbAssert(t *testing.T, db *gorm.DB,
	expIntegAtt []tbl.IntegrationAttendance, expTsheet []tbl.Timesheet,
	expTsRest []tbl.TimesheetRest, expIntegdAtt []tbl.IntegratedAttendance) {

	actIntegAtt := []tbl.IntegrationAttendance{}
	db.Find(&actIntegAtt)
	assertmodel.IntegrationAttendanceSlice(t, expIntegAtt, actIntegAtt)

	actTsheet := []tbl.Timesheet{}
	db.Find(&actTsheet)
	assertmodel.TimesheetSlice(t, expTsheet, actTsheet)

	actTsRest := []tbl.TimesheetRest{}
	db.Order("timesheet_no, start_date, start_time").Find(&actTsRest)
	assertmodel.TimesheetRestSlice(t, expTsRest, actTsRest)

	actIntegdAtt := []tbl.IntegratedAttendance{}
	db.Find(&actIntegdAtt)
	assertmodel.IntegratedAttendanceSlice(t, expIntegdAtt, actIntegdAtt)
}

func (i *integExecTester) execTest(t *testing.T, cases []integExecTcase) {
	var ctxDB idb.DB
	ctxDB.Logger = ilog.NewDBlogger(&ilog.InpmLogger{})
	cdb, err := ctxDB.ConnectDb("common_db")
	if err != nil {
		t.Fatalf("failed to connect db: %v", err)
	}
	mdb, err := ctxDB.ConnectDb("db01")
	if err != nil {
		t.Fatalf("failed to connect db: %v", err)
	}
	t.Cleanup(func() {
		if dbConn, err := mdb.DB(); err == nil {
			dbConn.Close()
		}
		if dbConn, err := cdb.DB(); err == nil {
			dbConn.Close()
		}
	})

	for _, cs := range cases {
		t.Run(cs.desc, func(t *testing.T) {
			if cs.flexNow != nil {
				dateutil.SetFlexNow(*cs.flexNow)
				defer dateutil.ResetFlexNow()
			}

			// HTTP Mock
			// jobcan token
			if cs.mockhttpJTkn != nil {
				tUrl := "https://crowdworks.co.jp/token"
				itest.SetIntegConfigHCTimeout(60)
				itest.SetIntegConfigTAurl(tUrl)
				client := infra.GetRestyClient()
				httpmock.ActivateNonDefault(client.GetClient())
				defer httpmock.DeactivateAndReset()
				httpmock.RegisterResponder("POST", tUrl,
					func(req *http.Request) (*http.Response, error) {
						return httpmock.NewJsonResponse(
							cs.mockhttpJTkn.status,
							cs.mockhttpJTkn.body,
						)
					},
				)
			}
			// jobcan employee
			if cs.mockhttpJEmp != nil {
				empUrl := "https://crowdworks.co.jp/emp"
				itest.SetIntegConfigHCTimeout(60)
				itest.SetIntegConfigEAUrl(empUrl)
				client := infra.GetRestyClient()
				httpmock.ActivateNonDefault(client.GetClient())
				defer httpmock.DeactivateAndReset()

				res, _ := httpmock.NewJsonResponse(cs.mockhttpJEmp.status, cs.mockhttpJEmp.body)
				httpResps := []*http.Response{res}

				if cs.mockhttpJEmp.multiRes {
					if cs.mockhttpJEmp.multiResBody == nil {
						res, _ = httpmock.NewJsonResponse(http.StatusOK, infra.Employees{})
						httpResps = append(httpResps, res)
					} else {
						for _, body := range cs.mockhttpJEmp.multiResBody {
							res, _ = httpmock.NewJsonResponse(http.StatusOK, body)
							httpResps = append(httpResps, res)
						}
					}
				}

				httpmock.RegisterResponder("GET", empUrl,
					httpmock.ResponderFromMultipleResponses(httpResps, t.Log),
				)
			}
			// jobcan summary
			if cs.mockhttpJSum != nil {
				sumUrl := "https://crowdworks.co.jp/sum"
				itest.SetIntegConfigHCTimeout(60)
				itest.SetIntegConfigSAurl(sumUrl)
				client := infra.GetRestyClient()
				httpmock.ActivateNonDefault(client.GetClient())
				defer httpmock.DeactivateAndReset()

				res, _ := httpmock.NewJsonResponse(cs.mockhttpJSum.status, cs.mockhttpJSum.body)
				httpResps := []*http.Response{res}

				if cs.mockhttpJSum.multiRes {
					if cs.mockhttpJSum.multiResBody == nil {
						res, _ = httpmock.NewJsonResponse(http.StatusOK, infra.Summaries{})
						httpResps = append(httpResps, res)
					} else {
						for _, body := range cs.mockhttpJSum.multiResBody {
							res, _ = httpmock.NewJsonResponse(http.StatusOK, body)
							httpResps = append(httpResps, res)
						}
					}
				}

				httpmock.RegisterResponder("GET", sumUrl,
					httpmock.ResponderFromMultipleResponses(httpResps, t.Log),
				)
			}
			// HTTP Mock
			if cs.setupKOTHTTPMocks != nil {
				t.Cleanup(cs.setupKOTHTTPMocks())
			}
			if cs.setupHrmosHTTPMocks != nil {
				t.Cleanup(cs.setupHrmosHTTPMocks())
			}

			tester := twisttest.TwistTester[integExecArgs]{
				MRR:     cs.mrr,
				CleanDB: cs.cleanDB,
			}

			dbData := []interface{}{
				cs.preComp, cs.preEmps, cs.preIntegJob,
				cs.preCmpClsng, cs.preIntegAtt,
				cs.preTsheet, cs.preTsRest, cs.preIntegdAtt,
				cs.preIntegKOT, cs.preIntegKinkakuji, cs.preIntegHrmos,
			}

			tester.ExecTwistTest(t, cdb, mdb,
				cs.input, &integExec{}, cs.expErr, dbData)

			// Assert DB
			i.dbAssert(t, mdb, cs.expIntegAtt, cs.expTsheet, cs.expTsRest, cs.expIntegdAtt)
		})
	}
}

func (i *integExecTester) genTestCompany(compID int) (company tbl.Company) {
	company = tbl.Company{
		CompanyNo:               compID,
		CompanyName:             "かいしゃ1",
		AppliedUser:             0,
		LanguageCode:            "en",
		CurrencyCode:            "JPY",
		Timezone:                "Asia/Tokyo",
		CountryCode:             "JP",
		LogoFile:                "f658a908f210f51e9a3dad20e10682a1.png",
		StartMonth:              4,
		CostMax:                 1.00,
		ReportTax:               1,
		ExpenseGroup:            1,
		JobtimeType:             1,
		JobtimeProcessCount:     1,
		UseJobtimeFlag:          true,
		SalesStatus:             false,
		WorktimeStep:            15,
		WorktimeUnit:            0,
		WorktimeRangeMin:        15,
		HoursInDay:              8.00,
		HoursInMonth:            160.00,
		TimesheetUseType:        true,
		TimesheetRestType:       true,
		TimesheetProjectOrder:   0,
		TimesheetFutureFlag:     true,
		TimesheetEqualityFlag:   true,
		EndTimeCalcFlag:         0,
		ProjectStr:              "0",
		StartTime:               "09:00:00",
		EndTime:                 "18:00:00",
		Rest1StartTime:          "12:00:00",
		Rest1EndTime:            "13:00:00",
		Rest2StartTime:          "",
		Rest2EndTime:            "",
		HalfRest1StartTime:      "09:00:00",
		HalfRest1EndTime:        "13:00:00",
		HalfRest2StartTime:      "14:00:00",
		HalfRest2EndTime:        "18:00:00",
		SyncCalendarService:     0,
		CertifyService:          0,
		PasswdExpire:            0,
		PasswdAlert:             0,
		ExpenseNo:               0,
		GanttFlag:               true,
		ProjectCdFlag:           true,
		ProjectCdStr:            "",
		CompanyProjectNo:        0,
		CompanyStatus:           0,
		AttendanceManageUseType: false,
		UseHolidayCountryCode:   "",
		OrignalHolidayUseFlag:   false,
		CreateTime:              util.DateToTime("2019-11-01 02:36:30"),
		UpdateTime:              util.DateToTime("2019-11-30 17:36:30"),
		AuthCode:                "0",
		SamlFlag:                true,
		APIFlag:                 true,
		UseTimesheetApproval:    true,
	}

	return
}

func (i *integExecTester) genTestEmployee(compID, cnt int) (emps []tbl.Employee) {
	for i := 1; i <= cnt; i++ {
		emp := tbl.Employee{
			EmployeeNo:              i,
			CompanyNo:               compID,
			DepartmentNo:            test.NewPtr(i).(*int),
			ContractNo:              test.NewPtr(1).(*int),
			TitleNo:                 test.NewPtr(1).(*int),
			CustomerNo:              test.NewPtr(1).(*int),
			Salary:                  test.NewPtr(6250.0).(*float64),
			FirstName:               test.NewPtr("Employee" + strconv.Itoa(i)).(*string),
			FamilyName:              test.NewPtr(strconv.Itoa(i)).(*string),
			Email:                   test.NewPtr("email " + strconv.Itoa(i) + "@example.com").(*string),
			Password:                nil,
			MemberCode:              test.NewPtr("EmployeeCd" + strconv.Itoa(i)).(*string),
			StartDate:               test.NewPtr(util.DateToTime("2020-01-01")).(*time.Time),
			EndDate:                 test.NewPtr(util.DateToTime("9999-12-31")).(*time.Time),
			OutputCsv:               test.NewPtr(true).(*bool),
			UseCalendarFlag:         test.NewPtr(true).(*bool),
			UseSyncCalendarType:     test.NewPtr(1).(*int),
			TimesheetProjectOrder:   test.NewPtr(1).(*int),
			EndTimeCalcFlag:         test.NewPtr(0).(*int),
			EmployeeCostGroup:       test.NewPtr(1).(*int),
			DayEndTime:              test.NewPtr("05:00:00").(*string),
			UseCertifyType:          test.NewPtr(1).(*int),
			PasswdTime:              test.NewPtr(util.DateToTime("2020-01-01")).(*time.Time),
			LanguageCode:            test.NewPtr("jp").(*string),
			Timezone:                test.NewPtr("UTC").(*string),
			UseOvertimeRequestType:  test.NewPtr(1).(*int),
			VacationMinusRejectFlag: test.NewPtr(true).(*bool),
			CreateTime:              util.DateToTime("2020-01-01"),
			UpdateTime:              util.DateToTime("2020-01-31"),
			AuthAPIUse:              test.NewPtr(true).(*bool),
		}
		emps = append(emps, emp)
	}
	return
}

func (i *integExecTester) genTestIntegAttendance(id, compID,
	userID, integSettingID, days int, status string, sys string) (integAtt tbl.IntegrationAttendance) {
	startTime := util.DateToTime("2023-01-01 09:00:00")
	targetStartDate := util.DateToTime("2022-12-01")
	targetEndDate := targetStartDate.AddDate(0, 0, days)
	return tbl.IntegrationAttendance{
		ID:                   id,
		CompanyNo:            compID,
		Name:                 sys + "Account",
		ExecutorFamilyName:   "System",
		ExecutorFirstName:    "",
		ExecutorID:           userID,
		IntegrationSettingID: integSettingID,
		TargetSystem:         sys,
		TargetStartDate:      targetStartDate,
		TargetEndDate:        targetEndDate,
		StartTime:            startTime,
		EndTime:              nil,
		Status:               status,
		ErrSummaries:         "{}",
		CreatedAt:            startTime,
		CreateUser:           userID,
		UpdatedAt:            startTime,
		UpdateUser:           userID,
	}
}

func (i *integExecTester) genTestCompanyClosing(compID, userID int,
	clsDate time.Time) (data tbl.CompanyClosing) {
	data = tbl.CompanyClosing{
		CompanyClosingNo: 1,
		CompanyNo:        compID,
		ClosingType:      test.NewPtr(0).(*int),
		ClosingDate:      util.DateToTime(clsDate.Format("2006-01-02")),
		CreateTime:       util.DateToTime("2021-01-01"),
		CreateUser:       userID,
		UpdateTime:       util.DateToTime("2021-01-01"),
		UpdateUser:       userID,
	}
	return
}
func (i *integExecTester) genTestIntegrationJobcan(id, compID, userID int) tbl.IntegrationJobcan {
	return tbl.IntegrationJobcan{
		ID:            id,
		Name:          "jobcanAccount",
		CompanyNo:     compID,
		ClientID:      "id01",
		Secret:        "secret01",
		AutoIntegFlag: true,
		CreatedAt:     util.DateToTime("2019-01-01"),
		CreateUser:    userID,
		UpdatedAt:     util.DateToTime("2019-01-31"),
		UpdateUser:    userID,
	}
}
func (i *integExecTester) genTestIntegrationKOT(id, compID, userID int) tbl.IntegrationKOT {
	encrypted, err := encutil.EncryptAES([]byte(config.Integ.EncryptionKey), []byte("ACCESS_TOKEN"))
	if err != nil {
		panic(err)
	}
	base64Encoded := base64.StdEncoding.EncodeToString(encrypted)
	return tbl.IntegrationKOT{
		ID:            id,
		Name:          "kotAccount",
		CompanyNo:     compID,
		AccessToken:   base64Encoded,
		AutoIntegFlag: true,
		CreatedAt:     util.DateToTime("2019-01-01"),
		CreateUser:    userID,
		UpdatedAt:     util.DateToTime("2019-01-31"),
		UpdateUser:    userID,
	}
}

func (i *integExecTester) genTestIntegrationKinkakuji(id, compID, userID int) tbl.IntegrationKinkakuji {
	encrypted, err := encutil.EncryptAES([]byte(config.Integ.EncryptionKey), []byte("ACCESS_TOKEN"))
	if err != nil {
		panic(err)
	}
	base64Encoded := base64.StdEncoding.EncodeToString(encrypted)
	return tbl.IntegrationKinkakuji{
		ID:            id,
		Name:          "kinkakujiAccount",
		CompanyNo:     compID,
		AccessToken:   base64Encoded,
		AutoIntegFlag: true,
		CreatedAt:     util.DateToTime("2019-01-01"),
		CreateUser:    userID,
		UpdatedAt:     util.DateToTime("2019-01-31"),
		UpdateUser:    userID,
	}
}

func (i *integExecTester) genTestIntegrationHrmos(id, compID, userID int) tbl.IntegrationHrmos {
	encrypted, err := encutil.EncryptAES([]byte(config.Integ.EncryptionKey), []byte("crowdworks"))
	if err != nil {
		panic(err)
	}
	compKey := base64.StdEncoding.EncodeToString(encrypted)
	encrypted, err = encutil.EncryptAES([]byte(config.Integ.EncryptionKey), []byte("secret"))
	if err != nil {
		panic(err)
	}
	secret := base64.StdEncoding.EncodeToString(encrypted)
	return tbl.IntegrationHrmos{
		ID:            id,
		CompanyNo:     compID,
		Name:          "hrmosAccount",
		CompanyKey:    compKey,
		Secret:        secret,
		AutoIntegFlag: true,
		CreatedAt:     util.DateToTime("2019-01-01"),
		CreateUser:    userID,
		UpdatedAt:     util.DateToTime("2019-01-31"),
		UpdateUser:    userID,
	}
}

func (i *integExecTester) genTestTimesheet(compID, cnt int, isMultiDays bool) (tsheets []tbl.Timesheet) {
	strTsDate := util.DateToTime("2022-12-01")
	for i := 1; i <= cnt; i++ {
		tsheet := tbl.Timesheet{
			TimesheetNo:               i,
			EmployeeNo:                i,
			TimesheetDate:             strTsDate,
			StartTime:                 test.NewPtr("09:00:00").(*string),
			StartStampTime:            nil,
			EndTime:                   test.NewPtr("18:00:00").(*string),
			EndStampTime:              nil,
			HasAttendance:             test.NewPtr(true).(*bool),
			Comments:                  nil,
			Status:                    0,
			HolidayType:               test.NewPtr(0).(*int),
			TmpDeleteFlag:             false,
			Salary:                    0,
			CreateTime:                strTsDate,
			UpdateTime:                strTsDate,
			ApprovalStatus:            test.NewPtr(0).(*int),
			AutoCalculationAttendance: test.NewPtr(false).(*bool),
		}
		tsheets = append(tsheets, tsheet)

		if isMultiDays {
			strTsDate = strTsDate.AddDate(0, 0, 1)
		}
	}
	return
}

func (i *integExecTester) genTestTimesheetRest(compID, cnt int, isMultiDays bool) (tsRests []tbl.TimesheetRest) {
	strTsDate := util.DateToTime("2022-12-01")
	for i := 1; i <= cnt; i++ {
		tsRest := tbl.TimesheetRest{
			RestNo:      i,
			TimesheetNo: i,
			StartDate:   test.NewPtr(strTsDate).(*time.Time),
			StartTime:   test.NewPtr("12:00:00").(*string),
			EndDate:     test.NewPtr(strTsDate).(*time.Time),
			EndTime:     test.NewPtr("13:00:00").(*string),
			CreateTime:  strTsDate,
			UpdateTime:  strTsDate,
		}
		tsRests = append(tsRests, tsRest)

		if isMultiDays {
			strTsDate = strTsDate.AddDate(0, 0, 1)
		}
	}
	return
}

func (i *integExecTester) genIntegratedAttendance(compID, userID, integAttID int, jSums []infra.Summary, sys string) (
	integdAtts []tbl.IntegratedAttendance) {
	for _, jSum := range jSums {
		holidayUse := false
		if jSum.VacationType != "none" {
			holidayUse = true
		}

		integdAtts = append(integdAtts,
			tbl.IntegratedAttendance{
				CompanyNo:               compID,
				EmployeeNo:              int(jSum.EmployeeId),
				TimesheetDate:           util.DateToTime(jSum.Date),
				TargetSystem:            sys,
				IntegrationAttendanceId: integAttID,
				StartTime:               jSum.WorkStart + ":00",
				EndTime:                 jSum.WorkEnd + ":00",
				HolidayUse:              holidayUse,
				RestTime:                int(jSum.Rest),
				CreateUser:              userID,
				UpdateUser:              userID,
			},
		)
	}
	return
}

func (i *integExecTester) genTestJobcanEmployee(cnt int) (jEmps []infra.Employee) {
	for i := 1; i <= cnt; i++ {
		jEmp := infra.Employee{
			EmployeeId: uint(i),
			Code:       "EmployeeCd" + strconv.Itoa(i),
		}
		jEmps = append(jEmps, jEmp)
	}
	return
}

func (i *integExecTester) genTestJobcanSummary(cnt int) (jSumms []infra.Summary) {
	strTsDate := util.DateToTime("2022-12-01")
	for i := 1; i <= cnt; i++ {
		jSumm := infra.Summary{
			EmployeeId:   uint(i),
			Date:         strTsDate.Format("2006-01-02"),
			Work:         uint(480),
			Rest:         uint(60),
			WorkStart:    "09:00",
			WorkEnd:      "18:00",
			VacationType: "none",
		}
		jSumms = append(jSumms, jSumm)
		strTsDate = strTsDate.AddDate(0, 0, 1)
	}
	return
}

// test integration executor errors
func Test_Integration_Executor(t *testing.T) {
	// tester
	i := integExecTester{}
	// test data
	const (
		compID     = 1
		userID     = 1
		integAttID = 1
	)
	company := i.genTestCompany(compID)
	// inputs
	in := integExecArgs{
		sqsID: "xxx-yyy-zzz",
		sqsMsg: model.SQSMessage{
			UserInfo: model.SQSUserInfo{
				CompanyNo:  compID,
				EmployeeNo: userID,
			},
		},
		sqsMsgParams: integExecSqsMsgParams{
			ID: integAttID,
		},
	}
	// unexpected err
	unexpErr := twisttest.ExpReturns{
		Err:    errors.New("AAA"),
		ErrStr: "*errors.errorString",
	}

	// CASE1 ====================
	case1 := integExecTcase{
		desc:  "ERR:sqs_id required",
		input: integExecArgs{},
		expErr: twisttest.ExpReturns{
			Err:    errSQSIDReq,
			ErrStr: "*errors.errorString",
		},
	}

	// CASE2 ====================
	case2 := integExecTcase{
		desc:  "ERR:user_info required",
		input: integExecArgs{sqsID: "xxx-yyy-zzz"},
		expErr: twisttest.ExpReturns{
			Err:    errSQSMsgUserInfoReq,
			ErrStr: "*errors.errorString",
		},
	}

	// CASE3 ====================
	case3 := integExecTcase{
		desc: "ERR:company_no required",
		input: integExecArgs{
			sqsID: "xxx-yyy-zzz",
			sqsMsg: model.SQSMessage{
				UserInfo: model.SQSUserInfo{
					CompanyNo:  0,
					EmployeeNo: userID,
				},
			},
		},
		expErr: twisttest.ExpReturns{
			Err:    errSQSMsgUserInfoCompIDReq,
			ErrStr: "*errors.errorString",
		},
	}

	// CASE4 ====================
	case4 := integExecTcase{
		desc: "ERR:employee_no invalid",
		input: integExecArgs{
			sqsID: "xxx-yyy-zzz",
			sqsMsg: model.SQSMessage{
				UserInfo: model.SQSUserInfo{
					CompanyNo:  compID,
					EmployeeNo: -2,
				},
			},
		},
		expErr: twisttest.ExpReturns{
			Err:    errSQSMsgUserInfoUserIDInvalid,
			ErrStr: "*errors.errorString",
		},
	}

	// CASE5 ====================
	case5 := integExecTcase{
		desc: "ERR:params required",
		input: integExecArgs{
			sqsID: "xxx-yyy-zzz",
			sqsMsg: model.SQSMessage{
				UserInfo: model.SQSUserInfo{
					CompanyNo:  compID,
					EmployeeNo: userID,
				},
			},
		},
		expErr: twisttest.ExpReturns{
			Err:    errSQSMsgParamsReq,
			ErrStr: "*errors.errorString",
		},
	}

	// CASE6 ====================
	case6 := integExecTcase{
		desc: "ERR:params id required",
		input: integExecArgs{
			sqsID: "xxx-yyy-zzz",
			sqsMsg: model.SQSMessage{
				UserInfo: model.SQSUserInfo{
					CompanyNo:  compID,
					EmployeeNo: userID,
				},
			},
			sqsMsgParams: integExecSqsMsgParams{
				ID: -1,
			},
		},
		expErr: twisttest.ExpReturns{
			Err:    errSQSMsgParamsIDReq,
			ErrStr: "*errors.errorString",
		},
	}

	// CASE7 ====================
	mockCtrl7 := gomock.NewController(t)
	defer mockCtrl7.Finish()
	mockObj7 := mockrepo.NewMockCompany(mockCtrl7)
	mockObj7.EXPECT().FindSpecific(gomock.Any()).
		Return(tbl.Company{}, errors.New("AAA"))
	mrr7 := &test.MockRepoRegistry{}
	mrr7.MComp = mockObj7
	case7 := integExecTcase{
		desc:   "ERR:unexpected error FindSpecific(company)",
		mrr:    mrr7,
		input:  in,
		expErr: unexpErr,
	}

	// CASE8 ====================
	mockCtrl8 := gomock.NewController(t)
	defer mockCtrl8.Finish()
	mockObj8 := mockrepo.NewMockCompanyContract(mockCtrl8)
	mockObj8.EXPECT().FindOneByCompID(gomock.Any()).
		Return(tbl.CompanyContract{}, errors.New("AAA"))
	mrr8 := &test.MockRepoRegistry{}
	mrr8.MCompContract = mockObj8
	case8 := integExecTcase{
		desc:    "ERR:unexpected error FindOneByCompID(company_contract)",
		preComp: company,
		mrr:     mrr8,
		input:   in,
		expErr:  unexpErr,
		cleanDB: true,
	}

	// CASE9 ====================
	mockCtrl9 := gomock.NewController(t)
	defer mockCtrl9.Finish()
	mockObj9 := mockrepo.NewMockCompanyClosing(mockCtrl9)
	mockObj9.EXPECT().FindOneByType(gomock.Any(), gomock.Any()).
		Return(tbl.CompanyClosing{}, errors.New("AAA"))
	mrr9 := &test.MockRepoRegistry{}
	mrr9.MCompClsng = mockObj9
	case9 := integExecTcase{
		desc:    "ERR:unexpected error FindOneByType(company_closing)",
		preComp: company,
		mrr:     mrr9,
		input:   in,
		expErr:  unexpErr,
		cleanDB: true,
	}

	// CASE10 ====================
	mockCtrl10 := gomock.NewController(t)
	defer mockCtrl10.Finish()
	mockObj10 := mockrepo.NewMockEmployee(mockCtrl10)
	mockObj10.EXPECT().FindSpecific(gomock.Any(), gomock.Any(), gomock.Any()).
		Return(tbl.ExtEmployee{}, errors.New("AAA"))
	mrr10 := &test.MockRepoRegistry{}
	mrr10.MEmp = mockObj10
	case10 := integExecTcase{
		desc:    "ERR:unexpected error FindSpecific(employee)",
		preComp: company,
		mrr:     mrr10,
		input:   in,
		expErr:  unexpErr,
		cleanDB: true,
	}

	cases := []integExecTcase{case1, case2, case3, case4, case5,
		case6, case7, case8, case9, case10}

	i.execTest(t, cases)
}

// test integration attendance errors
func Test_Integration_Executor_Attendance(t *testing.T) {
	// tester
	i := integExecTester{}
	// test data
	const (
		compID       = 1
		userID       = 1
		integAttID   = 1
		systemUserID = -1
		settingID    = 10
	)

	flexNow := util.DateToTime("2023-01-01 09:01:00")
	company := i.genTestCompany(compID)
	emps := i.genTestEmployee(compID, 2)
	compClsng := i.genTestCompanyClosing(compID, userID, util.DateToTime("2022-12-31"))
	integAtt := i.genTestIntegAttendance(integAttID, compID, userID, settingID, 30, "waiting", "jobcan")
	integAtt40Days := i.genTestIntegAttendance(integAttID, compID, systemUserID, settingID, 39, "waiting", "jobcan")
	jobcanSetting := i.genTestIntegrationJobcan(settingID, compID, userID)
	tsSingleData := i.genTestTimesheet(compID, 1, false)
	tsRestSingleData := i.genTestTimesheetRest(compID, 1, false)

	// expected integration attendance success no error
	expIntegAttNoError := integAtt
	expIntegAttNoError.Status = "success"
	expIntegAttNoError.ErrSummaries = "{\"errors\":[]}"
	expIntegAttNoError.EndTime = &flexNow

	// inputs
	in := integExecArgs{
		sqsID: "xxx-yyy-zzz",
		sqsMsg: model.SQSMessage{
			UserInfo: model.SQSUserInfo{
				CompanyNo:  compID,
				EmployeeNo: userID,
			},
		},
		sqsMsgParams: integExecSqsMsgParams{
			ID: integAttID,
		},
	}

	autoIn := integExecArgs{
		sqsID: "xxx-yyy-zzz",
		sqsMsg: model.SQSMessage{
			UserInfo: model.SQSUserInfo{
				CompanyNo:  compID,
				EmployeeNo: systemUserID,
			},
		},
		sqsMsgParams: integExecSqsMsgParams{
			ID: integAttID,
		},
	}

	// CASE1 ====================
	case1 := integExecTcase{
		desc:    "ERR:integration not exist",
		preComp: company,
		preEmps: emps,
		input:   in,
		expErr: twisttest.ExpReturns{
			Err:    errors.New("record not found"),
			ErrStr: "*errors.errorString",
		},
		cleanDB: true,
	}

	// CASE2 ====================
	mockCtrl2 := gomock.NewController(t)
	defer mockCtrl2.Finish()
	mockObj2 := mockrepo.NewMockIntegrationJobcan(mockCtrl2)
	mockObj2.EXPECT().Exist(gomock.Any(), gomock.Any()).
		Return(true).AnyTimes()
	mrr2 := &test.MockRepoRegistry{}
	mrr2.MIntegJobcan = mockObj2
	expIntegAtt2 := integAtt
	expIntegAtt2.Status = "failure"
	expIntegAtt2.ErrSummaries = "{\"errors\":[{\"count\":1,\"errType\":\"target_end_date_is_before_closing_date\"}]}"
	expIntegAtt2.EndTime = &flexNow
	case2 := integExecTcase{
		desc:        "ERR:target_end_date_is_before_closing_date",
		preComp:     company,
		preEmps:     emps,
		preIntegAtt: []tbl.IntegrationAttendance{integAtt},
		expIntegAtt: []tbl.IntegrationAttendance{expIntegAtt2},
		preCmpClsng: []tbl.CompanyClosing{compClsng},
		input:       in,
		cleanDB:     true,
		flexNow:     &flexNow,
		mrr:         mrr2,
	}

	// CASE3 ====================
	mockCtrl3 := gomock.NewController(t)
	defer mockCtrl3.Finish()
	mockObj3 := mockrepo.NewMockIntegrationJobcan(mockCtrl3)
	mockObj3.EXPECT().Exist(gomock.Any(), gomock.Any()).
		Return(true).AnyTimes()
	mrr3 := &test.MockRepoRegistry{}
	mrr3.MIntegJobcan = mockObj3
	runningIntegAtt3 := integAtt
	runningIntegAtt3.Status = "running"
	toBeRunIntegAtt3 := integAtt
	toBeRunIntegAtt3.ID = 2
	in3 := in
	in3.sqsMsgParams.ID = 2
	expIntgAtt3 := toBeRunIntegAtt3
	expIntgAtt3.Status = "failure"
	expIntgAtt3.ErrSummaries = "{\"errors\":[{\"count\":1,\"errType\":\"other_integration_is_running\"}]}"
	expIntgAtt3.EndTime = &flexNow
	case3 := integExecTcase{
		desc:        "ERR:other_integration_is_running",
		preComp:     company,
		preEmps:     emps,
		preIntegAtt: []tbl.IntegrationAttendance{runningIntegAtt3, toBeRunIntegAtt3},
		expIntegAtt: []tbl.IntegrationAttendance{runningIntegAtt3, expIntgAtt3},
		input:       in3,
		cleanDB:     true,
		flexNow:     &flexNow,
		mrr:         mrr3,
	}

	// CASE4 ====================
	// special case, Mock Employee always returns error, so we cannot get system user(pk: -1)
	mrr4 := &test.MockRepoRegistry{}
	mockCtrl4 := gomock.NewController(t)
	defer mockCtrl4.Finish()
	mockObjIntegJobcan4 := mockrepo.NewMockIntegrationJobcan(mockCtrl4)
	mockObjIntegJobcan4.EXPECT().Exist(gomock.Any(), gomock.Any()).
		Return(true).AnyTimes()
	mrr4.MIntegJobcan = mockObjIntegJobcan4
	in4 := in
	mockObj4 := mockrepo.NewMockEmployee(mockCtrl4)
	gomock.InOrder(
		mockObj4.EXPECT().FindSpecific(gomock.Any(), gomock.Any(), gomock.Any()).
			Return(tbl.ExtEmployee{}, nil),
		mockObj4.EXPECT().Find(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).
			Return([]tbl.ExtEmployee{}, 0, errors.New("AAA")),
	)
	mrr4.MEmp = mockObj4
	expIntegAtt4 := integAtt
	expIntegAtt4.Status = "failure"
	expIntegAtt4.ErrSummaries = "{\"errors\":[{\"count\":1,\"errType\":\"cannot_get_crowdlog_employee\"}]}"
	expIntegAtt4.EndTime = &flexNow
	case4 := integExecTcase{
		desc:        "ERR:cannot_get_crowdlog_employee",
		preComp:     company,
		preEmps:     emps,
		preIntegAtt: []tbl.IntegrationAttendance{integAtt},
		expIntegAtt: []tbl.IntegrationAttendance{expIntegAtt4},
		input:       in4,
		expErr: twisttest.ExpReturns{
			Err:    errors.New("AAA"),
			ErrStr: "*errors.errorString",
		},
		mrr:     mrr4,
		cleanDB: true,
		flexNow: &flexNow,
	}

	// CASE5 ====================
	mrr5 := &test.MockRepoRegistry{}
	mockCtrl5 := gomock.NewController(t)
	defer mockCtrl5.Finish()
	mockObjIntegJobcan5 := mockrepo.NewMockIntegrationJobcan(mockCtrl5)
	mockObjIntegJobcan5.EXPECT().Exist(gomock.Any(), gomock.Any()).
		Return(true).AnyTimes()
	mrr5.MIntegJobcan = mockObjIntegJobcan5
	emps5 := i.genTestEmployee(compID, 2)
	emps5[0].StartDate = test.NewPtr(util.DateToTime("2023-01-01")).(*time.Time)
	emps5[1].StartDate = test.NewPtr(util.DateToTime("2023-01-01")).(*time.Time)
	expIntgAtt5 := integAtt
	expIntgAtt5.Status = "success"
	expIntgAtt5.ErrSummaries = "{\"errors\":[{\"count\":1,\"errType\":\"employee_not_found\"}]}"
	expIntgAtt5.EndTime = &flexNow
	case5 := integExecTcase{
		desc:        "ERR:employee_not_found",
		preComp:     company,
		preEmps:     emps5,
		preIntegAtt: []tbl.IntegrationAttendance{integAtt},
		expIntegAtt: []tbl.IntegrationAttendance{expIntgAtt5},
		input:       in,
		cleanDB:     true,
		flexNow:     &flexNow,
		mrr:         mrr5,
	}

	// CASE6 ====================
	mrr6 := &test.MockRepoRegistry{}
	mockCtrl6 := gomock.NewController(t)
	defer mockCtrl6.Finish()
	mockObjIntegJobcan6 := mockrepo.NewMockIntegrationJobcan(mockCtrl6)
	mockObjIntegJobcan6.EXPECT().Exist(gomock.Any(), gomock.Any()).
		Return(true).AnyTimes()
	mrr6.MIntegJobcan = mockObjIntegJobcan6
	preIntegAtt6 := integAtt
	preIntegAtt6.TargetSystem = "unknown"
	expIntegAtt6 := integAtt
	expIntegAtt6.TargetSystem = "unknown"
	expIntegAtt6.Status = "failure"
	expIntegAtt6.ErrSummaries = "{\"errors\":[{\"count\":1,\"errType\":\"unknown_target_system\"}]}"
	expIntegAtt6.EndTime = &flexNow
	case6 := integExecTcase{
		desc:        "ERR:unknown_target_system",
		preComp:     company,
		preEmps:     emps,
		preIntegAtt: []tbl.IntegrationAttendance{preIntegAtt6},
		expIntegAtt: []tbl.IntegrationAttendance{expIntegAtt6},
		input:       in,
		expErr: twisttest.ExpReturns{
			Err:    infra.ErrUnknownIntegTargetSys,
			ErrStr: "*errors.errorString",
		},
		cleanDB: true,
		flexNow: &flexNow,
		mrr:     mrr6,
	}

	// CASE7 ====================
	mrr7 := &test.MockRepoRegistry{}
	mockCtrl7 := gomock.NewController(t)
	defer mockCtrl7.Finish()
	mockObjIntegJobcan7 := mockrepo.NewMockIntegrationJobcan(mockCtrl7)
	mockObjIntegJobcan7.EXPECT().Exist(gomock.Any(), gomock.Any()).
		Return(false).AnyTimes()
	mrr7.MIntegJobcan = mockObjIntegJobcan7
	expIntegAtt7 := integAtt
	expIntegAtt7.Status = "failure"
	expIntegAtt7.ErrSummaries = "{\"errors\":[{\"count\":1,\"errType\":\"integ_setting_not_found\"}]}"
	expIntegAtt7.EndTime = &flexNow
	case7 := integExecTcase{
		desc:        "ERR:integ_setting_not_found",
		preComp:     company,
		preEmps:     emps,
		preIntegAtt: []tbl.IntegrationAttendance{integAtt},
		expIntegAtt: []tbl.IntegrationAttendance{expIntegAtt7},
		input:       in,
		expErr: twisttest.ExpReturns{
			Err:    ierror.NewNoResourceErr(),
			ErrStr: "*ierror.noResourceErr",
		},
		cleanDB: true,
		flexNow: &flexNow,
		mrr:     mrr7,
	}

	// CASE8 ====================
	mockCtrl8 := gomock.NewController(t)
	defer mockCtrl8.Finish()
	mockObjIntegJobcan8 := mockrepo.NewMockIntegrationJobcan(mockCtrl8)
	mockObjIntegJobcan8.EXPECT().Exist(gomock.Any(), gomock.Any()).
		Return(true).AnyTimes()
	mockObjIntegJobcan8.EXPECT().FindSpecific(gomock.Any(), gomock.Any()).
		Return(tbl.IntegrationJobcan{}, errors.New("AAA"))
	mrr8 := &test.MockRepoRegistry{}
	mrr8.MIntegJobcan = mockObjIntegJobcan8
	expIntegAtt8 := integAtt
	expIntegAtt8.Status = "failure"
	expIntegAtt8.ErrSummaries = "{\"errors\":[{\"count\":1,\"errType\":\"server_error\"}]}"
	expIntegAtt8.EndTime = &flexNow
	case8 := integExecTcase{
		desc:        "ERR:server_error",
		preComp:     company,
		preEmps:     emps,
		preIntegAtt: []tbl.IntegrationAttendance{integAtt},
		expIntegAtt: []tbl.IntegrationAttendance{expIntegAtt8},
		input:       in,
		expErr: twisttest.ExpReturns{
			Err:    errors.New("AAA"),
			ErrStr: "*errors.errorString",
		},
		mrr:     mrr8,
		cleanDB: true,
		flexNow: &flexNow,
	}

	// CASE9 ====================
	mrr9 := &test.MockRepoRegistry{}
	mockCtrl9 := gomock.NewController(t)
	defer mockCtrl9.Finish()
	mockObjIntegJobcan9 := mockrepo.NewMockIntegrationJobcan(mockCtrl9)
	mockObjIntegJobcan9.EXPECT().Exist(gomock.Any(), gomock.Any()).
		Return(true).AnyTimes()
	expIntegAtt9 := integAtt
	expIntegAtt9.Status = "failure"
	expIntegAtt9.ErrSummaries = "{\"errors\":[{\"count\":1,\"errType\":\"cannot_get_access_token\"}]}"
	expIntegAtt9.EndTime = &flexNow
	case9 := integExecTcase{
		desc:        "ERR:cannot_get_access_token_jobcan_token",
		preComp:     company,
		preEmps:     emps,
		preIntegAtt: []tbl.IntegrationAttendance{integAtt},
		expIntegAtt: []tbl.IntegrationAttendance{expIntegAtt9},
		preIntegJob: jobcanSetting,
		input:       in,
		expErr: twisttest.ExpReturns{
			Err:    infra.ErrAccTokenPubFaild,
			ErrStr: "*errors.errorString",
		},
		mockhttpJTkn: &mockHttpRes{
			status: http.StatusOK,
			body:   &util.H{},
		},
		cleanDB: true,
		flexNow: &flexNow,
		mrr:     mrr9,
	}

	// CASE10 ====================
	expIntegAtt10 := integAtt
	expIntegAtt10.Status = "failure"
	expIntegAtt10.ErrSummaries = "{\"errors\":[{\"count\":1,\"errType\":\"api_access_error\"}]}"
	expIntegAtt10.EndTime = &flexNow
	case10 := integExecTcase{
		desc:        "ERR:CASE10_api_access_error",
		preComp:     company,
		preEmps:     emps,
		preIntegAtt: []tbl.IntegrationAttendance{integAtt},
		expIntegAtt: []tbl.IntegrationAttendance{expIntegAtt10},
		preIntegJob: jobcanSetting,
		input:       in,
		expErr: twisttest.ExpReturns{
			Err:    infra.NewJobcanAPIAccessError(http.StatusBadRequest, "{\"error\":\"invalid_request\"}", nil),
			ErrStr: "*integration.JobcanErrAPIAccessError",
		},
		mockhttpJTkn: &mockHttpRes{
			status: http.StatusBadRequest,
			body:   &util.H{"error": "invalid_request"},
		},
		cleanDB: true,
		flexNow: &flexNow,
	}

	// CASE11 ====================
	expIntegAtt11 := integAtt
	expIntegAtt11.Status = "failure"
	expIntegAtt11.ErrSummaries = "{\"errors\":[{\"count\":1,\"errType\":\"api_access_error\"}]}"
	expIntegAtt11.EndTime = &flexNow
	case11 := integExecTcase{
		desc:        "ERR:api_access_error_jobcan_token",
		preComp:     company,
		preEmps:     emps,
		preIntegAtt: []tbl.IntegrationAttendance{integAtt},
		expIntegAtt: []tbl.IntegrationAttendance{expIntegAtt11},
		preIntegJob: jobcanSetting,
		input:       in,
		expErr: twisttest.ExpReturns{
			Err:    infra.NewJobcanAPIAccessError(http.StatusBadRequest, "{\"error\":\"invalid_request\"}", nil),
			ErrStr: "*integration.JobcanErrAPIAccessError",
		},
		mockhttpJTkn: &mockHttpRes{
			status: http.StatusBadRequest,
			body:   &util.H{"error": "invalid_request"},
		},
		cleanDB: true,
		flexNow: &flexNow,
	}

	// jobcan token success
	mockhttpJobTkn := mockHttpRes{
		status: http.StatusOK,
		body: &util.H{
			"access_token":  "token",
			"token_type":    "bearer",
			"expires_in":    3600,
			"scope":         "scope",
			"refresh_token": "rtoken",
		},
	}

	// CASE12 ====================
	expIntegAtt12 := integAtt
	expIntegAtt12.Status = "failure"
	expIntegAtt12.ErrSummaries = "{\"errors\":[{\"count\":1,\"errType\":\"api_access_error\"}]}"
	expIntegAtt12.EndTime = &flexNow
	case12 := integExecTcase{
		desc:        "ERR:server_error_jobcan_employee",
		preComp:     company,
		preEmps:     emps,
		preIntegAtt: []tbl.IntegrationAttendance{integAtt},
		expIntegAtt: []tbl.IntegrationAttendance{expIntegAtt12},
		preIntegJob: jobcanSetting,
		input:       in,
		expErr: twisttest.ExpReturns{
			Err:    infra.NewJobcanAPIAccessError(http.StatusBadRequest, "{\"detail\":\"bad_request\",\"status\":\"error\",\"status_code\":400}", nil),
			ErrStr: "*integration.JobcanErrAPIAccessError",
		},
		mockhttpJTkn: &mockhttpJobTkn,
		mockhttpJEmp: &mockHttpRes{
			status: http.StatusBadRequest,
			body: &util.H{
				"status_code": http.StatusBadRequest,
				"status":      "error",
				"detail":      "bad_request",
			},
		},
		cleanDB: true,
		flexNow: &flexNow,
	}

	// CASE13 ====================
	expIntegAtt13 := integAtt
	expIntegAtt13.Status = "failure"
	expIntegAtt13.ErrSummaries = "{\"errors\":[{\"count\":1,\"errType\":\"invalid_access_token\"}]}"
	expIntegAtt13.EndTime = &flexNow
	case13 := integExecTcase{
		desc:        "ERR:invalid_access_token_jobcan_employee",
		preComp:     company,
		preEmps:     emps,
		preIntegAtt: []tbl.IntegrationAttendance{integAtt},
		expIntegAtt: []tbl.IntegrationAttendance{expIntegAtt13},
		preIntegJob: jobcanSetting,
		input:       in,
		expErr: twisttest.ExpReturns{
			Err:    infra.NewJobcanInvalidAccessTokenError(http.StatusUnauthorized, "{\"detail\":\"unauthorized\",\"status\":\"error\",\"status_code\":401}", nil),
			ErrStr: "*integration.JobcanErrInvalidAccessToken",
		},
		mockhttpJTkn: &mockhttpJobTkn,
		mockhttpJEmp: &mockHttpRes{
			status: http.StatusUnauthorized,
			body: &util.H{
				"status_code": http.StatusUnauthorized,
				"status":      "error",
				"detail":      "unauthorized",
			},
		},
		cleanDB: true,
		flexNow: &flexNow,
	}

	// CASE14 ====================
	expIntegAtt14 := integAtt
	expIntegAtt14.Status = "failure"
	expIntegAtt14.ErrSummaries = "{\"errors\":[{\"count\":1,\"errType\":\"api_call_limit_exceeded\"}]}"
	expIntegAtt14.EndTime = &flexNow
	case14 := integExecTcase{
		desc:        "ERR:api_call_limit_exceeded_jobcan_employee",
		preComp:     company,
		preEmps:     emps,
		preIntegAtt: []tbl.IntegrationAttendance{integAtt},
		expIntegAtt: []tbl.IntegrationAttendance{expIntegAtt14},
		preIntegJob: jobcanSetting,
		input:       in,
		expErr: twisttest.ExpReturns{
			Err:    infra.NewJobcanAPICallLimitExceededError(http.StatusTooManyRequests, "{\"detail\":\"too_many_request\",\"status\":\"error\",\"status_code\":429}", nil),
			ErrStr: "*integration.JobcanErrAPICallLimitExceeded",
		},
		mockhttpJTkn: &mockhttpJobTkn,
		mockhttpJEmp: &mockHttpRes{
			status: http.StatusTooManyRequests,
			body: &util.H{
				"status_code": http.StatusTooManyRequests,
				"status":      "error",
				"detail":      "too_many_request",
			},
		},
		cleanDB: true,
		flexNow: &flexNow,
	}

	// CASE15 ====================
	expIntegAtt15 := integAtt
	expIntegAtt15.Status = "failure"
	expIntegAtt15.ErrSummaries = "{\"errors\":[{\"count\":1,\"errType\":\"api_access_error\"}]}"
	expIntegAtt15.EndTime = &flexNow
	case15 := integExecTcase{
		desc:        "ERR:api_access_error_jobcan_employee",
		preComp:     company,
		preEmps:     emps,
		preIntegAtt: []tbl.IntegrationAttendance{integAtt},
		expIntegAtt: []tbl.IntegrationAttendance{expIntegAtt15},
		preIntegJob: jobcanSetting,
		input:       in,
		expErr: twisttest.ExpReturns{
			Err:    infra.NewJobcanAPIAccessError(http.StatusInternalServerError, "{\"detail\":\"internal_server_error\",\"status\":\"error\",\"status_code\":500}", nil),
			ErrStr: "*integration.JobcanErrAPIAccessError",
		},
		mockhttpJTkn: &mockhttpJobTkn,
		mockhttpJEmp: &mockHttpRes{
			status: http.StatusInternalServerError,
			body: &util.H{
				"status_code": http.StatusInternalServerError,
				"status":      "error",
				"detail":      "internal_server_error",
			},
		},
		cleanDB: true,
		flexNow: &flexNow,
	}

	// jobcan employee success
	jobcanEmps := i.genTestJobcanEmployee(2)
	mockhttpJobEmps := mockHttpRes{
		status:   http.StatusOK,
		body:     infra.Employees{Employees: jobcanEmps},
		multiRes: true,
	}

	// CASE16 ====================
	expIntegAtt16 := integAtt
	expIntegAtt16.Status = "failure"
	expIntegAtt16.ErrSummaries = "{\"errors\":[{\"count\":1,\"errType\":\"api_access_error\"}]}"
	expIntegAtt16.EndTime = &flexNow
	case16 := integExecTcase{
		desc:        "ERR:server_error_jobcan_summary",
		preComp:     company,
		preEmps:     emps,
		preIntegAtt: []tbl.IntegrationAttendance{integAtt},
		expIntegAtt: []tbl.IntegrationAttendance{expIntegAtt16},
		preIntegJob: jobcanSetting,
		input:       in,
		expErr: twisttest.ExpReturns{
			Err:    infra.NewJobcanAPIAccessError(http.StatusForbidden, "{\"detail\":\"forbidden\",\"status\":\"error\",\"status_code\":403}", nil),
			ErrStr: "*integration.JobcanErrAPIAccessError",
		},
		mockhttpJTkn: &mockhttpJobTkn,
		mockhttpJEmp: &mockhttpJobEmps,
		mockhttpJSum: &mockHttpRes{
			status: http.StatusForbidden,
			body: &util.H{
				"status_code": http.StatusForbidden,
				"status":      "error",
				"detail":      "forbidden",
			},
		},
		cleanDB: true,
		flexNow: &flexNow,
	}

	// CASE17 ====================
	expIntegAtt17 := integAtt
	expIntegAtt17.Status = "failure"
	expIntegAtt17.ErrSummaries = "{\"errors\":[{\"count\":1,\"errType\":\"invalid_access_token\"}]}"
	expIntegAtt17.EndTime = &flexNow
	case17 := integExecTcase{
		desc:        "ERR:invalid_access_token_jobcan_summary",
		preComp:     company,
		preEmps:     emps,
		preIntegAtt: []tbl.IntegrationAttendance{integAtt},
		expIntegAtt: []tbl.IntegrationAttendance{expIntegAtt17},
		preIntegJob: jobcanSetting,
		input:       in,
		expErr: twisttest.ExpReturns{
			Err:    infra.NewJobcanInvalidAccessTokenError(http.StatusUnauthorized, "{\"detail\":\"unauthorized\",\"status\":\"error\",\"status_code\":401}", nil),
			ErrStr: "*integration.JobcanErrInvalidAccessToken",
		},
		mockhttpJTkn: &mockhttpJobTkn,
		mockhttpJEmp: &mockhttpJobEmps,
		mockhttpJSum: &mockHttpRes{
			status: http.StatusUnauthorized,
			body: &util.H{
				"status_code": http.StatusUnauthorized,
				"status":      "error",
				"detail":      "unauthorized",
			},
		},
		cleanDB: true,
		flexNow: &flexNow,
	}

	// CASE18 ====================
	expIntegAtt18 := integAtt
	expIntegAtt18.Status = "failure"
	expIntegAtt18.ErrSummaries = "{\"errors\":[{\"count\":1,\"errType\":\"api_call_limit_exceeded\"}]}"
	expIntegAtt18.EndTime = &flexNow
	case18 := integExecTcase{
		desc:        "ERR:api_call_limit_exceeded_jobcan_summary",
		preComp:     company,
		preEmps:     emps,
		preIntegAtt: []tbl.IntegrationAttendance{integAtt},
		expIntegAtt: []tbl.IntegrationAttendance{expIntegAtt18},
		preIntegJob: jobcanSetting,
		input:       in,
		expErr: twisttest.ExpReturns{
			Err:    infra.NewJobcanAPICallLimitExceededError(http.StatusTooManyRequests, "{\"detail\":\"too_many_request\",\"status\":\"error\",\"status_code\":429}", nil),
			ErrStr: "*integration.JobcanErrAPICallLimitExceeded",
		},
		mockhttpJTkn: &mockhttpJobTkn,
		mockhttpJEmp: &mockhttpJobEmps,
		mockhttpJSum: &mockHttpRes{
			status: http.StatusTooManyRequests,
			body: &util.H{
				"status_code": http.StatusTooManyRequests,
				"status":      "error",
				"detail":      "too_many_request",
			},
		},
		cleanDB: true,
		flexNow: &flexNow,
	}

	// CASE19 ====================
	expIntegAtt19 := integAtt
	expIntegAtt19.Status = "failure"
	expIntegAtt19.ErrSummaries = "{\"errors\":[{\"count\":1,\"errType\":\"api_access_error\"}]}"
	expIntegAtt19.EndTime = &flexNow
	case19 := integExecTcase{
		desc:        "ERR:api_access_error_jobcan_summary",
		preComp:     company,
		preEmps:     emps,
		preIntegAtt: []tbl.IntegrationAttendance{integAtt},
		expIntegAtt: []tbl.IntegrationAttendance{expIntegAtt19},
		preIntegJob: jobcanSetting,
		input:       in,
		expErr: twisttest.ExpReturns{
			Err:    infra.NewJobcanAPIAccessError(http.StatusInternalServerError, "{\"detail\":\"internal_server_error\",\"status\":\"error\",\"status_code\":500}", nil),
			ErrStr: "*integration.JobcanErrAPIAccessError",
		},
		mockhttpJTkn: &mockhttpJobTkn,
		mockhttpJEmp: &mockhttpJobEmps,
		mockhttpJSum: &mockHttpRes{
			status: http.StatusInternalServerError,
			body: &util.H{
				"status_code": http.StatusInternalServerError,
				"status":      "error",
				"detail":      "internal_server_error",
			},
		},
		cleanDB: true,
		flexNow: &flexNow,
	}

	// CASE20 ====================
	expIntegAtt20 := integAtt
	expIntegAtt20.Status = "success"
	expIntegAtt20.ErrSummaries = "{\"errors\":[{\"count\":1,\"errType\":\"no_attendance\"}]}"
	expIntegAtt20.EndTime = &flexNow
	case20 := integExecTcase{
		desc:         "ERR:no_attendance_jobcan_summary",
		preComp:      company,
		preEmps:      emps,
		preIntegAtt:  []tbl.IntegrationAttendance{integAtt},
		expIntegAtt:  []tbl.IntegrationAttendance{expIntegAtt20},
		preIntegJob:  jobcanSetting,
		input:        in,
		mockhttpJTkn: &mockhttpJobTkn,
		mockhttpJEmp: &mockhttpJobEmps,
		mockhttpJSum: &mockHttpRes{
			status: http.StatusOK,
			body:   infra.Summaries{Summaries: []infra.Summary{}},
		},
		cleanDB: true,
		flexNow: &flexNow,
	}

	jobSums := i.genTestJobcanSummary(1)
	expIntegdAtts := i.genIntegratedAttendance(compID, userID, integAttID, jobSums, "jobcan")
	mockhttpJobSumms := mockHttpRes{
		status:   http.StatusOK,
		body:     infra.Summaries{Summaries: jobSums},
		multiRes: true,
	}

	// CASE21 ====================
	mockCtrl21 := gomock.NewController(t)
	defer mockCtrl21.Finish()
	mockObj21 := mockrepo.NewMockTimesheet(mockCtrl21)
	mockObj21.EXPECT().FindOneByUserIDAndTimesheetDate(gomock.Any(), gomock.Any()).
		Return(tbl.Timesheet{}, errors.New("AAA"))
	mrr21 := &test.MockRepoRegistry{}
	mrr21.MTsheet = mockObj21
	expIntegAtt21 := integAtt
	expIntegAtt21.Status = "success"
	expIntegAtt21.ErrSummaries = "{\"errors\":[{\"count\":1,\"errType\":\"cannot_save_attendance\"}]}"
	expIntegAtt21.EndTime = &flexNow
	case21 := integExecTcase{
		desc:         "ERR:cannot_save_attendance_timesheet",
		preComp:      company,
		preEmps:      emps,
		preIntegAtt:  []tbl.IntegrationAttendance{integAtt},
		expIntegAtt:  []tbl.IntegrationAttendance{expIntegAtt21},
		preIntegJob:  jobcanSetting,
		expIntegdAtt: expIntegdAtts,
		input:        in,
		mockhttpJTkn: &mockhttpJobTkn,
		mockhttpJEmp: &mockhttpJobEmps,
		mockhttpJSum: &mockhttpJobSumms,
		mrr:          mrr21,
		cleanDB:      true,
		flexNow:      &flexNow,
	}

	// CASE22 ====================
	mockCtrl22 := gomock.NewController(t)
	defer mockCtrl22.Finish()
	mockObj22 := mockrepo.NewMockTimesheetRest(mockCtrl22)
	gomock.InOrder(
		mockObj22.EXPECT().FindByTimesheetID(gomock.Any()).
			Return(tsRestSingleData, nil),
		mockObj22.EXPECT().FindOneByUserIDAndRestID(gomock.Any(), gomock.Any()).
			Return(tbl.ExtTimesheetRest{}, errors.New("AAA")),
	)
	mrr22 := &test.MockRepoRegistry{}
	mrr22.MTsheetRest = mockObj22
	expIntegAtt22 := integAtt
	expIntegAtt22.Status = "success"
	expIntegAtt22.ErrSummaries = "{\"errors\":[{\"count\":1,\"errType\":\"cannot_save_attendance\"}]}"
	expIntegAtt22.EndTime = &flexNow
	case22 := integExecTcase{
		desc:         "ERR:cannot_save_attendance_delete_timesheet_rest",
		preComp:      company,
		preEmps:      emps,
		preIntegAtt:  []tbl.IntegrationAttendance{integAtt},
		expIntegAtt:  []tbl.IntegrationAttendance{expIntegAtt22},
		expTsheet:    tsSingleData,
		preIntegJob:  jobcanSetting,
		expIntegdAtt: expIntegdAtts,
		input:        in,
		mockhttpJTkn: &mockhttpJobTkn,
		mockhttpJEmp: &mockhttpJobEmps,
		mockhttpJSum: &mockhttpJobSumms,
		mrr:          mrr22,
		cleanDB:      true,
		flexNow:      &flexNow,
	}

	// CASE23 ====================
	mockCtrl23 := gomock.NewController(t)
	defer mockCtrl23.Finish()
	mockObj23 := mockrepo.NewMockTimesheetRest(mockCtrl23)
	gomock.InOrder(
		mockObj23.EXPECT().FindByTimesheetID(gomock.Any()).
			Return([]tbl.TimesheetRest{}, nil),
		mockObj23.EXPECT().Create(gomock.Any()).
			Return(errors.New("AAA")),
	)
	mrr23 := &test.MockRepoRegistry{}
	mrr23.MTsheetRest = mockObj23
	expIntegAtt23 := integAtt
	expIntegAtt23.Status = "success"
	expIntegAtt23.ErrSummaries = "{\"errors\":[{\"count\":1,\"errType\":\"cannot_save_attendance\"}]}"
	expIntegAtt23.EndTime = &flexNow
	case23 := integExecTcase{
		desc:         "ERR:cannot_save_attendance_create_timesheet_rest",
		preComp:      company,
		preEmps:      emps,
		preIntegAtt:  []tbl.IntegrationAttendance{integAtt},
		expIntegAtt:  []tbl.IntegrationAttendance{expIntegAtt23},
		expTsheet:    tsSingleData,
		preIntegJob:  jobcanSetting,
		expIntegdAtt: expIntegdAtts,
		input:        in,
		mockhttpJTkn: &mockhttpJobTkn,
		mockhttpJEmp: &mockhttpJobEmps,
		mockhttpJSum: &mockhttpJobSumms,
		mrr:          mrr23,
		cleanDB:      true,
		flexNow:      &flexNow,
	}

	// CASE24 ====================
	preIntegAtt24 := integAtt
	preIntegAtt24.TargetStartDate = util.DateToTime("2022-11-30")
	expIntegAtt24 := integAtt
	expIntegAtt24.Status = "success"
	expIntegAtt24.ErrSummaries = "{\"errors\":[{\"count\":1,\"errType\":\"target_start_date_is_changed\"}]}"
	expIntegAtt24.EndTime = &flexNow
	compClsng24 := i.genTestCompanyClosing(compID, userID, util.DateToTime("2022-11-30"))
	case24 := integExecTcase{
		desc:         "OK:target_start_date_is_changed",
		preComp:      company,
		preEmps:      emps,
		preIntegAtt:  []tbl.IntegrationAttendance{preIntegAtt24},
		expIntegAtt:  []tbl.IntegrationAttendance{expIntegAtt24},
		expTsheet:    tsSingleData,
		expTsRest:    tsRestSingleData,
		preIntegJob:  jobcanSetting,
		preCmpClsng:  []tbl.CompanyClosing{compClsng24},
		expIntegdAtt: expIntegdAtts,
		input:        in,
		mockhttpJTkn: &mockhttpJobTkn,
		mockhttpJEmp: &mockhttpJobEmps,
		mockhttpJSum: &mockhttpJobSumms,
		cleanDB:      true,
		flexNow:      &flexNow,
	}

	// CASE25 ====================
	case25 := integExecTcase{
		desc:         "OK:save_timesheet_and_rest",
		preComp:      company,
		preEmps:      emps,
		preIntegAtt:  []tbl.IntegrationAttendance{integAtt},
		expIntegAtt:  []tbl.IntegrationAttendance{expIntegAttNoError},
		preTsheet:    []tbl.Timesheet{},
		expTsheet:    tsSingleData,
		preTsRest:    []tbl.TimesheetRest{},
		expTsRest:    tsRestSingleData,
		preIntegJob:  jobcanSetting,
		expIntegdAtt: expIntegdAtts,
		input:        in,
		mockhttpJTkn: &mockhttpJobTkn,
		mockhttpJEmp: &mockhttpJobEmps,
		mockhttpJSum: &mockhttpJobSumms,
		cleanDB:      true,
		flexNow:      &flexNow,
	}

	// CASE26 ====================
	jobcanEmps26 := i.genTestJobcanEmployee(2)
	jobcanEmps26[1].Code = "NotExist"
	mockhttpJobEmps26 := mockHttpRes{
		status:   http.StatusOK,
		body:     infra.Employees{Employees: jobcanEmps26},
		multiRes: true,
	}
	expIntegAtt26 := integAtt
	expIntegAtt26.Status = "success"
	expIntegAtt26.ErrSummaries = "{\"errors\":[{\"count\":1,\"errType\":\"cannot_get_employee\"}]}"
	expIntegAtt26.EndTime = &flexNow
	case26 := integExecTcase{
		desc:         "OK:save_timesheet_and_rest_with_cannot_get_employee",
		preComp:      company,
		preEmps:      emps,
		preIntegAtt:  []tbl.IntegrationAttendance{integAtt},
		expIntegAtt:  []tbl.IntegrationAttendance{expIntegAtt26},
		preTsheet:    []tbl.Timesheet{},
		expTsheet:    tsSingleData,
		preTsRest:    []tbl.TimesheetRest{},
		expTsRest:    tsRestSingleData,
		preIntegJob:  jobcanSetting,
		expIntegdAtt: expIntegdAtts,
		input:        in,
		mockhttpJTkn: &mockhttpJobTkn,
		mockhttpJEmp: &mockhttpJobEmps26,
		mockhttpJSum: &mockhttpJobSumms,
		cleanDB:      true,
		flexNow:      &flexNow,
	}

	// CASE27 ====================
	jobSums27 := i.genTestJobcanSummary(2)
	jobSums27[1].WorkStart = "04:00"
	jobSums27[1].WorkEnd = "13:00"
	mockhttpJobSumms27 := mockHttpRes{
		status:   http.StatusOK,
		body:     infra.Summaries{Summaries: jobSums27},
		multiRes: true,
	}
	expIntegdAtts27 := i.genIntegratedAttendance(compID, userID, integAttID, jobSums27, "jobcan")
	ts27 := i.genTestTimesheet(compID, 2, true)
	ts27[1].StartTime = test.NewPtr("05:00:00").(*string)
	ts27[1].EndTime = test.NewPtr("14:00:00").(*string)
	tsRest27 := i.genTestTimesheetRest(compID, 2, true)
	tsRest27[1].StartTime = test.NewPtr("08:00:00").(*string)
	tsRest27[1].EndTime = test.NewPtr("09:00:00").(*string)
	case27 := integExecTcase{
		desc:         "OK:save_timesheet_and_rest_with_att_start_before_min_day_end_time",
		preComp:      company,
		preEmps:      emps,
		preIntegAtt:  []tbl.IntegrationAttendance{integAtt},
		expIntegAtt:  []tbl.IntegrationAttendance{expIntegAttNoError},
		preTsheet:    []tbl.Timesheet{},
		expTsheet:    ts27,
		preTsRest:    []tbl.TimesheetRest{},
		expTsRest:    tsRest27,
		preIntegJob:  jobcanSetting,
		expIntegdAtt: expIntegdAtts27,
		input:        in,
		mockhttpJTkn: &mockhttpJobTkn,
		mockhttpJEmp: &mockhttpJobEmps,
		mockhttpJSum: &mockhttpJobSumms27,
		cleanDB:      true,
		flexNow:      &flexNow,
	}

	// CASE28 ====================
	jobSums28 := i.genTestJobcanSummary(2)
	jobSums28[1].WorkStart = "21:00"
	jobSums28[1].WorkEnd = "30:00"
	mockhttpJobSumms28 := mockHttpRes{
		status:   http.StatusOK,
		body:     infra.Summaries{Summaries: jobSums28},
		multiRes: true,
	}
	expIntegdAtts28 := i.genIntegratedAttendance(compID, userID, integAttID, jobSums28, "jobcan")
	ts28 := i.genTestTimesheet(compID, 2, true)
	ts28[1].StartTime = test.NewPtr("20:00:00").(*string)
	ts28[1].EndTime = test.NewPtr("29:00:00").(*string)
	tsRest28 := i.genTestTimesheetRest(compID, 2, true)
	tsRest28[1].StartTime = test.NewPtr("23:00:00").(*string)
	tsRest28[1].EndDate = test.NewPtr(tsRest28[1].EndDate.AddDate(0, 0, 1)).(*time.Time)
	tsRest28[1].EndTime = test.NewPtr("00:00:00").(*string)
	case28 := integExecTcase{
		desc:         "OK:save_timesheet_and_rest_att_end_after_max_day_end_time",
		preComp:      company,
		preEmps:      emps,
		preIntegAtt:  []tbl.IntegrationAttendance{integAtt},
		expIntegAtt:  []tbl.IntegrationAttendance{expIntegAttNoError},
		preTsheet:    []tbl.Timesheet{},
		expTsheet:    ts28,
		preTsRest:    []tbl.TimesheetRest{},
		expTsRest:    tsRest28,
		preIntegJob:  jobcanSetting,
		expIntegdAtt: expIntegdAtts28,
		input:        in,
		mockhttpJTkn: &mockhttpJobTkn,
		mockhttpJEmp: &mockhttpJobEmps,
		mockhttpJSum: &mockhttpJobSumms28,
		cleanDB:      true,
		flexNow:      &flexNow,
	}

	// CASE29 ====================
	jobSums29 := i.genTestJobcanSummary(2)
	jobSums29[1].WorkStart = "04:00"
	jobSums29[1].WorkEnd = "30:00"
	mockhttpJobSumms29 := mockHttpRes{
		status:   http.StatusOK,
		body:     infra.Summaries{Summaries: jobSums29},
		multiRes: true,
	}
	expIntegdAtts29 := i.genIntegratedAttendance(compID, userID, integAttID, jobSums29, "jobcan")
	expIntegAtt29 := integAtt
	expIntegAtt29.Status = "success"
	expIntegAtt29.ErrSummaries = "{\"errors\":[{\"count\":1,\"errType\":\"cannot_convert_attendance\"}]}"
	expIntegAtt29.EndTime = &flexNow
	case29 := integExecTcase{
		desc:         "OK:save_timesheet_and_rest_with_cannot_convert_attendance_more_than_one_day",
		preComp:      company,
		preEmps:      emps,
		preIntegAtt:  []tbl.IntegrationAttendance{integAtt},
		expIntegAtt:  []tbl.IntegrationAttendance{expIntegAtt29},
		preTsheet:    []tbl.Timesheet{},
		expTsheet:    tsSingleData,
		preTsRest:    []tbl.TimesheetRest{},
		expTsRest:    tsRestSingleData,
		preIntegJob:  jobcanSetting,
		expIntegdAtt: expIntegdAtts29,
		input:        in,
		mockhttpJTkn: &mockhttpJobTkn,
		mockhttpJEmp: &mockhttpJobEmps,
		mockhttpJSum: &mockhttpJobSumms29,
		cleanDB:      true,
		flexNow:      &flexNow,
	}

	// CASE30 ====================
	jobSums30 := i.genTestJobcanSummary(1)
	jobSums30[0].Work = uint(0)
	jobSums30[0].WorkStart = ""
	jobSums30[0].WorkEnd = ""
	jobSums30[0].VacationType = "paid"
	jobSums30[0].Rest = uint(0)
	mockhttpJobSumms30 := mockHttpRes{
		status:   http.StatusOK,
		body:     infra.Summaries{Summaries: jobSums30},
		multiRes: true,
	}

	expIntegdAtts30 := i.genIntegratedAttendance(compID, userID, integAttID, jobSums30, "jobcan")
	expIntegdAtts30[0].StartTime = "00:00:00"
	expIntegdAtts30[0].EndTime = "00:00:00"
	ts30 := i.genTestTimesheet(compID, 1, false)
	ts30[0].HolidayType = test.NewPtr(1).(*int)
	case30 := integExecTcase{
		desc:         "OK:save_timesheet_day_off",
		preComp:      company,
		preEmps:      emps,
		preIntegAtt:  []tbl.IntegrationAttendance{integAtt},
		expIntegAtt:  []tbl.IntegrationAttendance{expIntegAttNoError},
		preTsheet:    []tbl.Timesheet{},
		expTsheet:    ts30,
		preTsRest:    []tbl.TimesheetRest{},
		preIntegJob:  jobcanSetting,
		expIntegdAtt: expIntegdAtts30,
		input:        in,
		mockhttpJTkn: &mockhttpJobTkn,
		mockhttpJEmp: &mockhttpJobEmps,
		mockhttpJSum: &mockhttpJobSumms30,
		cleanDB:      true,
		flexNow:      &flexNow,
	}

	// CASE31 ====================
	jobSums31 := i.genTestJobcanSummary(1)
	jobSums31[0].Work = uint(240)
	jobSums31[0].WorkStart = "14:00"
	jobSums31[0].VacationType = "paid"
	jobSums31[0].Rest = uint(0)
	mockhttpJobSumms31 := mockHttpRes{
		status:   http.StatusOK,
		body:     infra.Summaries{Summaries: jobSums31},
		multiRes: true,
	}
	expIntegdAtts31 := i.genIntegratedAttendance(compID, userID, integAttID, jobSums31, "jobcan")
	ts31 := i.genTestTimesheet(compID, 1, false)
	ts31[0].StartTime = test.NewPtr("14:00:00").(*string)
	ts31[0].HolidayType = test.NewPtr(2).(*int)
	case31 := integExecTcase{
		desc:         "OK:save_timesheet_morning_off",
		preComp:      company,
		preEmps:      emps,
		preIntegAtt:  []tbl.IntegrationAttendance{integAtt},
		expIntegAtt:  []tbl.IntegrationAttendance{expIntegAttNoError},
		preTsheet:    []tbl.Timesheet{},
		expTsheet:    ts31,
		preTsRest:    []tbl.TimesheetRest{},
		preIntegJob:  jobcanSetting,
		expIntegdAtt: expIntegdAtts31,
		input:        in,
		mockhttpJTkn: &mockhttpJobTkn,
		mockhttpJEmp: &mockhttpJobEmps,
		mockhttpJSum: &mockhttpJobSumms31,
		cleanDB:      true,
		flexNow:      &flexNow,
	}

	// CASE32 ====================
	jobSums32 := i.genTestJobcanSummary(1)
	jobSums32[0].Work = uint(240)
	jobSums32[0].WorkEnd = "13:00"
	jobSums32[0].VacationType = "paid"
	jobSums32[0].Rest = uint(0)
	mockhttpJobSumms32 := mockHttpRes{
		status:   http.StatusOK,
		body:     infra.Summaries{Summaries: jobSums32},
		multiRes: true,
	}
	expIntegdAtts32 := i.genIntegratedAttendance(compID, userID, integAttID, jobSums32, "jobcan")
	ts32 := i.genTestTimesheet(compID, 1, false)
	ts32[0].EndTime = test.NewPtr("13:00:00").(*string)
	ts32[0].HolidayType = test.NewPtr(3).(*int)
	case32 := integExecTcase{
		desc:         "OK:save_timesheet_afternoon_off",
		preComp:      company,
		preEmps:      emps,
		preIntegAtt:  []tbl.IntegrationAttendance{integAtt},
		expIntegAtt:  []tbl.IntegrationAttendance{expIntegAttNoError},
		preTsheet:    []tbl.Timesheet{},
		expTsheet:    ts32,
		preTsRest:    []tbl.TimesheetRest{},
		preIntegJob:  jobcanSetting,
		expIntegdAtt: expIntegdAtts32,
		input:        in,
		mockhttpJTkn: &mockhttpJobTkn,
		mockhttpJEmp: &mockhttpJobEmps,
		mockhttpJSum: &mockhttpJobSumms32,
		cleanDB:      true,
		flexNow:      &flexNow,
	}

	// CASE33 ====================
	preTs33 := i.genTestTimesheet(compID, 1, false)
	expTs33 := i.genTestTimesheet(compID, 1, false)
	preTs33[0].StartTime = test.NewPtr("08:50:00").(*string)
	preTs33[0].EndTime = test.NewPtr("11:50:00").(*string)
	// To check auto-calc is off in case of attendance sync
	preTs33[0].AutoCalculationAttendance = test.NewPtr(true).(*bool)
	expTs33[0].AutoCalculationAttendance = test.NewPtr(false).(*bool)
	preTsRest33 := i.genTestTimesheetRest(compID, 1, false)
	preTsRest33[0].StartTime = test.NewPtr("11:50:00").(*string)
	preTsRest33[0].EndTime = test.NewPtr("12:50:00").(*string)
	// rest is deleted and created, and rest time is not updated
	expTsRest33 := i.genTestTimesheetRest(compID, 1, false)
	expTsRest33[0].RestNo = 2
	expTsRest33[0].StartTime = test.NewPtr("11:50:00").(*string)
	expTsRest33[0].EndTime = test.NewPtr("12:50:00").(*string)
	preIntegdAtts33 := i.genIntegratedAttendance(compID, userID, integAttID, jobSums, "jobcan")
	preIntegdAtts33[0].StartTime = "07:00:00"
	preIntegdAtts33[0].EndTime = "16:00:00"
	case33 := integExecTcase{
		desc:         "OK:update_timesheet_and_rest_and_integrated_attendance",
		preComp:      company,
		preEmps:      emps,
		preIntegAtt:  []tbl.IntegrationAttendance{integAtt},
		expIntegAtt:  []tbl.IntegrationAttendance{expIntegAttNoError},
		preTsheet:    preTs33,
		expTsheet:    expTs33,
		preTsRest:    preTsRest33,
		expTsRest:    expTsRest33,
		preIntegJob:  jobcanSetting,
		preIntegdAtt: preIntegdAtts33,
		expIntegdAtt: expIntegdAtts,
		input:        in,
		mockhttpJTkn: &mockhttpJobTkn,
		mockhttpJEmp: &mockhttpJobEmps,
		mockhttpJSum: &mockhttpJobSumms,
		cleanDB:      true,
		flexNow:      &flexNow,
	}

	// CASE34 ====================
	expIntegAtt34 := integAtt
	expIntegAtt34.Status = "success"
	expIntegAtt34.ErrSummaries = "{\"errors\":[{\"count\":1,\"errType\":\"cannot_save_attendance\"}]}"
	expIntegAtt34.EndTime = &flexNow
	expTs34 := i.genTestTimesheet(compID, 1, false)
	expTs34[0].ApprovalStatus = test.NewPtr(1).(*int)
	case34 := integExecTcase{
		desc:         "OK:cannot_save_timesheet_and_rest_status_pending",
		preComp:      company,
		preEmps:      emps,
		preIntegAtt:  []tbl.IntegrationAttendance{integAtt},
		expIntegAtt:  []tbl.IntegrationAttendance{expIntegAtt34},
		preTsheet:    expTs34,
		expTsheet:    expTs34,
		preTsRest:    tsRestSingleData,
		expTsRest:    tsRestSingleData,
		preIntegJob:  jobcanSetting,
		expIntegdAtt: expIntegdAtts,
		input:        in,
		mockhttpJTkn: &mockhttpJobTkn,
		mockhttpJEmp: &mockhttpJobEmps,
		mockhttpJSum: &mockhttpJobSumms,
		cleanDB:      true,
		flexNow:      &flexNow,
	}

	// CASE35 ====================
	expIntegAtt35 := integAtt
	expIntegAtt35.Status = "success"
	expIntegAtt35.ErrSummaries = "{\"errors\":[{\"count\":1,\"errType\":\"cannot_save_attendance\"}]}"
	expIntegAtt35.EndTime = &flexNow
	expTs35 := i.genTestTimesheet(compID, 1, false)
	expTs35[0].ApprovalStatus = test.NewPtr(2).(*int)
	case35 := integExecTcase{
		desc:         "OK:cannot_save_timesheet_and_rest_status_approved",
		preComp:      company,
		preEmps:      emps,
		preIntegAtt:  []tbl.IntegrationAttendance{integAtt},
		expIntegAtt:  []tbl.IntegrationAttendance{expIntegAtt35},
		preTsheet:    expTs35,
		expTsheet:    expTs35,
		preTsRest:    tsRestSingleData,
		expTsRest:    tsRestSingleData,
		preIntegJob:  jobcanSetting,
		expIntegdAtt: expIntegdAtts,
		input:        in,
		mockhttpJTkn: &mockhttpJobTkn,
		mockhttpJEmp: &mockhttpJobEmps,
		mockhttpJSum: &mockhttpJobSumms,
		cleanDB:      true,
		flexNow:      &flexNow,
	}

	// CASE36 ====================
	preTs36 := i.genTestTimesheet(compID, 1, false)
	preTs36[0].ApprovalStatus = test.NewPtr(3).(*int)
	expTsRest36 := i.genTestTimesheetRest(compID, 1, false)
	expTsRest36[0].RestNo = 2 // rest is deleted and created
	case36 := integExecTcase{
		desc:         "OK:save_timesheet_and_rest_status_rejected",
		preComp:      company,
		preEmps:      emps,
		preIntegAtt:  []tbl.IntegrationAttendance{integAtt},
		expIntegAtt:  []tbl.IntegrationAttendance{expIntegAttNoError},
		preTsheet:    preTs36,
		expTsheet:    tsSingleData,
		preTsRest:    tsRestSingleData,
		expTsRest:    expTsRest36,
		preIntegJob:  jobcanSetting,
		expIntegdAtt: expIntegdAtts,
		input:        in,
		mockhttpJTkn: &mockhttpJobTkn,
		mockhttpJEmp: &mockhttpJobEmps,
		mockhttpJSum: &mockhttpJobSumms,
		cleanDB:      true,
		flexNow:      &flexNow,
	}

	// CASE37 ====================
	preIntegAtt37 := integAtt
	preIntegAtt37.ExecutorID = systemUserID
	preIntegAtt37.CreateUser = systemUserID
	preIntegAtt37.UpdateUser = systemUserID
	expIntegAtt37 := preIntegAtt37
	expIntegAtt37.Status = "success"
	expIntegAtt37.ErrSummaries = "{\"errors\":[]}"
	expIntegAtt37.EndTime = &flexNow
	case37 := integExecTcase{
		desc:         "OK:save_timesheet_and_rest_system_generated",
		preComp:      company,
		preEmps:      emps,
		preIntegAtt:  []tbl.IntegrationAttendance{preIntegAtt37},
		expIntegAtt:  []tbl.IntegrationAttendance{expIntegAtt37},
		preTsheet:    []tbl.Timesheet{},
		expTsheet:    tsSingleData,
		preTsRest:    []tbl.TimesheetRest{},
		expTsRest:    tsRestSingleData,
		preIntegJob:  jobcanSetting,
		expIntegdAtt: i.genIntegratedAttendance(compID, systemUserID, integAttID, jobSums, "jobcan"),
		input:        autoIn,
		mockhttpJTkn: &mockhttpJobTkn,
		mockhttpJEmp: &mockhttpJobEmps,
		mockhttpJSum: &mockhttpJobSumms,
		cleanDB:      true,
		flexNow:      &flexNow,
	}

	// CASE38 ====================
	mockCtrl38 := gomock.NewController(t)
	defer mockCtrl38.Finish()
	mockObj38 := mockrepo.NewMockIntegrationAttendance(mockCtrl38)
	gomock.InOrder(
		mockObj38.EXPECT().FindSpecific(gomock.Any(), gomock.Any(), gomock.Any()).
			Return(tbl.ExtIntegrationAttendance{IntegrationAttendance: integAtt}, nil),
		mockObj38.EXPECT().UpdateStatusFromWaiting(gomock.Any(), gomock.Any(), gomock.Any()).
			Return(true, nil),
		mockObj38.EXPECT().ExistRunningIntegrationExcept(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).
			Return(false),
		mockObj38.EXPECT().Update(gomock.Any()).
			Return(errors.New("AAA")),
	)
	mrr38 := &test.MockRepoRegistry{}
	mrr38.MIntegAtt = mockObj38
	case38 := integExecTcase{
		desc:         "OK:save_timesheet_and_rest_unable_to_update_result",
		preComp:      company,
		preEmps:      emps,
		preIntegAtt:  []tbl.IntegrationAttendance{integAtt},
		expIntegAtt:  []tbl.IntegrationAttendance{integAtt},
		preTsheet:    []tbl.Timesheet{},
		expTsheet:    tsSingleData,
		preTsRest:    []tbl.TimesheetRest{},
		expTsRest:    tsRestSingleData,
		preIntegJob:  jobcanSetting,
		expIntegdAtt: expIntegdAtts,
		input:        in,
		mockhttpJTkn: &mockhttpJobTkn,
		mockhttpJEmp: &mockhttpJobEmps,
		mockhttpJSum: &mockhttpJobSumms,
		mrr:          mrr38,
		cleanDB:      true,
		flexNow:      &flexNow,
	}

	// CASE39 ====================
	mockCtrl39 := gomock.NewController(t)
	defer mockCtrl39.Finish()
	mockObj39 := mockrepo.NewMockIntegratedAttendance(mockCtrl39)
	mockObj39.EXPECT().FindSpecific(gomock.Any(), gomock.Any(), gomock.Any()).
		Return(tbl.IntegratedAttendance{}, gorm.ErrRecordNotFound).AnyTimes()
	mockObj39.EXPECT().Create(gomock.Any()).Return(errors.New("AAA")).AnyTimes()

	mrr39 := &test.MockRepoRegistry{}
	mrr39.MIntegedAtt = mockObj39
	expIntegAtt39 := integAtt
	expIntegAtt39.Status = "success"
	expIntegAtt39.ErrSummaries = "{\"errors\":[{\"count\":1,\"errType\":\"cannot_save_attendance\"}]}"
	expIntegAtt39.EndTime = &flexNow
	case39 := integExecTcase{
		desc:         "ERR:unexpected_err_create_integrated_attendance",
		preComp:      company,
		preEmps:      emps,
		preIntegAtt:  []tbl.IntegrationAttendance{integAtt},
		expIntegAtt:  []tbl.IntegrationAttendance{expIntegAtt39},
		preIntegJob:  jobcanSetting,
		input:        in,
		mockhttpJTkn: &mockhttpJobTkn,
		mockhttpJEmp: &mockhttpJobEmps,
		mockhttpJSum: &mockhttpJobSumms,
		mrr:          mrr39,
		cleanDB:      true,
		flexNow:      &flexNow,
	}

	// CASE40 ====================
	integdAtts40 := i.genIntegratedAttendance(compID, userID, integAttID, jobSums, "jobcan")
	mockCtrl40 := gomock.NewController(t)
	defer mockCtrl40.Finish()
	mockObj40 := mockrepo.NewMockIntegratedAttendance(mockCtrl40)
	mockObj40.EXPECT().FindSpecific(gomock.Any(), gomock.Any(), gomock.Any()).
		Return(integdAtts40[0], nil).AnyTimes()
	mockObj40.EXPECT().Update(gomock.Any()).Return(errors.New("AAA")).AnyTimes()

	mrr40 := &test.MockRepoRegistry{}
	mrr40.MIntegedAtt = mockObj40
	expIntegAtt40 := integAtt
	expIntegAtt40.Status = "success"
	expIntegAtt40.ErrSummaries = "{\"errors\":[{\"count\":1,\"errType\":\"cannot_save_attendance\"}]}"
	expIntegAtt40.EndTime = &flexNow
	case40 := integExecTcase{
		desc:         "ERR:unexpected_err_update_integrated_attendance",
		preComp:      company,
		preEmps:      emps,
		preIntegAtt:  []tbl.IntegrationAttendance{integAtt},
		expIntegAtt:  []tbl.IntegrationAttendance{expIntegAtt40},
		preIntegJob:  jobcanSetting,
		input:        in,
		mockhttpJTkn: &mockhttpJobTkn,
		mockhttpJEmp: &mockhttpJobEmps,
		mockhttpJSum: &mockhttpJobSumms,
		mrr:          mrr40,
		cleanDB:      true,
		flexNow:      &flexNow,
	}

	// CASE41 ====================
	preTsRest41 := i.genTestTimesheetRest(compID, 3, false)
	preTsRest41[0].TimesheetNo = 1
	preTsRest41[1].TimesheetNo = 1
	preTsRest41[2].TimesheetNo = 1
	preTsRest41[0].StartTime = test.NewPtr("10:00:00").(*string)
	preTsRest41[0].EndTime = test.NewPtr("10:15:00").(*string)
	preTsRest41[1].StartTime = test.NewPtr("12:00:00").(*string)
	preTsRest41[1].EndTime = test.NewPtr("12:30:00").(*string)
	preTsRest41[2].StartTime = test.NewPtr("15:00:00").(*string)
	preTsRest41[2].EndTime = test.NewPtr("15:15:00").(*string)
	// rest is deleted and created, and rest time is not updated
	expTsRest41 := i.genTestTimesheetRest(compID, 3, false)
	expTsRest41[0].RestNo = 4
	expTsRest41[1].RestNo = 5
	expTsRest41[2].RestNo = 6
	expTsRest41[0].TimesheetNo = 1
	expTsRest41[1].TimesheetNo = 1
	expTsRest41[2].TimesheetNo = 1
	expTsRest41[0].StartTime = test.NewPtr("10:00:00").(*string)
	expTsRest41[0].EndTime = test.NewPtr("10:15:00").(*string)
	expTsRest41[1].StartTime = test.NewPtr("12:00:00").(*string)
	expTsRest41[1].EndTime = test.NewPtr("12:30:00").(*string)
	expTsRest41[2].StartTime = test.NewPtr("15:00:00").(*string)
	expTsRest41[2].EndTime = test.NewPtr("15:15:00").(*string)
	case41 := integExecTcase{
		desc:         "OK:multiple_rest_no_change_of_time",
		preComp:      company,
		preEmps:      emps,
		preIntegAtt:  []tbl.IntegrationAttendance{integAtt},
		expIntegAtt:  []tbl.IntegrationAttendance{expIntegAttNoError},
		preTsheet:    tsSingleData,
		expTsheet:    tsSingleData,
		preTsRest:    preTsRest41,
		expTsRest:    expTsRest41,
		preIntegJob:  jobcanSetting,
		expIntegdAtt: expIntegdAtts,
		input:        in,
		mockhttpJTkn: &mockhttpJobTkn,
		mockhttpJEmp: &mockhttpJobEmps,
		mockhttpJSum: &mockhttpJobSumms,
		cleanDB:      true,
		flexNow:      &flexNow,
	}

	// CASE42 ====================
	preTsRest42 := i.genTestTimesheetRest(compID, 3, false)
	preTsRest42[0].TimesheetNo = 1
	preTsRest42[1].TimesheetNo = 1
	preTsRest42[2].TimesheetNo = 1 // delete this rest
	preTsRest42[0].StartTime = test.NewPtr("10:00:00").(*string)
	preTsRest42[0].EndTime = test.NewPtr("10:15:00").(*string)
	preTsRest42[1].StartTime = test.NewPtr("12:00:00").(*string)
	preTsRest42[1].EndTime = test.NewPtr("12:45:00").(*string)
	preTsRest42[2].StartTime = test.NewPtr("15:00:00").(*string) // delete this rest
	preTsRest42[2].EndTime = test.NewPtr("15:15:00").(*string)   // delete this rest
	// rest is deleted and created, and rest time is not updated
	expTsRest42 := i.genTestTimesheetRest(compID, 2, false)
	expTsRest42[0].RestNo = 4
	expTsRest42[1].RestNo = 5
	expTsRest42[0].TimesheetNo = 1
	expTsRest42[1].TimesheetNo = 1
	expTsRest42[0].StartTime = test.NewPtr("10:00:00").(*string)
	expTsRest42[0].EndTime = test.NewPtr("10:15:00").(*string)
	expTsRest42[1].StartTime = test.NewPtr("12:00:00").(*string)
	expTsRest42[1].EndTime = test.NewPtr("12:45:00").(*string)
	case42 := integExecTcase{
		desc:         "OK:multiple_rest_delete_excess_ts_rest",
		preComp:      company,
		preEmps:      emps,
		preIntegAtt:  []tbl.IntegrationAttendance{integAtt},
		expIntegAtt:  []tbl.IntegrationAttendance{expIntegAttNoError},
		preTsheet:    tsSingleData,
		expTsheet:    tsSingleData,
		preTsRest:    preTsRest42,
		expTsRest:    expTsRest42,
		preIntegJob:  jobcanSetting,
		expIntegdAtt: expIntegdAtts,
		input:        in,
		mockhttpJTkn: &mockhttpJobTkn,
		mockhttpJEmp: &mockhttpJobEmps,
		mockhttpJSum: &mockhttpJobSumms,
		cleanDB:      true,
		flexNow:      &flexNow,
	}

	// CASE43 ====================
	jobSums43 := i.genTestJobcanSummary(1)
	jobSums43[0].Rest = 105
	mockhttpJobSumms43 := mockHttpRes{
		status:   http.StatusOK,
		body:     infra.Summaries{Summaries: jobSums43},
		multiRes: true,
	}
	expIntegdAtts43 := i.genIntegratedAttendance(compID, userID, integAttID, jobSums43, "jobcan")
	preTsRest43 := i.genTestTimesheetRest(compID, 3, false)
	preTsRest43[0].TimesheetNo = 1
	preTsRest43[1].TimesheetNo = 1
	preTsRest43[2].TimesheetNo = 1
	preTsRest43[0].StartTime = test.NewPtr("16:30:00").(*string)
	preTsRest43[0].EndTime = test.NewPtr("16:45:00").(*string)
	preTsRest43[1].StartTime = test.NewPtr("17:00:00").(*string)
	preTsRest43[1].EndTime = test.NewPtr("17:15:00").(*string)
	preTsRest43[2].StartTime = test.NewPtr("17:30:00").(*string)
	preTsRest43[2].EndTime = test.NewPtr("17:45:00").(*string)
	// rest is deleted and created, and rest time is not updated
	expTsRest43 := i.genTestTimesheetRest(compID, 3, false)
	expTsRest43[0].RestNo = 4
	expTsRest43[1].RestNo = 5
	expTsRest43[2].RestNo = 6
	expTsRest43[0].TimesheetNo = 1
	expTsRest43[1].TimesheetNo = 1
	expTsRest43[2].TimesheetNo = 1
	expTsRest43[0].StartTime = test.NewPtr("16:15:00").(*string) // -15 minutes
	expTsRest43[0].EndTime = test.NewPtr("17:00:00").(*string)   // +15 minutes
	expTsRest43[1].StartTime = test.NewPtr("17:00:00").(*string)
	expTsRest43[1].EndTime = test.NewPtr("17:30:00").(*string) // +15 minutes
	expTsRest43[2].StartTime = test.NewPtr("17:30:00").(*string)
	expTsRest43[2].EndTime = test.NewPtr("18:00:00").(*string) // +15 minutes
	case43 := integExecTcase{
		desc:         "OK:multiple_rest_add_rest_time_to_latest(up)_middle(up)_and_earliest(up_and_down)_rest",
		preComp:      company,
		preEmps:      emps,
		preIntegAtt:  []tbl.IntegrationAttendance{integAtt},
		expIntegAtt:  []tbl.IntegrationAttendance{expIntegAttNoError},
		preTsheet:    tsSingleData,
		expTsheet:    tsSingleData,
		preTsRest:    preTsRest43,
		expTsRest:    expTsRest43,
		preIntegJob:  jobcanSetting,
		expIntegdAtt: expIntegdAtts43,
		input:        in,
		mockhttpJTkn: &mockhttpJobTkn,
		mockhttpJEmp: &mockhttpJobEmps,
		mockhttpJSum: &mockhttpJobSumms43,
		cleanDB:      true,
		flexNow:      &flexNow,
	}

	// CASE44 ====================
	preTsRest44 := i.genTestTimesheetRest(compID, 5, false)
	preTsRest44[0].TimesheetNo = 1
	preTsRest44[1].TimesheetNo = 1
	preTsRest44[2].TimesheetNo = 1
	preTsRest44[3].TimesheetNo = 1
	preTsRest44[4].TimesheetNo = 1
	preTsRest44[0].StartTime = test.NewPtr("08:00:00").(*string) // outside work
	preTsRest44[0].EndTime = test.NewPtr("08:30:00").(*string)
	preTsRest44[1].StartTime = test.NewPtr("08:45:00").(*string) // inside work
	preTsRest44[1].EndTime = test.NewPtr("09:15:00").(*string)
	preTsRest44[2].StartTime = test.NewPtr("12:00:00").(*string) // inside work
	preTsRest44[2].EndTime = test.NewPtr("12:15:00").(*string)
	preTsRest44[3].StartTime = test.NewPtr("17:45:00").(*string) // inside work
	preTsRest44[3].EndTime = test.NewPtr("18:15:00").(*string)
	preTsRest44[4].StartTime = test.NewPtr("18:30:00").(*string) // outside work
	preTsRest44[4].EndTime = test.NewPtr("19:00:00").(*string)
	// rest is deleted and created, and rest time is not updated
	expTsRest44 := i.genTestTimesheetRest(compID, 5, false)
	expTsRest44[1].RestNo = 6
	expTsRest44[2].RestNo = 7
	expTsRest44[3].RestNo = 8
	expTsRest44[0].TimesheetNo = 1
	expTsRest44[1].TimesheetNo = 1
	expTsRest44[2].TimesheetNo = 1
	expTsRest44[3].TimesheetNo = 1
	expTsRest44[4].TimesheetNo = 1
	expTsRest44[0].StartTime = test.NewPtr("08:00:00").(*string) // outside work
	expTsRest44[0].EndTime = test.NewPtr("08:30:00").(*string)
	expTsRest44[1].StartTime = test.NewPtr("09:00:00").(*string) // update start
	expTsRest44[1].EndTime = test.NewPtr("09:15:00").(*string)
	expTsRest44[2].StartTime = test.NewPtr("12:00:00").(*string)
	expTsRest44[2].EndTime = test.NewPtr("12:30:00").(*string)   // +15 minutes
	expTsRest44[3].StartTime = test.NewPtr("17:45:00").(*string) // update end
	expTsRest44[3].EndTime = test.NewPtr("18:00:00").(*string)
	expTsRest44[4].StartTime = test.NewPtr("18:30:00").(*string) // outside work
	expTsRest44[4].EndTime = test.NewPtr("19:00:00").(*string)
	case44 := integExecTcase{
		desc:         "OK:multiple_rest_disregard_outside_rest",
		preComp:      company,
		preEmps:      emps,
		preIntegAtt:  []tbl.IntegrationAttendance{integAtt},
		expIntegAtt:  []tbl.IntegrationAttendance{expIntegAttNoError},
		preTsheet:    tsSingleData,
		expTsheet:    tsSingleData,
		preTsRest:    preTsRest44,
		expTsRest:    expTsRest44,
		preIntegJob:  jobcanSetting,
		expIntegdAtt: expIntegdAtts,
		input:        in,
		mockhttpJTkn: &mockhttpJobTkn,
		mockhttpJEmp: &mockhttpJobEmps,
		mockhttpJSum: &mockhttpJobSumms,
		cleanDB:      true,
		flexNow:      &flexNow,
	}

	// CASE45 ====================
	jobSums45 := i.genTestJobcanSummary(1)
	jobSums45[0].WorkStart = "08:00"
	jobSums45[0].WorkEnd = "09:00"
	jobSums45[0].Rest = 75
	mockhttpJobSumms45 := mockHttpRes{
		status:   http.StatusOK,
		body:     infra.Summaries{Summaries: jobSums45},
		multiRes: true,
	}
	expIntegdAtts45 := i.genIntegratedAttendance(compID, userID, integAttID, jobSums45, "jobcan")
	ts45 := i.genTestTimesheet(compID, 1, false)
	ts45[0].StartTime = test.NewPtr("08:00:00").(*string)
	ts45[0].EndTime = test.NewPtr("09:00:00").(*string)
	expIntegAtt45 := integAtt
	expIntegAtt45.Status = "success"
	expIntegAtt45.ErrSummaries = "{\"errors\":[{\"count\":1,\"errType\":\"cannot_convert_attendance\"}]}"
	expIntegAtt45.EndTime = &flexNow
	case45 := integExecTcase{
		desc:         "OK:cannot_convert_attendance_rest_more_than_work_time",
		preComp:      company,
		preEmps:      emps,
		preIntegAtt:  []tbl.IntegrationAttendance{integAtt},
		expIntegAtt:  []tbl.IntegrationAttendance{expIntegAtt45},
		preTsheet:    []tbl.Timesheet{},
		expTsheet:    ts45,
		preIntegJob:  jobcanSetting,
		expIntegdAtt: expIntegdAtts45,
		input:        in,
		mockhttpJTkn: &mockhttpJobTkn,
		mockhttpJEmp: &mockhttpJobEmps,
		mockhttpJSum: &mockhttpJobSumms45,
		cleanDB:      true,
		flexNow:      &flexNow,
	}

	// CASE46 ====================
	jobSums46 := i.genTestJobcanSummary(1)
	jobSums46[0].Rest = 0
	mockhttpJobSumms46 := mockHttpRes{
		status:   http.StatusOK,
		body:     infra.Summaries{Summaries: jobSums46},
		multiRes: true,
	}
	expIntegdAtts46 := i.genIntegratedAttendance(compID, userID, integAttID, jobSums46, "jobcan")
	case46 := integExecTcase{
		desc:         "OK:delete_timesheet_rest",
		preComp:      company,
		preEmps:      emps,
		preIntegAtt:  []tbl.IntegrationAttendance{integAtt},
		expIntegAtt:  []tbl.IntegrationAttendance{expIntegAttNoError},
		preTsheet:    tsSingleData,
		expTsheet:    tsSingleData,
		preIntegJob:  jobcanSetting,
		expIntegdAtt: expIntegdAtts46,
		preTsRest:    tsRestSingleData,
		expTsRest:    []tbl.TimesheetRest{},
		input:        in,
		mockhttpJTkn: &mockhttpJobTkn,
		mockhttpJEmp: &mockhttpJobEmps,
		mockhttpJSum: &mockhttpJobSumms46,
		cleanDB:      true,
		flexNow:      &flexNow,
	}

	// CASE47 ====================
	preIntegAtt47 := integAtt40Days

	expIntegAtt47 := preIntegAtt47
	expIntegAtt47.Status = "success"
	expIntegAtt47.ErrSummaries = "{\"errors\":[]}"
	expIntegAtt47.EndTime = &flexNow

	jobSums47 := i.genTestJobcanSummary(2)
	expIntegdAtts47 := i.genIntegratedAttendance(compID, systemUserID, integAttID, jobSums47, "jobcan")

	ts47 := i.genTestTimesheet(compID, 2, true)
	tsRest47 := i.genTestTimesheetRest(compID, 2, true)

	jobcanEmps47 := i.genTestJobcanEmployee(2)
	mockEmpBody47 := infra.Employees{Employees: jobcanEmps47}
	mockhttpJobEmps47 := mockHttpRes{
		status:   http.StatusOK,
		body:     mockEmpBody47,
		multiRes: true,
		multiResBody: []any{
			mockEmpBody47,
		},
	}

	mockSumBody47First := infra.Summaries{Summaries: []infra.Summary{jobSums47[0]}}
	mockSumBody47Second := infra.Summaries{Summaries: []infra.Summary{jobSums47[1]}}
	mockhttpJobSumms47 := mockHttpRes{
		status:   http.StatusOK,
		body:     mockSumBody47First,
		multiRes: true,
		multiResBody: []any{
			infra.Summaries{},
			mockSumBody47Second,
			infra.Summaries{},
		},
	}

	case47 := integExecTcase{
		desc:         "OK:support_over_31_days_integration",
		preComp:      company,
		preEmps:      emps,
		preIntegJob:  jobcanSetting,
		preIntegAtt:  []tbl.IntegrationAttendance{preIntegAtt47},
		expIntegAtt:  []tbl.IntegrationAttendance{expIntegAtt47},
		preTsheet:    []tbl.Timesheet{},
		expTsheet:    ts47,
		preTsRest:    []tbl.TimesheetRest{},
		expTsRest:    tsRest47,
		preIntegdAtt: []tbl.IntegratedAttendance{},
		expIntegdAtt: expIntegdAtts47,
		input:        autoIn,
		mockhttpJTkn: &mockhttpJobTkn,
		mockhttpJEmp: &mockhttpJobEmps47,
		mockhttpJSum: &mockhttpJobSumms47,
		cleanDB:      true,
		flexNow:      &flexNow,
	}

	// CASE48 ====================
	jobSums48 := i.genTestJobcanSummary(1)
	// Pre timesheet
	preTs48 := i.genTestTimesheet(compID, 1, false)
	// Pre timesheet_rest
	preTsRest48 := i.genTestTimesheetRest(compID, 1, false)
	// Pre integrated_attendance
	preIntegdAtts48 := i.genIntegratedAttendance(compID, userID, integAttID, jobSums48, "jobcan")

	// Exp timesheet
	expTs48 := i.genTestTimesheet(compID, 1, false)
	// Exp integrated_attendance
	jobSums48[0].Rest = 0
	expIntegdAtts48 := i.genIntegratedAttendance(compID, userID, integAttID, jobSums48, "jobcan")

	mockhttpJobSumms48 := mockHttpRes{
		status:   http.StatusOK,
		body:     infra.Summaries{Summaries: jobSums48},
		multiRes: true,
	}

	case48 := integExecTcase{
		desc:         "OK:update_rest_of_integrated_attendance_to_zero",
		preComp:      company,
		preEmps:      emps,
		preIntegJob:  jobcanSetting,
		preIntegAtt:  []tbl.IntegrationAttendance{integAtt},
		preTsheet:    preTs48,
		preTsRest:    preTsRest48,
		preIntegdAtt: preIntegdAtts48,
		expIntegAtt:  []tbl.IntegrationAttendance{expIntegAttNoError},
		expTsheet:    expTs48,
		expIntegdAtt: expIntegdAtts48,
		input:        in,
		mockhttpJTkn: &mockhttpJobTkn,
		mockhttpJEmp: &mockhttpJobEmps,
		mockhttpJSum: &mockhttpJobSumms48,
		cleanDB:      true,
		flexNow:      &flexNow,
	}

	// CASE49 ====================
	jobSums49 := i.genTestJobcanSummary(1)
	// Exp timesheet
	expTs49 := i.genTestTimesheet(compID, 1, false)
	// Exp timesheet_rest
	expTsRest49 := i.genTestTimesheetRest(compID, 1, false)
	// Exp integrated_attendance
	expIntegdAtts49 := i.genIntegratedAttendance(compID, userID, integAttID, jobSums49, "jobcan")

	// Pre timesheet
	preTs49 := i.genTestTimesheet(compID, 1, false)
	preTs49[0].StartTime = test.NewPtr("09:00:00").(*string)
	preTs49[0].EndTime = test.NewPtr("18:00:00").(*string)
	preTs49[0].HolidayType = intutil.ToPtr(0)
	// Pre integrated_attendance
	jobSums49[0].Work = uint(0)
	jobSums49[0].Rest = uint(0)
	jobSums49[0].WorkStart = "00:00"
	jobSums49[0].WorkEnd = "00:00"
	jobSums49[0].VacationType = "paid"
	preIntegdAtts49 := i.genIntegratedAttendance(compID, userID, integAttID, jobSums49, "jobcan")

	case49 := integExecTcase{
		desc:         "OK:update_holiday_type_of_integrated_attendance_to_zero",
		preComp:      company,
		preEmps:      emps,
		preIntegJob:  jobcanSetting,
		preIntegAtt:  []tbl.IntegrationAttendance{integAtt},
		preTsheet:    preTs49,
		preIntegdAtt: preIntegdAtts49,
		expIntegAtt:  []tbl.IntegrationAttendance{expIntegAttNoError},
		expTsheet:    expTs49,
		expTsRest:    expTsRest49,
		expIntegdAtt: expIntegdAtts49,
		input:        in,
		mockhttpJTkn: &mockhttpJobTkn,
		mockhttpJEmp: &mockhttpJobEmps,
		mockhttpJSum: &mockhttpJobSumms,
		cleanDB:      true,
		flexNow:      &flexNow,
	}

	// CASE50 ====================
	mockhttpJobSumms50 := mockHttpRes{
		status:   http.StatusOK,
		body:     infra.Summaries{Summaries: jobSums},
		multiRes: true,
	}
	integAttID50 := 2
	JobcanSettingID50 := 2
	input50 := in
	input50.sqsMsgParams.ID = JobcanSettingID50
	preIntegdAtts50 := i.genIntegratedAttendance(compID, userID, integAttID, jobSums, "jobcan")
	expIntegdAtts50 := i.genIntegratedAttendance(compID, userID, integAttID50, jobSums, "jobcan")
	expJobcan1IntegAtt50 := i.genTestIntegAttendance(integAttID, compID, userID, settingID, 30, "success", "jobcan")
	expJobcan1IntegAtt50.ErrSummaries = "{\"errors\":[{\"count\":1,\"errType\":\"attendance_synchronized_by_another_condition_existed\"}]}"
	expJobcan1IntegAtt50.EndTime = &flexNow
	expJobcan2IntegAtt50 := expJobcan1IntegAtt50
	expJobcan2IntegAtt50.ID = integAttID50
	expJobcan2IntegAtt50.IntegrationSettingID = JobcanSettingID50
	ts50 := i.genTestTimesheet(compID, 1, true)
	tsRest50 := i.genTestTimesheetRest(compID, 1, true)
	case50 := integExecTcase{
		desc:    "OK:Jobcan_timesheet_and_rest_created, update jobcan account(ID:1) with another jobcan account(ID:2)",
		preComp: company,
		preEmps: emps,
		preIntegAtt: []tbl.IntegrationAttendance{expJobcan1IntegAtt50,
			i.genTestIntegAttendance(integAttID50, compID, userID, JobcanSettingID50, 30, "waiting", "jobcan")},
		expIntegAtt:  []tbl.IntegrationAttendance{expJobcan1IntegAtt50, expJobcan2IntegAtt50},
		preTsheet:    []tbl.Timesheet{},
		preTsRest:    []tbl.TimesheetRest{},
		expTsheet:    ts50,
		expTsRest:    tsRest50,
		preIntegJob:  i.genTestIntegrationJobcan(JobcanSettingID50, compID, userID),
		preIntegdAtt: preIntegdAtts50,
		expIntegdAtt: expIntegdAtts50,
		input:        input50,
		mockhttpJTkn: &mockhttpJobTkn,
		mockhttpJEmp: &mockhttpJobEmps,
		mockhttpJSum: &mockhttpJobSumms50,
		cleanDB:      true,
		flexNow:      &flexNow,
	}

	/*******************************************************
	*	KOT test cases
	********************************************************/
	integKOT := i.genTestIntegAttendance(integAttID, compID, userID, settingID, 30, "waiting", "kot")
	kotSetting := i.genTestIntegrationKOT(settingID, compID, userID)
	jobSumsKOT := i.genTestJobcanSummary(1)

	// CASE51 ====================
	expIntegdAtts51 := i.genIntegratedAttendance(compID, userID, integAttID, jobSumsKOT, "kot")
	expIntegAtt51 := i.genTestIntegAttendance(integAttID, compID, userID, settingID, 30, "success", "kot")
	expIntegAtt51.ErrSummaries = "{\"errors\":[{\"count\":1,\"errType\":\"cannot_get_employee\"}]}"
	expIntegAtt51.EndTime = &flexNow
	preEmps51 := i.genTestEmployee(compID, 2)
	expTs51 := i.genTestTimesheet(compID, 1, false)
	expTsRest51 := i.genTestTimesheetRest(compID, 1, false)
	case51 := integExecTcase{
		desc:         "Case51 - OK:KOT_timesheet_and_rest_created",
		preComp:      company,
		preEmps:      preEmps51,
		preIntegAtt:  []tbl.IntegrationAttendance{integKOT},
		expIntegAtt:  []tbl.IntegrationAttendance{expIntegAtt51},
		preTsheet:    []tbl.Timesheet{},
		expTsheet:    expTs51,
		preTsRest:    []tbl.TimesheetRest{},
		expTsRest:    expTsRest51,
		preIntegKOT:  kotSetting,
		expIntegdAtt: expIntegdAtts51,
		input:        in,
		setupKOTHTTPMocks: func() func() {
			kotReuesterTester := kotRequesterTester{}
			cleanup := kotReuesterTester.setupHTTPMock()
			kotReuesterTester.setupKOTEmployeeRequesterMock(t, infra.KOTEmployeesResponse{
				{Code: "EmployeeCd1", Key: "1"},
			}, nil)
			kotReuesterTester.setupKOTDailyWorkingsRequesterMock(t, []infra.KOTDailyWorkings{
				{Date: "2022-12-01", DailyWorkings: []infra.KOTDailyWorking{
					kotReuesterTester.genKOTDailyWorkingTest(testKOTDailyWorkingProps{
						Date:        "2022-12-01",
						EmployeeKey: "1",
						BreakTime:   60,
						TotalWork:   480,
					}),
				}},
			}, nil)
			kotReuesterTester.setupKOTTimerecordsRequesterMock(t, []infra.KOTTimerecord{
				{Date: "2022-12-01", DailyWorkings: []infra.KOTTimerecordDailyWorking{
					kotReuesterTester.genTimerecordDailyWorking("2022-12-01", "1",
						kotReuesterTester.withAttendanceStartRecordTest(kotReuesterTester.genRecordTime("2022-12-01", "09:00:00")),
						kotReuesterTester.withAttendanceEndRecordTest(kotReuesterTester.genRecordTime("2022-12-01", "18:00:00")),
						kotReuesterTester.withRestStartRecordTest(kotReuesterTester.genRecordTime("2022-12-01", "12:00:00")),
						kotReuesterTester.withRestEndRecordTest(kotReuesterTester.genRecordTime("2022-12-01", "13:00:00")),
					),
				}},
			}, nil)
			return cleanup
		},
		cleanDB: true,
		flexNow: &flexNow,
	}

	// CASE52 ====================
	expIntegdAtt52 := i.genIntegratedAttendance(compID, userID, integAttID, []infra.Summary{
		{
			EmployeeId:   uint(1),
			Date:         util.DateToTime("2022-12-01").Format("2006-01-02"),
			Work:         uint(480),
			Rest:         uint(60),
			WorkStart:    "09:00",
			WorkEnd:      "18:00",
			VacationType: "none",
		},
		{
			EmployeeId:   uint(2),
			Date:         util.DateToTime("2022-12-01").Format("2006-01-02"),
			Work:         uint(480),
			Rest:         uint(60),
			WorkStart:    "09:00",
			WorkEnd:      "18:00",
			VacationType: "none",
		},
	}, "kot")
	expIntegAtt52 := i.genTestIntegAttendance(integAttID, compID, userID, settingID, 30, "success", "kot")
	expIntegAtt52.ErrSummaries = "{\"errors\":[]}"
	expIntegAtt52.EndTime = &flexNow
	preEmps52 := i.genTestEmployee(compID, 2)
	expTs52 := i.genTestTimesheet(compID, 2, false)
	expTsRest52 := i.genTestTimesheetRest(compID, 2, false)
	case52 := integExecTcase{
		desc:         "OK:KOT_timesheet_and_rest_created, 2 emps",
		preComp:      company,
		preEmps:      preEmps52,
		preIntegAtt:  []tbl.IntegrationAttendance{integKOT},
		expIntegAtt:  []tbl.IntegrationAttendance{expIntegAtt52},
		preTsheet:    []tbl.Timesheet{},
		expTsheet:    expTs52,
		preTsRest:    []tbl.TimesheetRest{},
		expTsRest:    expTsRest52,
		preIntegKOT:  kotSetting,
		expIntegdAtt: expIntegdAtt52,
		input:        in,
		setupKOTHTTPMocks: func() func() {
			kotReuesterTester := kotRequesterTester{}
			cleanup := kotReuesterTester.setupHTTPMock()
			kotReuesterTester.setupKOTEmployeeRequesterMock(t, infra.KOTEmployeesResponse{
				{Code: "EmployeeCd1", Key: "1"},
				{Code: "EmployeeCd2", Key: "2"},
			}, nil)
			kotReuesterTester.setupKOTDailyWorkingsRequesterMock(t, []infra.KOTDailyWorkings{
				{Date: "2022-12-01", DailyWorkings: []infra.KOTDailyWorking{
					kotReuesterTester.genKOTDailyWorkingTest(testKOTDailyWorkingProps{
						Date:        "2022-12-01",
						EmployeeKey: "1",
						BreakTime:   60,
						TotalWork:   480,
					}),
				}},
				{Date: "2022-12-01", DailyWorkings: []infra.KOTDailyWorking{
					kotReuesterTester.genKOTDailyWorkingTest(testKOTDailyWorkingProps{
						Date:        "2022-12-01",
						EmployeeKey: "2",
						BreakTime:   60,
						TotalWork:   480,
					}),
				}},
			}, nil)
			kotReuesterTester.setupKOTTimerecordsRequesterMock(t, []infra.KOTTimerecord{
				{Date: "2022-12-01", DailyWorkings: []infra.KOTTimerecordDailyWorking{
					kotReuesterTester.genTimerecordDailyWorking("2022-12-01", "1",
						kotReuesterTester.withAttendanceStartRecordTest(kotReuesterTester.genRecordTime("2022-12-01", "09:00:00")),
						kotReuesterTester.withAttendanceEndRecordTest(kotReuesterTester.genRecordTime("2022-12-01", "18:00:00")),
						kotReuesterTester.withRestStartRecordTest(kotReuesterTester.genRecordTime("2022-12-01", "12:00:00")),
						kotReuesterTester.withRestEndRecordTest(kotReuesterTester.genRecordTime("2022-12-01", "13:00:00")),
					),
				}},
				{Date: "2022-12-01", DailyWorkings: []infra.KOTTimerecordDailyWorking{
					kotReuesterTester.genTimerecordDailyWorking("2022-12-01", "2",
						kotReuesterTester.withAttendanceStartRecordTest(kotReuesterTester.genRecordTime("2022-12-01", "09:00:00")),
						kotReuesterTester.withAttendanceEndRecordTest(kotReuesterTester.genRecordTime("2022-12-01", "18:00:00")),
						kotReuesterTester.withRestStartRecordTest(kotReuesterTester.genRecordTime("2022-12-01", "12:00:00")),
						kotReuesterTester.withRestEndRecordTest(kotReuesterTester.genRecordTime("2022-12-01", "13:00:00")),
					),
				}},
			}, nil)
			return cleanup
		},
		cleanDB: true,
		flexNow: &flexNow,
	}

	// CASE53 ====================
	expIntegdAtt53 := i.genIntegratedAttendance(compID, userID, integAttID, []infra.Summary{
		{
			EmployeeId:   uint(1),
			Date:         util.DateToTime("2022-12-01").Format("2006-01-02"),
			Work:         uint(480),
			Rest:         uint(60),
			WorkStart:    "09:00",
			WorkEnd:      "18:00",
			VacationType: "morning_off",
			HolidayType:  2,
		},
		{
			EmployeeId:   uint(2),
			Date:         util.DateToTime("2022-12-01").Format("2006-01-02"),
			Work:         uint(480),
			Rest:         uint(60),
			WorkStart:    "09:00",
			WorkEnd:      "18:00",
			VacationType: "afternoon_off",
			HolidayType:  3,
		},
	}, "kot")
	expIntegAtt53 := i.genTestIntegAttendance(integAttID, compID, userID, settingID, 30, "success", "kot")
	expIntegAtt53.ErrSummaries = "{\"errors\":[]}"
	expIntegAtt53.EndTime = &flexNow
	preEmps53 := i.genTestEmployee(compID, 2)
	expTs53 := i.genTestTimesheet(compID, 2, false)
	expTs53[0].HolidayType = test.GenPtr(2)
	expTs53[1].HolidayType = test.GenPtr(3)
	expTsRest53 := i.genTestTimesheetRest(compID, 2, false)
	case53 := integExecTcase{
		desc:         "OK:KOT_timesheet_and_rest_created, 2 emps, with half holiyday",
		preComp:      company,
		preEmps:      preEmps53,
		preIntegAtt:  []tbl.IntegrationAttendance{integKOT},
		expIntegAtt:  []tbl.IntegrationAttendance{expIntegAtt53},
		preTsheet:    []tbl.Timesheet{},
		expTsheet:    expTs53,
		preTsRest:    []tbl.TimesheetRest{},
		expTsRest:    expTsRest53,
		preIntegKOT:  kotSetting,
		expIntegdAtt: expIntegdAtt53,
		input:        in,
		setupKOTHTTPMocks: func() func() {
			kotReuesterTester := kotRequesterTester{}
			cleanup := kotReuesterTester.setupHTTPMock()
			kotReuesterTester.setupKOTEmployeeRequesterMock(t, infra.KOTEmployeesResponse{
				{Code: "EmployeeCd1", Key: "1"},
				{Code: "EmployeeCd2", Key: "2"},
			}, nil)
			kotReuesterTester.setupKOTDailyWorkingsRequesterMock(t, []infra.KOTDailyWorkings{
				{Date: "2022-12-01", DailyWorkings: []infra.KOTDailyWorking{
					kotReuesterTester.genKOTDailyWorkingTest(testKOTDailyWorkingProps{
						Date:        "2022-12-01",
						EmployeeKey: "1",
						BreakTime:   60,
						TotalWork:   480,
					}, kotReuesterTester.withMorningHalfHolidayTest),
				}},
				{Date: "2022-12-01", DailyWorkings: []infra.KOTDailyWorking{
					kotReuesterTester.genKOTDailyWorkingTest(testKOTDailyWorkingProps{
						Date:        "2022-12-01",
						EmployeeKey: "2",
						BreakTime:   60,
						TotalWork:   480,
					}, kotReuesterTester.withAfternoonHalfHolidayTest),
				}},
			}, nil)
			kotReuesterTester.setupKOTTimerecordsRequesterMock(t, []infra.KOTTimerecord{
				{Date: "2022-12-01", DailyWorkings: []infra.KOTTimerecordDailyWorking{
					kotReuesterTester.genTimerecordDailyWorking("2022-12-01", "1",
						kotReuesterTester.withAttendanceStartRecordTest(kotReuesterTester.genRecordTime("2022-12-01", "09:00:00")),
						kotReuesterTester.withAttendanceEndRecordTest(kotReuesterTester.genRecordTime("2022-12-01", "18:00:00")),
						kotReuesterTester.withRestStartRecordTest(kotReuesterTester.genRecordTime("2022-12-01", "12:00:00")),
						kotReuesterTester.withRestEndRecordTest(kotReuesterTester.genRecordTime("2022-12-01", "13:00:00")),
					),
				}},
				{Date: "2022-12-01", DailyWorkings: []infra.KOTTimerecordDailyWorking{
					kotReuesterTester.genTimerecordDailyWorking("2022-12-01", "2",
						kotReuesterTester.withAttendanceStartRecordTest(kotReuesterTester.genRecordTime("2022-12-01", "09:00:00")),
						kotReuesterTester.withAttendanceEndRecordTest(kotReuesterTester.genRecordTime("2022-12-01", "18:00:00")),
						kotReuesterTester.withRestStartRecordTest(kotReuesterTester.genRecordTime("2022-12-01", "12:00:00")),
						kotReuesterTester.withRestEndRecordTest(kotReuesterTester.genRecordTime("2022-12-01", "13:00:00")),
					),
				}},
			}, nil)
			return cleanup
		},
		cleanDB: true,
		flexNow: &flexNow,
	}

	// CASE54 ====================
	expIntegdAtt54 := i.genIntegratedAttendance(compID, userID, integAttID, []infra.Summary{
		{
			EmployeeId:   uint(1),
			Date:         util.DateToTime("2022-12-01").Format("2006-01-02"),
			Work:         uint(0),
			Rest:         uint(0),
			WorkStart:    "00:00",
			WorkEnd:      "00:00",
			VacationType: "wholeday_off",
			HolidayType:  1,
		},
		{
			EmployeeId:   uint(2),
			Date:         util.DateToTime("2022-12-01").Format("2006-01-02"),
			Work:         uint(480),
			Rest:         uint(120),
			WorkStart:    "09:00",
			WorkEnd:      "18:00",
			VacationType: "none",
			HolidayType:  0,
		},
	}, "kot")
	expIntegAtt54 := i.genTestIntegAttendance(integAttID, compID, userID, settingID, 30, "success", "kot")
	expIntegAtt54.ErrSummaries = "{\"errors\":[]}"
	expIntegAtt54.EndTime = &flexNow
	preEmps54 := i.genTestEmployee(compID, 2)
	expTs54 := i.genTestTimesheet(compID, 2, false)
	expTs54[0].HolidayType = test.GenPtr(1)
	expTsRest54 := i.genTestTimesheetRest(compID, 1, false)
	expTsRest54[0].EndTime = test.NewPtr("14:00:00").(*string)
	expTsRest54[0].TimesheetNo = 2
	case54 := integExecTcase{
		desc:         "OK:KOT_timesheet_and_rest_created, 2 emps, full holiyday, hour holiday",
		preComp:      company,
		preEmps:      preEmps54,
		preIntegAtt:  []tbl.IntegrationAttendance{integKOT},
		expIntegAtt:  []tbl.IntegrationAttendance{expIntegAtt54},
		preTsheet:    []tbl.Timesheet{},
		expTsheet:    expTs54,
		preTsRest:    []tbl.TimesheetRest{},
		expTsRest:    expTsRest54,
		preIntegKOT:  kotSetting,
		expIntegdAtt: expIntegdAtt54,
		input:        in,
		setupKOTHTTPMocks: func() func() {
			kotReuesterTester := kotRequesterTester{}
			cleanup := kotReuesterTester.setupHTTPMock()
			kotReuesterTester.setupKOTEmployeeRequesterMock(t, infra.KOTEmployeesResponse{
				{Code: "EmployeeCd1", Key: "1"},
				{Code: "EmployeeCd2", Key: "2"},
			}, nil)
			kotReuesterTester.setupKOTDailyWorkingsRequesterMock(t, []infra.KOTDailyWorkings{
				{Date: "2022-12-01", DailyWorkings: []infra.KOTDailyWorking{
					kotReuesterTester.genKOTDailyWorkingTest(testKOTDailyWorkingProps{
						Date:        "2022-12-01",
						EmployeeKey: "1",
						BreakTime:   0,
						TotalWork:   0,
					}, kotReuesterTester.withFulltimeHolidayTest),
				}},
				{Date: "2022-12-01", DailyWorkings: []infra.KOTDailyWorking{
					kotReuesterTester.genKOTDailyWorkingTest(testKOTDailyWorkingProps{
						Date:        "2022-12-01",
						EmployeeKey: "2",
						BreakTime:   60,
						TotalWork:   480,
					}, kotReuesterTester.withHourHolidayTest(60)),
				}},
			}, nil)
			kotReuesterTester.setupKOTTimerecordsRequesterMock(t, []infra.KOTTimerecord{
				{Date: "2022-12-01", DailyWorkings: []infra.KOTTimerecordDailyWorking{
					kotReuesterTester.genTimerecordDailyWorking("2022-12-01", "2",
						kotReuesterTester.withAttendanceStartRecordTest(kotReuesterTester.genRecordTime("2022-12-01", "09:00:00")),
						kotReuesterTester.withAttendanceEndRecordTest(kotReuesterTester.genRecordTime("2022-12-01", "18:00:00")),
						kotReuesterTester.withRestStartRecordTest(kotReuesterTester.genRecordTime("2022-12-01", "12:00:00")),
						kotReuesterTester.withRestEndRecordTest(kotReuesterTester.genRecordTime("2022-12-01", "13:00:00")),
					),
				}},
			}, nil)
			return cleanup
		},
		cleanDB: true,
		flexNow: &flexNow,
	}

	// CASE55 ====================
	expIntegdAtt55 := i.genIntegratedAttendance(compID, userID, integAttID, []infra.Summary{
		{
			EmployeeId:   uint(1),
			Date:         util.DateToTime("2022-12-01").Format("2006-01-02"),
			Work:         uint(480),
			Rest:         uint(60),
			WorkStart:    "09:00",
			WorkEnd:      "18:00",
			VacationType: "none",
			HolidayType:  0,
		},
		{
			EmployeeId:   uint(1),
			Date:         util.DateToTime("2022-12-02").Format("2006-01-02"),
			Work:         uint(480),
			Rest:         uint(60),
			WorkStart:    "09:00",
			WorkEnd:      "18:00",
			VacationType: "morning_off",
			HolidayType:  2,
		},
		{
			EmployeeId:   uint(1),
			Date:         util.DateToTime("2022-12-03").Format("2006-01-02"),
			Work:         uint(480),
			Rest:         uint(60),
			WorkStart:    "09:00",
			WorkEnd:      "18:00",
			VacationType: "afternoon_off",
			HolidayType:  3,
		},
		{
			EmployeeId:   uint(1),
			Date:         util.DateToTime("2022-12-04").Format("2006-01-02"),
			Work:         uint(0),
			Rest:         uint(0),
			WorkStart:    "00:00",
			WorkEnd:      "00:00",
			VacationType: "wholeday_off",
			HolidayType:  1,
		},
	}, "kot")
	expIntegAtt55 := i.genTestIntegAttendance(integAttID, compID, userID, settingID, 30, "success", "kot")
	expIntegAtt55.ErrSummaries = "{\"errors\":[]}"
	expIntegAtt55.EndTime = &flexNow
	preEmps55 := i.genTestEmployee(compID, 1)
	preTs55 := func() []tbl.Timesheet {
		ts := i.genTestTimesheet(compID, 4, true)
		for i := range ts {
			ts[i].EmployeeNo = 1
		}
		return ts
	}()
	expTs55 := func() []tbl.Timesheet {
		ts := i.genTestTimesheet(compID, 4, true)
		for i := range ts {
			ts[i].EmployeeNo = 1
		}
		return ts
	}()
	expTs55[1].HolidayType = test.GenPtr(2)
	expTs55[2].HolidayType = test.GenPtr(3)
	expTs55[3].HolidayType = test.GenPtr(1)
	preTsRest55 := i.genTestTimesheetRest(compID, 3, true)
	expTsRest55 := func() []tbl.TimesheetRest {
		tsRests := i.genTestTimesheetRest(compID, 3, true)
		lastId := preTsRest55[len(preTsRest55)-1].RestNo
		for i := range tsRests {
			lastId++
			tsRests[i].RestNo = lastId
		}
		return tsRests
	}()
	case55 := integExecTcase{
		desc:         "OK:KOT_timesheet_and_rest_created, 1 emps, update timesheet with KOT attendance.",
		preComp:      company,
		preEmps:      preEmps55,
		preIntegAtt:  []tbl.IntegrationAttendance{integKOT},
		expIntegAtt:  []tbl.IntegrationAttendance{expIntegAtt55},
		preTsheet:    preTs55,
		expTsheet:    expTs55,
		preTsRest:    preTsRest55,
		expTsRest:    expTsRest55,
		preIntegKOT:  kotSetting,
		expIntegdAtt: expIntegdAtt55,
		input:        in,
		setupKOTHTTPMocks: func() func() {
			kotReuesterTester := kotRequesterTester{}
			cleanup := kotReuesterTester.setupHTTPMock()
			kotReuesterTester.setupKOTEmployeeRequesterMock(t, infra.KOTEmployeesResponse{
				{Code: "EmployeeCd1", Key: "1"},
			}, nil)
			kotReuesterTester.setupKOTDailyWorkingsRequesterMock(t, []infra.KOTDailyWorkings{
				{Date: "2022-12-01", DailyWorkings: []infra.KOTDailyWorking{
					kotReuesterTester.genKOTDailyWorkingTest(testKOTDailyWorkingProps{
						Date:        "2022-12-01",
						EmployeeKey: "1",
						BreakTime:   60,
						TotalWork:   480,
					}),
				}},
				{Date: "2022-12-02", DailyWorkings: []infra.KOTDailyWorking{
					kotReuesterTester.genKOTDailyWorkingTest(testKOTDailyWorkingProps{
						Date:        "2022-12-02",
						EmployeeKey: "1",
						BreakTime:   60,
						TotalWork:   480,
					}, kotReuesterTester.withMorningHalfHolidayTest),
				}},
				{Date: "2022-12-03", DailyWorkings: []infra.KOTDailyWorking{
					kotReuesterTester.genKOTDailyWorkingTest(testKOTDailyWorkingProps{
						Date:        "2022-12-03",
						EmployeeKey: "1",
						BreakTime:   60,
						TotalWork:   480,
					}, kotReuesterTester.withAfternoonHalfHolidayTest),
				}},
				{Date: "2022-12-04", DailyWorkings: []infra.KOTDailyWorking{
					kotReuesterTester.genKOTDailyWorkingTest(testKOTDailyWorkingProps{
						Date:        "2022-12-04",
						EmployeeKey: "1",
						BreakTime:   0,
						TotalWork:   0,
					}, kotReuesterTester.withFulltimeHolidayTest),
				}},
			}, nil)
			kotReuesterTester.setupKOTTimerecordsRequesterMock(t, []infra.KOTTimerecord{
				{Date: "2022-12-01", DailyWorkings: []infra.KOTTimerecordDailyWorking{
					kotReuesterTester.genTimerecordDailyWorking("2022-12-01", "1",
						kotReuesterTester.withAttendanceStartRecordTest(kotReuesterTester.genRecordTime("2022-12-01", "09:00:00")),
						kotReuesterTester.withAttendanceEndRecordTest(kotReuesterTester.genRecordTime("2022-12-01", "18:00:00")),
						kotReuesterTester.withRestStartRecordTest(kotReuesterTester.genRecordTime("2022-12-01", "12:00:00")),
						kotReuesterTester.withRestEndRecordTest(kotReuesterTester.genRecordTime("2022-12-01", "13:00:00")),
					),
				}},
				{Date: "2022-12-02", DailyWorkings: []infra.KOTTimerecordDailyWorking{
					kotReuesterTester.genTimerecordDailyWorking("2022-12-02", "1",
						kotReuesterTester.withAttendanceStartRecordTest(kotReuesterTester.genRecordTime("2022-12-02", "09:00:00")),
						kotReuesterTester.withAttendanceEndRecordTest(kotReuesterTester.genRecordTime("2022-12-02", "18:00:00")),
						kotReuesterTester.withRestStartRecordTest(kotReuesterTester.genRecordTime("2022-12-02", "12:00:00")),
						kotReuesterTester.withRestEndRecordTest(kotReuesterTester.genRecordTime("2022-12-02", "13:00:00")),
					),
				}},
				{Date: "2022-12-03", DailyWorkings: []infra.KOTTimerecordDailyWorking{
					kotReuesterTester.genTimerecordDailyWorking("2022-12-03", "1",
						kotReuesterTester.withAttendanceStartRecordTest(kotReuesterTester.genRecordTime("2022-12-03", "09:00:00")),
						kotReuesterTester.withAttendanceEndRecordTest(kotReuesterTester.genRecordTime("2022-12-03", "18:00:00")),
						kotReuesterTester.withRestStartRecordTest(kotReuesterTester.genRecordTime("2022-12-03", "12:00:00")),
						kotReuesterTester.withRestEndRecordTest(kotReuesterTester.genRecordTime("2022-12-03", "13:00:00")),
					),
				}},
			}, nil)
			return cleanup
		},
		cleanDB: true,
		flexNow: &flexNow,
	}
	// CASE56 ====================
	mockCtrl56 := gomock.NewController(t)
	defer mockCtrl56.Finish()
	mockObjIntegKOT56 := mockrepo.NewMockIntegrationKOT(mockCtrl56)
	mockObjIntegKOT56.EXPECT().Exist(gomock.Any(), gomock.Any()).
		Return(false, errors.New("AAA")).AnyTimes()
	mrr56 := &test.MockRepoRegistry{}
	mrr56.MIntegKOT = mockObjIntegKOT56
	expIntegAtt56 := i.genTestIntegAttendance(integAttID, compID, userID, settingID, 30, "success", "kot")
	expIntegAtt56.ErrSummaries = "{\"errors\":[{\"count\":1,\"errType\":\"server_error\"}]}"
	expIntegAtt56.Status = "failure"
	expIntegAtt56.EndTime = &flexNow
	preEmps56 := i.genTestEmployee(compID, 2)
	case56 := integExecTcase{
		desc:         "NG:unexpected error from repo related to KOT",
		preComp:      company,
		preEmps:      preEmps56,
		preIntegAtt:  []tbl.IntegrationAttendance{integKOT},
		expIntegAtt:  []tbl.IntegrationAttendance{expIntegAtt56},
		preTsheet:    []tbl.Timesheet{},
		expTsheet:    []tbl.Timesheet{},
		preTsRest:    []tbl.TimesheetRest{},
		expTsRest:    []tbl.TimesheetRest{},
		preIntegKOT:  kotSetting,
		expIntegdAtt: []tbl.IntegratedAttendance{},
		input:        in,
		expErr: twisttest.ExpReturns{
			Err:    errors.New("AAA"),
			ErrStr: "*errors.errorString",
		},
		mrr: mrr56,
		setupKOTHTTPMocks: func() func() {
			kotReuesterTester := kotRequesterTester{}
			cleanup := kotReuesterTester.setupHTTPMock()
			kotReuesterTester.setupKOTEmployeeRequesterMock(t, infra.KOTEmployeesResponse{
				{Code: "EmployeeCd1", Key: "1"},
			}, nil)
			kotReuesterTester.setupKOTDailyWorkingsRequesterMock(t, []infra.KOTDailyWorkings{
				{Date: "2022-12-01", DailyWorkings: []infra.KOTDailyWorking{
					kotReuesterTester.genKOTDailyWorkingTest(testKOTDailyWorkingProps{
						Date:        "2022-12-01",
						EmployeeKey: "1",
						BreakTime:   60,
						TotalWork:   480,
					}),
				}},
			}, nil)
			kotReuesterTester.setupKOTTimerecordsRequesterMock(t, []infra.KOTTimerecord{
				{Date: "2022-12-01", DailyWorkings: []infra.KOTTimerecordDailyWorking{
					kotReuesterTester.genTimerecordDailyWorking("2022-12-01", "1",
						kotReuesterTester.withAttendanceStartRecordTest(kotReuesterTester.genRecordTime("2022-12-01", "09:00:00")),
						kotReuesterTester.withAttendanceEndRecordTest(kotReuesterTester.genRecordTime("2022-12-01", "18:00:00")),
						kotReuesterTester.withRestStartRecordTest(kotReuesterTester.genRecordTime("2022-12-01", "12:00:00")),
						kotReuesterTester.withRestEndRecordTest(kotReuesterTester.genRecordTime("2022-12-01", "13:00:00")),
					),
				}},
			}, nil)
			return cleanup
		},
		cleanDB: true,
		flexNow: &flexNow,
	}

	// CASE57 ====================
	expIntegAtt57 := i.genTestIntegAttendance(integAttID, compID, userID, settingID, 30, "success", "kot")
	expIntegAtt57.ErrSummaries = "{\"errors\":[{\"count\":1,\"errType\":\"api_access_error\"}]}"
	expIntegAtt57.EndTime = &flexNow
	expIntegAtt57.Status = "failure"
	preEmps57 := i.genTestEmployee(compID, 2)
	case57 := integExecTcase{
		desc:         "NG:KOT Employee API return error",
		preComp:      company,
		preEmps:      preEmps57,
		preIntegAtt:  []tbl.IntegrationAttendance{integKOT},
		expIntegAtt:  []tbl.IntegrationAttendance{expIntegAtt57},
		preTsheet:    []tbl.Timesheet{},
		expTsheet:    []tbl.Timesheet{},
		preTsRest:    []tbl.TimesheetRest{},
		expTsRest:    []tbl.TimesheetRest{},
		preIntegKOT:  kotSetting,
		expIntegdAtt: []tbl.IntegratedAttendance{},
		input:        in,
		expErr: twisttest.ExpReturns{
			Err:    infra.ErrAPIAccess,
			ErrStr: "*errors.errorString",
		},
		setupKOTHTTPMocks: func() func() {
			kotReuesterTester := kotRequesterTester{}
			cleanup := kotReuesterTester.setupHTTPMock()
			kotReuesterTester.setupKOTEmployeeRequesterMock(t, nil, &[]infra.KOTError{
				{Message: "TEST_MSG", Code: 1},
			})
			kotReuesterTester.setupKOTDailyWorkingsRequesterMock(t, []infra.KOTDailyWorkings{
				{Date: "2022-12-01", DailyWorkings: []infra.KOTDailyWorking{
					kotReuesterTester.genKOTDailyWorkingTest(testKOTDailyWorkingProps{
						Date:        "2022-12-01",
						EmployeeKey: "1",
						BreakTime:   60,
						TotalWork:   480,
					}),
				}},
			}, nil)
			kotReuesterTester.setupKOTTimerecordsRequesterMock(t, []infra.KOTTimerecord{
				{Date: "2022-12-01", DailyWorkings: []infra.KOTTimerecordDailyWorking{
					kotReuesterTester.genTimerecordDailyWorking("2022-12-01", "1",
						kotReuesterTester.withAttendanceStartRecordTest(kotReuesterTester.genRecordTime("2022-12-01", "09:00:00")),
						kotReuesterTester.withAttendanceEndRecordTest(kotReuesterTester.genRecordTime("2022-12-01", "18:00:00")),
						kotReuesterTester.withRestStartRecordTest(kotReuesterTester.genRecordTime("2022-12-01", "12:00:00")),
						kotReuesterTester.withRestEndRecordTest(kotReuesterTester.genRecordTime("2022-12-01", "13:00:00")),
					),
				}},
			}, nil)
			return cleanup
		},
		cleanDB: true,
		flexNow: &flexNow,
	}

	// CASE58 ====================
	expIntegAtt58 := i.genTestIntegAttendance(integAttID, compID, userID, settingID, 30, "success", "kot")
	expIntegAtt58.ErrSummaries = "{\"errors\":[{\"count\":1,\"errType\":\"api_access_error\"},{\"count\":1,\"errType\":\"cannot_get_employee\"}]}"
	expIntegAtt58.EndTime = &flexNow
	expIntegAtt58.Status = "failure"
	preEmps58 := i.genTestEmployee(compID, 2)
	case58 := integExecTcase{
		desc:         "NG:KOT DailyWorkings API return error",
		preComp:      company,
		preEmps:      preEmps58,
		preIntegAtt:  []tbl.IntegrationAttendance{integKOT},
		expIntegAtt:  []tbl.IntegrationAttendance{expIntegAtt58},
		preTsheet:    []tbl.Timesheet{},
		expTsheet:    []tbl.Timesheet{},
		preTsRest:    []tbl.TimesheetRest{},
		expTsRest:    []tbl.TimesheetRest{},
		preIntegKOT:  kotSetting,
		expIntegdAtt: []tbl.IntegratedAttendance{},
		input:        in,
		expErr: twisttest.ExpReturns{
			Err:    infra.ErrAPIAccess,
			ErrStr: "*errors.errorString",
		},
		setupKOTHTTPMocks: func() func() {
			kotReuesterTester := kotRequesterTester{}
			cleanup := kotReuesterTester.setupHTTPMock()
			kotReuesterTester.setupKOTEmployeeRequesterMock(t, infra.KOTEmployeesResponse{
				{Code: "EmployeeCd1", Key: "1"},
			}, nil)
			kotReuesterTester.setupKOTDailyWorkingsRequesterMock(t, nil, &[]infra.KOTError{
				{Message: "TEST_MSG", Code: 1},
			})
			kotReuesterTester.setupKOTTimerecordsRequesterMock(t, []infra.KOTTimerecord{
				{Date: "2022-12-01", DailyWorkings: []infra.KOTTimerecordDailyWorking{
					kotReuesterTester.genTimerecordDailyWorking("2022-12-01", "1",
						kotReuesterTester.withAttendanceStartRecordTest(kotReuesterTester.genRecordTime("2022-12-01", "09:00:00")),
						kotReuesterTester.withAttendanceEndRecordTest(kotReuesterTester.genRecordTime("2022-12-01", "18:00:00")),
						kotReuesterTester.withRestStartRecordTest(kotReuesterTester.genRecordTime("2022-12-01", "12:00:00")),
						kotReuesterTester.withRestEndRecordTest(kotReuesterTester.genRecordTime("2022-12-01", "13:00:00")),
					),
				}},
			}, nil)
			return cleanup
		},
		cleanDB: true,
		flexNow: &flexNow,
	}
	// CASE59 ====================
	expIntegAtt59 := i.genTestIntegAttendance(integAttID, compID, userID, settingID, 30, "success", "kot")
	expIntegAtt59.ErrSummaries = "{\"errors\":[{\"count\":1,\"errType\":\"api_access_error\"},{\"count\":1,\"errType\":\"cannot_get_employee\"}]}"
	expIntegAtt59.EndTime = &flexNow
	expIntegAtt59.Status = "failure"
	preEmps59 := i.genTestEmployee(compID, 2)
	case59 := integExecTcase{
		desc:         "NG:KOT Timerecords API return error",
		preComp:      company,
		preEmps:      preEmps59,
		preIntegAtt:  []tbl.IntegrationAttendance{integKOT},
		expIntegAtt:  []tbl.IntegrationAttendance{expIntegAtt59},
		preTsheet:    []tbl.Timesheet{},
		expTsheet:    []tbl.Timesheet{},
		preTsRest:    []tbl.TimesheetRest{},
		expTsRest:    []tbl.TimesheetRest{},
		preIntegKOT:  kotSetting,
		expIntegdAtt: []tbl.IntegratedAttendance{},
		input:        in,
		expErr: twisttest.ExpReturns{
			Err:    infra.ErrAPIAccess,
			ErrStr: "*errors.errorString",
		},
		setupKOTHTTPMocks: func() func() {
			kotReuesterTester := kotRequesterTester{}
			cleanup := kotReuesterTester.setupHTTPMock()
			kotReuesterTester.setupKOTEmployeeRequesterMock(t, infra.KOTEmployeesResponse{
				{Code: "EmployeeCd1", Key: "1"},
			}, nil)
			kotReuesterTester.setupKOTDailyWorkingsRequesterMock(t, []infra.KOTDailyWorkings{
				{Date: "2022-12-01", DailyWorkings: []infra.KOTDailyWorking{
					kotReuesterTester.genKOTDailyWorkingTest(testKOTDailyWorkingProps{
						Date:        "2022-12-01",
						EmployeeKey: "1",
						BreakTime:   60,
						TotalWork:   480,
					}),
				}},
			}, nil)
			kotReuesterTester.setupKOTTimerecordsRequesterMock(t, nil, &[]infra.KOTError{
				{Message: "TEST_MSG", Code: 1},
			})
			return cleanup
		},
		cleanDB: true,
		flexNow: &flexNow,
	}

	// CASE60 ====================
	expIntegAtt60 := i.genTestIntegAttendance(integAttID, compID, userID, settingID, 30, "success", "kot")
	expIntegAtt60.ErrSummaries = "{\"errors\":[{\"count\":1,\"errType\":\"no_attendance\"}]}"
	expIntegAtt60.EndTime = &flexNow
	preEmps60 := i.genTestEmployee(compID, 1)
	case60 := integExecTcase{
		desc:         "OK:KOT data is ignored, 1 emps attendance is KOT error",
		preComp:      company,
		preEmps:      preEmps60,
		preIntegAtt:  []tbl.IntegrationAttendance{integKOT},
		expIntegAtt:  []tbl.IntegrationAttendance{expIntegAtt60},
		preTsheet:    []tbl.Timesheet{},
		expTsheet:    []tbl.Timesheet{},
		preTsRest:    []tbl.TimesheetRest{},
		expTsRest:    []tbl.TimesheetRest{},
		preIntegKOT:  kotSetting,
		expIntegdAtt: []tbl.IntegratedAttendance{},
		input:        in,
		setupKOTHTTPMocks: func() func() {
			kotReuesterTester := kotRequesterTester{}
			cleanup := kotReuesterTester.setupHTTPMock()
			kotReuesterTester.setupKOTEmployeeRequesterMock(t, infra.KOTEmployeesResponse{
				{Code: "EmployeeCd1", Key: "1"},
				{Code: "EmployeeCd2", Key: "2"},
			}, nil)
			kotReuesterTester.setupKOTDailyWorkingsRequesterMock(t, []infra.KOTDailyWorkings{
				{Date: "2022-12-01", DailyWorkings: []infra.KOTDailyWorking{
					kotReuesterTester.genKOTDailyWorkingTest(testKOTDailyWorkingProps{
						Date:        "2022-12-01",
						EmployeeKey: "1",
						BreakTime:   60,
						TotalWork:   480,
						IsError:     true,
					}),
				}},
			}, nil)
			kotReuesterTester.setupKOTTimerecordsRequesterMock(t, []infra.KOTTimerecord{}, nil)
			return cleanup
		},
		cleanDB: true,
		flexNow: &flexNow,
	}

	// CASE61 ====================
	expIntegdAtt61 := i.genIntegratedAttendance(compID, userID, integAttID, []infra.Summary{
		{
			EmployeeId:   uint(1),
			Date:         util.DateToTime("2022-12-01").Format("2006-01-02"),
			Work:         uint(480),
			Rest:         uint(60),
			WorkStart:    "09:00",
			WorkEnd:      "18:00",
			VacationType: "none",
		},
	}, "kot")
	expIntegAtt61 := i.genTestIntegAttendance(integAttID, compID, userID, settingID, 30, "success", "kot")
	expIntegAtt61.ErrSummaries = "{\"errors\":[]}"
	expIntegAtt61.EndTime = &flexNow
	preEmps61 := i.genTestEmployee(compID, 2)
	expTs61 := i.genTestTimesheet(compID, 1, false)
	expTsRest61 := i.genTestTimesheetRest(compID, 1, false)
	case61 := integExecTcase{
		desc:         "OK:KOT_timesheet_and_rest_created, 2 emps, one is ignored(error attendance)",
		preComp:      company,
		preEmps:      preEmps61,
		preIntegAtt:  []tbl.IntegrationAttendance{integKOT},
		expIntegAtt:  []tbl.IntegrationAttendance{expIntegAtt61},
		preTsheet:    []tbl.Timesheet{},
		expTsheet:    expTs61,
		preTsRest:    []tbl.TimesheetRest{},
		expTsRest:    expTsRest61,
		preIntegKOT:  kotSetting,
		expIntegdAtt: expIntegdAtt61,
		input:        in,
		setupKOTHTTPMocks: func() func() {
			kotReuesterTester := kotRequesterTester{}
			cleanup := kotReuesterTester.setupHTTPMock()
			kotReuesterTester.setupKOTEmployeeRequesterMock(t, infra.KOTEmployeesResponse{
				{Code: "EmployeeCd1", Key: "1"},
				{Code: "EmployeeCd2", Key: "2"},
			}, nil)
			kotReuesterTester.setupKOTDailyWorkingsRequesterMock(t, []infra.KOTDailyWorkings{
				{Date: "2022-12-01", DailyWorkings: []infra.KOTDailyWorking{
					kotReuesterTester.genKOTDailyWorkingTest(testKOTDailyWorkingProps{
						Date:        "2022-12-01",
						EmployeeKey: "1",
						BreakTime:   60,
						TotalWork:   480,
					}),
				}},
				{Date: "2022-12-01", DailyWorkings: []infra.KOTDailyWorking{
					kotReuesterTester.genKOTDailyWorkingTest(testKOTDailyWorkingProps{
						Date:        "2022-12-01",
						EmployeeKey: "2",
						BreakTime:   60,
						TotalWork:   480,
						IsError:     true,
					}),
				}},
			}, nil)
			kotReuesterTester.setupKOTTimerecordsRequesterMock(t, []infra.KOTTimerecord{
				{Date: "2022-12-01", DailyWorkings: []infra.KOTTimerecordDailyWorking{
					kotReuesterTester.genTimerecordDailyWorking("2022-12-01", "1",
						kotReuesterTester.withAttendanceStartRecordTest(kotReuesterTester.genRecordTime("2022-12-01", "09:00:00")),
						kotReuesterTester.withAttendanceEndRecordTest(kotReuesterTester.genRecordTime("2022-12-01", "18:00:00")),
						kotReuesterTester.withRestStartRecordTest(kotReuesterTester.genRecordTime("2022-12-01", "12:00:00")),
						kotReuesterTester.withRestEndRecordTest(kotReuesterTester.genRecordTime("2022-12-01", "13:00:00")),
					),
				}},
			}, nil)
			return cleanup
		},
		cleanDB: true,
		flexNow: &flexNow,
	}

	// CASE62 ====================
	/*
		the test to check if it's possible to register with crowdlog exists separately.
		if you want to see it, please try to grep "TestStoreReq_validateAttendanceInfo"
	*/
	expIntegdAtt62 := i.genIntegratedAttendance(compID, userID, integAttID, []infra.Summary{
		{
			EmployeeId:   uint(2),
			Date:         util.DateToTime("2022-12-01").Format("2006-01-02"),
			Work:         uint(480),
			Rest:         uint(120),
			WorkStart:    "09:00",
			WorkEnd:      "18:00",
			VacationType: "none",
			HolidayType:  0,
		},
	}, "kot")
	expIntegAtt62 := i.genTestIntegAttendance(integAttID, compID, userID, settingID, 30, "success", "kot")
	expIntegAtt62.ErrSummaries = "{\"errors\":[{\"count\":1,\"errType\":\"cannot_save_attendance\"}]}"
	expIntegAtt62.EndTime = &flexNow
	preEmps62 := i.genTestEmployee(compID, 2)
	expTs62 := i.genTestTimesheet(compID, 2, false)
	expTs62[1].TimesheetNo = 1
	expTsRest62 := i.genTestTimesheetRest(compID, 1, false)
	expTsRest62[0].EndTime = test.NewPtr("14:00:00").(*string)
	expTsRest62[0].TimesheetNo = 1
	case62 := integExecTcase{
		desc:         "OK:KOT_timesheet_and_rest_created, 2 emps, invalid attendance that has full holiday, hour holiday",
		preComp:      company,
		preEmps:      preEmps62,
		preIntegAtt:  []tbl.IntegrationAttendance{integKOT},
		expIntegAtt:  []tbl.IntegrationAttendance{expIntegAtt62},
		preTsheet:    []tbl.Timesheet{},
		expTsheet:    expTs62[1:2],
		preTsRest:    []tbl.TimesheetRest{},
		expTsRest:    expTsRest62,
		preIntegKOT:  kotSetting,
		expIntegdAtt: expIntegdAtt62,
		input:        in,
		setupKOTHTTPMocks: func() func() {
			kotReuesterTester := kotRequesterTester{}
			cleanup := kotReuesterTester.setupHTTPMock()
			kotReuesterTester.setupKOTEmployeeRequesterMock(t, infra.KOTEmployeesResponse{
				{Code: "EmployeeCd1", Key: "1"},
				{Code: "EmployeeCd2", Key: "2"},
			}, nil)
			kotReuesterTester.setupKOTDailyWorkingsRequesterMock(t, []infra.KOTDailyWorkings{
				{Date: "2022-12-01", DailyWorkings: []infra.KOTDailyWorking{
					kotReuesterTester.genKOTDailyWorkingTest(testKOTDailyWorkingProps{
						Date:        "2022-12-01",
						EmployeeKey: "1",
						BreakTime:   0,
						TotalWork:   1,
					}, kotReuesterTester.withFulltimeHolidayTest),
				}},
				{Date: "2022-12-01", DailyWorkings: []infra.KOTDailyWorking{
					kotReuesterTester.genKOTDailyWorkingTest(testKOTDailyWorkingProps{
						Date:        "2022-12-01",
						EmployeeKey: "2",
						BreakTime:   60,
						TotalWork:   480,
					}, kotReuesterTester.withHourHolidayTest(60)),
				}},
			}, nil)
			kotReuesterTester.setupKOTTimerecordsRequesterMock(t, []infra.KOTTimerecord{
				{Date: "2022-12-01", DailyWorkings: []infra.KOTTimerecordDailyWorking{
					kotReuesterTester.genTimerecordDailyWorking("2022-12-01", "2",
						kotReuesterTester.withAttendanceStartRecordTest(kotReuesterTester.genRecordTime("2022-12-01", "09:00:00")),
						kotReuesterTester.withAttendanceEndRecordTest(kotReuesterTester.genRecordTime("2022-12-01", "18:00:00")),
						kotReuesterTester.withRestStartRecordTest(kotReuesterTester.genRecordTime("2022-12-01", "12:00:00")),
						kotReuesterTester.withRestEndRecordTest(kotReuesterTester.genRecordTime("2022-12-01", "13:00:00")),
					),
				}},
			}, nil)
			return cleanup
		},
		cleanDB: true,
		flexNow: &flexNow,
	}

	// CASE63 ====================
	preIntegdAtts63 := i.genIntegratedAttendance(compID, userID, integAttID, jobSumsKOT, "kot")
	expIntegdAtts63 := i.genIntegratedAttendance(compID, userID, integAttID, jobSumsKOT, "kot")
	expIntegAtt63 := i.genTestIntegAttendance(integAttID, compID, userID, settingID, 30, "success", "kot")
	expIntegAtt63.ErrSummaries = "{\"errors\":[{\"count\":1,\"errType\":\"cannot_get_employee\"}]}"
	expIntegAtt63.EndTime = &flexNow
	preEmps63 := i.genTestEmployee(compID, 2)
	expTs63 := i.genTestTimesheet(compID, 1, false)
	expTsRest63 := i.genTestTimesheetRest(compID, 1, false)
	case63 := integExecTcase{
		desc:         "OK:KOT_timesheet_and_rest_created, update kot with kot",
		preComp:      company,
		preEmps:      preEmps63,
		preIntegAtt:  []tbl.IntegrationAttendance{integKOT},
		expIntegAtt:  []tbl.IntegrationAttendance{expIntegAtt63},
		preTsheet:    []tbl.Timesheet{},
		expTsheet:    expTs63,
		preTsRest:    []tbl.TimesheetRest{},
		expTsRest:    expTsRest63,
		preIntegKOT:  kotSetting,
		preIntegdAtt: preIntegdAtts63,
		expIntegdAtt: expIntegdAtts63,
		input:        in,
		setupKOTHTTPMocks: func() func() {
			kotReuesterTester := kotRequesterTester{}
			cleanup := kotReuesterTester.setupHTTPMock()
			kotReuesterTester.setupKOTEmployeeRequesterMock(t, infra.KOTEmployeesResponse{
				{Code: "EmployeeCd1", Key: "1"},
			}, nil)
			kotReuesterTester.setupKOTDailyWorkingsRequesterMock(t, []infra.KOTDailyWorkings{
				{Date: "2022-12-01", DailyWorkings: []infra.KOTDailyWorking{
					kotReuesterTester.genKOTDailyWorkingTest(testKOTDailyWorkingProps{
						Date:        "2022-12-01",
						EmployeeKey: "1",
						BreakTime:   60,
						TotalWork:   480,
					}),
				}},
			}, nil)
			kotReuesterTester.setupKOTTimerecordsRequesterMock(t, []infra.KOTTimerecord{
				{Date: "2022-12-01", DailyWorkings: []infra.KOTTimerecordDailyWorking{
					kotReuesterTester.genTimerecordDailyWorking("2022-12-01", "1",
						kotReuesterTester.withAttendanceStartRecordTest(kotReuesterTester.genRecordTime("2022-12-01", "09:00:00")),
						kotReuesterTester.withAttendanceEndRecordTest(kotReuesterTester.genRecordTime("2022-12-01", "18:00:00")),
						kotReuesterTester.withRestStartRecordTest(kotReuesterTester.genRecordTime("2022-12-01", "12:00:00")),
						kotReuesterTester.withRestEndRecordTest(kotReuesterTester.genRecordTime("2022-12-01", "13:00:00")),
					),
				}},
			}, nil)
			return cleanup
		},
		cleanDB: true,
		flexNow: &flexNow,
	}

	// CASE64 ====================
	preIntegdAtts64 := i.genIntegratedAttendance(compID, userID, integAttID, jobSumsKOT, "jobcan")
	expIntegdAtts64 := i.genIntegratedAttendance(compID, userID, integAttID, jobSumsKOT, "kot")
	expIntegAtt64 := i.genTestIntegAttendance(integAttID, compID, userID, settingID, 30, "success", "kot")
	expIntegAtt64.ErrSummaries = "{\"errors\":[{\"count\":1,\"errType\":\"attendance_synchronized_by_another_condition_existed\"},{\"count\":1,\"errType\":\"cannot_get_employee\"}]}"
	expIntegAtt64.EndTime = &flexNow
	preEmps64 := i.genTestEmployee(compID, 2)
	ts64 := i.genTestTimesheet(compID, 1, true)
	tsRest64 := i.genTestTimesheetRest(compID, 1, true)
	case64 := integExecTcase{
		desc:         "OK:KOT_timesheet_and_rest_created, update jobcan with kot",
		preComp:      company,
		preEmps:      preEmps64,
		preIntegAtt:  []tbl.IntegrationAttendance{integKOT},
		expIntegAtt:  []tbl.IntegrationAttendance{expIntegAtt64},
		preTsheet:    []tbl.Timesheet{},
		preTsRest:    []tbl.TimesheetRest{},
		expTsheet:    ts64,
		expTsRest:    tsRest64,
		preIntegKOT:  kotSetting,
		preIntegdAtt: preIntegdAtts64,
		expIntegdAtt: expIntegdAtts64,
		input:        in,
		setupKOTHTTPMocks: func() func() {
			kotReuesterTester := kotRequesterTester{}
			cleanup := kotReuesterTester.setupHTTPMock()
			kotReuesterTester.setupKOTEmployeeRequesterMock(t, infra.KOTEmployeesResponse{
				{Code: "EmployeeCd1", Key: "1"},
			}, nil)
			kotReuesterTester.setupKOTDailyWorkingsRequesterMock(t, []infra.KOTDailyWorkings{
				{Date: "2022-12-01", DailyWorkings: []infra.KOTDailyWorking{
					kotReuesterTester.genKOTDailyWorkingTest(testKOTDailyWorkingProps{
						Date:        "2022-12-01",
						EmployeeKey: "1",
						BreakTime:   60,
						TotalWork:   480,
					}),
				}},
			}, nil)
			kotReuesterTester.setupKOTTimerecordsRequesterMock(t, []infra.KOTTimerecord{
				{Date: "2022-12-01", DailyWorkings: []infra.KOTTimerecordDailyWorking{
					kotReuesterTester.genTimerecordDailyWorking("2022-12-01", "1",
						kotReuesterTester.withAttendanceStartRecordTest(kotReuesterTester.genRecordTime("2022-12-01", "09:00:00")),
						kotReuesterTester.withAttendanceEndRecordTest(kotReuesterTester.genRecordTime("2022-12-01", "18:00:00")),
						kotReuesterTester.withRestStartRecordTest(kotReuesterTester.genRecordTime("2022-12-01", "12:00:00")),
						kotReuesterTester.withRestEndRecordTest(kotReuesterTester.genRecordTime("2022-12-01", "13:00:00")),
					),
				}},
			}, nil)
			return cleanup
		},
		cleanDB: true,
		flexNow: &flexNow,
	}

	// CASE65 ====================
	jobSums65 := i.genTestJobcanSummary(2)
	mockhttpJobSumms65 := mockHttpRes{
		status:   http.StatusOK,
		body:     infra.Summaries{Summaries: jobSums65},
		multiRes: true,
	}
	expIntegAtt65 := integAtt
	expIntegAtt65.Status = "success"
	expIntegAtt65.ErrSummaries = "{\"errors\":[{\"count\":1,\"errType\":\"attendance_synchronized_by_another_condition_existed\"}]}"
	expIntegAtt65.EndTime = &flexNow
	preIntegdAtts65 := i.genIntegratedAttendance(compID, userID, integAttID, jobSums65, "kot")[1]
	expIntegdAtts65 := i.genIntegratedAttendance(compID, userID, integAttID, jobSums65, "jobcan")
	expIntegdAtts65[0].TargetSystem = "jobcan"
	ts65 := i.genTestTimesheet(compID, 2, true)
	tsRest65 := i.genTestTimesheetRest(compID, 2, true)
	case65 := integExecTcase{
		desc:         "OK:KOT_timesheet_and_rest_created, update kot with jobcan",
		preComp:      company,
		preEmps:      emps,
		preIntegAtt:  []tbl.IntegrationAttendance{integAtt},
		expIntegAtt:  []tbl.IntegrationAttendance{expIntegAtt65},
		preTsheet:    []tbl.Timesheet{},
		preTsRest:    []tbl.TimesheetRest{},
		expTsheet:    ts65,
		expTsRest:    tsRest65,
		preIntegJob:  jobcanSetting,
		preIntegdAtt: []tbl.IntegratedAttendance{preIntegdAtts65},
		expIntegdAtt: expIntegdAtts65,
		input:        in,
		mockhttpJTkn: &mockhttpJobTkn,
		mockhttpJEmp: &mockhttpJobEmps,
		mockhttpJSum: &mockhttpJobSumms65,
		cleanDB:      true,
		flexNow:      &flexNow,
	}

	// CASE66 ====================
	integAttID66 := 2
	KOTSettingID66 := 2
	input66 := in
	input66.sqsMsgParams.ID = KOTSettingID66
	preIntegdAtts66 := i.genIntegratedAttendance(compID, userID, integAttID, jobSumsKOT, "kot")
	expIntegdAtts66 := i.genIntegratedAttendance(compID, userID, integAttID66, jobSumsKOT, "kot")
	expKot1IntegAtt66 := i.genTestIntegAttendance(integAttID, compID, userID, settingID, 30, "success", "kot")
	expKot1IntegAtt66.ErrSummaries = "{\"errors\":[{\"count\":1,\"errType\":\"attendance_synchronized_by_another_condition_existed\"},{\"count\":1,\"errType\":\"cannot_get_employee\"}]}"
	expKot1IntegAtt66.EndTime = &flexNow
	expKot2IntegAtt66 := expKot1IntegAtt66
	expKot2IntegAtt66.ID = integAttID66
	expKot2IntegAtt66.IntegrationSettingID = KOTSettingID66
	preEmps66 := i.genTestEmployee(compID, 2)
	ts66 := i.genTestTimesheet(compID, 1, true)
	tsRest66 := i.genTestTimesheetRest(compID, 1, true)
	case66 := integExecTcase{
		desc:    "OK:KOT_timesheet_and_rest_created, update kot account(ID:1) with another kot account(ID:2)",
		preComp: company,
		preEmps: preEmps66,
		preIntegAtt: []tbl.IntegrationAttendance{expKot1IntegAtt66,
			i.genTestIntegAttendance(integAttID66, compID, userID, KOTSettingID66, 30, "waiting", "kot")},
		expIntegAtt:  []tbl.IntegrationAttendance{expKot1IntegAtt66, expKot2IntegAtt66},
		preTsheet:    []tbl.Timesheet{},
		preTsRest:    []tbl.TimesheetRest{},
		expTsheet:    ts66,
		expTsRest:    tsRest66,
		preIntegKOT:  i.genTestIntegrationKOT(KOTSettingID66, compID, userID),
		preIntegdAtt: preIntegdAtts66,
		expIntegdAtt: expIntegdAtts66,
		input:        input66,
		setupKOTHTTPMocks: func() func() {
			kotReuesterTester := kotRequesterTester{}
			cleanup := kotReuesterTester.setupHTTPMock()
			kotReuesterTester.setupKOTEmployeeRequesterMock(t, infra.KOTEmployeesResponse{
				{Code: "EmployeeCd1", Key: "1"},
			}, nil)
			kotReuesterTester.setupKOTDailyWorkingsRequesterMock(t, []infra.KOTDailyWorkings{
				{Date: "2022-12-01", DailyWorkings: []infra.KOTDailyWorking{
					kotReuesterTester.genKOTDailyWorkingTest(testKOTDailyWorkingProps{
						Date:        "2022-12-01",
						EmployeeKey: "1",
						BreakTime:   60,
						TotalWork:   480,
					}),
				}},
			}, nil)
			kotReuesterTester.setupKOTTimerecordsRequesterMock(t, []infra.KOTTimerecord{
				{Date: "2022-12-01", DailyWorkings: []infra.KOTTimerecordDailyWorking{
					kotReuesterTester.genTimerecordDailyWorking("2022-12-01", "1",
						kotReuesterTester.withAttendanceStartRecordTest(kotReuesterTester.genRecordTime("2022-12-01", "09:00:00")),
						kotReuesterTester.withAttendanceEndRecordTest(kotReuesterTester.genRecordTime("2022-12-01", "18:00:00")),
						kotReuesterTester.withRestStartRecordTest(kotReuesterTester.genRecordTime("2022-12-01", "12:00:00")),
						kotReuesterTester.withRestEndRecordTest(kotReuesterTester.genRecordTime("2022-12-01", "13:00:00")),
					),
				}},
			}, nil)
			return cleanup
		},
		cleanDB: true,
		flexNow: &flexNow,
	}

	// CASE67 ====================
	/*
		there are tests for invalid timerecords separately(Test_kotReqester_isValidTimerecords)
	*/
	expIntegdAtt67 := i.genIntegratedAttendance(compID, userID, integAttID, []infra.Summary{
		{
			EmployeeId:   uint(1),
			Date:         util.DateToTime("2022-12-01").Format("2006-01-02"),
			Work:         uint(480),
			Rest:         uint(60),
			WorkStart:    "09:00",
			WorkEnd:      "18:00",
			VacationType: "none",
		},
	}, "kot")
	expIntegAtt67 := i.genTestIntegAttendance(integAttID, compID, userID, settingID, 30, "success", "kot")
	expIntegAtt67.ErrSummaries = "{\"errors\":[]}"
	expIntegAtt67.EndTime = &flexNow
	preEmps67 := i.genTestEmployee(compID, 1)
	expTs67 := i.genTestTimesheet(compID, 1, false)
	expTsRest67 := i.genTestTimesheetRest(compID, 1, false)
	case67 := integExecTcase{
		desc:         "OK:KOT_timesheet_and_rest_created, 2 emps(includes invalid timerecord)",
		preComp:      company,
		preEmps:      preEmps67,
		preIntegAtt:  []tbl.IntegrationAttendance{integKOT},
		expIntegAtt:  []tbl.IntegrationAttendance{expIntegAtt67},
		preTsheet:    []tbl.Timesheet{},
		expTsheet:    expTs67,
		preTsRest:    []tbl.TimesheetRest{},
		expTsRest:    expTsRest67,
		preIntegKOT:  kotSetting,
		expIntegdAtt: expIntegdAtt67,
		input:        in,
		setupKOTHTTPMocks: func() func() {
			kotReuesterTester := kotRequesterTester{}
			cleanup := kotReuesterTester.setupHTTPMock()
			kotReuesterTester.setupKOTEmployeeRequesterMock(t, infra.KOTEmployeesResponse{
				{Code: "EmployeeCd1", Key: "1"},
				{Code: "EmployeeCd2", Key: "2"},
			}, nil)
			kotReuesterTester.setupKOTDailyWorkingsRequesterMock(t, []infra.KOTDailyWorkings{
				{Date: "2022-12-01", DailyWorkings: []infra.KOTDailyWorking{
					kotReuesterTester.genKOTDailyWorkingTest(testKOTDailyWorkingProps{
						Date:        "2022-12-01",
						EmployeeKey: "1",
						BreakTime:   60,
						TotalWork:   480,
					}),
				}},
				{Date: "2022-12-01", DailyWorkings: []infra.KOTDailyWorking{
					kotReuesterTester.genKOTDailyWorkingTest(testKOTDailyWorkingProps{
						Date:        "2022-12-01",
						EmployeeKey: "2",
						BreakTime:   60,
						TotalWork:   480,
					}),
				}},
			}, nil)
			kotReuesterTester.setupKOTTimerecordsRequesterMock(t, []infra.KOTTimerecord{
				{Date: "2022-12-01", DailyWorkings: []infra.KOTTimerecordDailyWorking{
					kotReuesterTester.genTimerecordDailyWorking("2022-12-01", "1",
						kotReuesterTester.withAttendanceStartRecordTest(kotReuesterTester.genRecordTime("2022-12-01", "09:00:00")),
						kotReuesterTester.withAttendanceEndRecordTest(kotReuesterTester.genRecordTime("2022-12-01", "18:00:00")),
						kotReuesterTester.withRestStartRecordTest(kotReuesterTester.genRecordTime("2022-12-01", "12:00:00")),
						kotReuesterTester.withRestEndRecordTest(kotReuesterTester.genRecordTime("2022-12-01", "13:00:00")),
					),
				}},
				{Date: "2022-12-01", DailyWorkings: []infra.KOTTimerecordDailyWorking{
					kotReuesterTester.genTimerecordDailyWorking("2022-12-01", "2",
						kotReuesterTester.withAttendanceStartRecordTest(kotReuesterTester.genRecordTime("2022-12-01", "09:00:00")),
						kotReuesterTester.withAttendanceEndRecordTest(kotReuesterTester.genRecordTime("2022-12-01", "18:00:00")),
						kotReuesterTester.withRestStartRecordTest(kotReuesterTester.genRecordTime("2022-12-01", "18:00:00")),
						kotReuesterTester.withRestEndRecordTest(kotReuesterTester.genRecordTime("2022-12-01", "19:00:00")),
						kotReuesterTester.withAttendanceStartRecordTest(kotReuesterTester.genRecordTime("2022-12-01", "19:00:00")),
						kotReuesterTester.withAttendanceEndRecordTest(kotReuesterTester.genRecordTime("2022-12-01", "22:00:00")),
					),
				}},
			}, nil)
			return cleanup
		},
		cleanDB: true,
		flexNow: &flexNow,
	}

	// CASE68 ====================
	mockhttpJobSumms68 := mockHttpRes{
		status:   http.StatusOK,
		body:     infra.Summaries{Summaries: jobSums},
		multiRes: true,
	}
	integAttID68 := 2
	input68 := in
	input68.sqsMsgParams.ID = integAttID68
	preIntegdAtts68 := i.genIntegratedAttendance(compID, userID, integAttID, jobSums, "jobcan")
	expIntegdAtts68 := i.genIntegratedAttendance(compID, userID, integAttID68, jobSums, "jobcan")

	expJobcan1IntegAtt68 := i.genTestIntegAttendance(integAttID, compID, userID, settingID, 30, "success", "jobcan")
	expJobcan1IntegAtt68.ErrSummaries = "{\"errors\":[]}"
	expJobcan1IntegAtt68.EndTime = &flexNow

	expJobcan2IntegAtt68 := i.genTestIntegAttendance(integAttID68, compID, userID, settingID, 30, "success", "jobcan")
	expJobcan2IntegAtt68.ErrSummaries = "{\"errors\":[]}"
	expJobcan2IntegAtt68.EndTime = &flexNow

	ts68 := i.genTestTimesheet(compID, 1, true)
	tsRest68 := i.genTestTimesheetRest(compID, 1, true)
	case68 := integExecTcase{
		desc:    "OK:Jobcan_timesheet_and_rest_created with checking same sync condition, and same condition",
		preComp: company,
		preEmps: emps,
		preIntegAtt: []tbl.IntegrationAttendance{
			expJobcan1IntegAtt68,
			i.genTestIntegAttendance(integAttID68, compID, userID, settingID, 30, "waiting", "jobcan"),
		},
		expIntegAtt:  []tbl.IntegrationAttendance{expJobcan1IntegAtt68, expJobcan2IntegAtt68},
		preTsheet:    []tbl.Timesheet{},
		preTsRest:    []tbl.TimesheetRest{},
		expTsheet:    ts68,
		expTsRest:    tsRest68,
		preIntegJob:  i.genTestIntegrationJobcan(settingID, compID, userID),
		preIntegdAtt: preIntegdAtts68,
		expIntegdAtt: expIntegdAtts68,
		input:        input68,
		mockhttpJTkn: &mockhttpJobTkn,
		mockhttpJEmp: &mockhttpJobEmps,
		mockhttpJSum: &mockhttpJobSumms68,
		cleanDB:      true,
		flexNow:      &flexNow,
	}

	// CASE69 ====================
	mockhttpJobSumms69 := mockHttpRes{
		status:   http.StatusOK,
		body:     infra.Summaries{Summaries: jobSums},
		multiRes: true,
	}
	integAttID69 := 2
	settingID69 := 100
	input69 := in
	input69.sqsMsgParams.ID = integAttID69
	preIntegdAtts69 := i.genIntegratedAttendance(compID, userID, integAttID, jobSums, "kot")
	expIntegdAtts69 := i.genIntegratedAttendance(compID, userID, integAttID69, jobSums, "jobcan")

	expJobcan1IntegAtt69 := i.genTestIntegAttendance(integAttID, compID, userID, settingID69, 30, "success", "jobcan")
	expJobcan1IntegAtt69.ErrSummaries = "{\"errors\":[]}"
	expJobcan1IntegAtt69.EndTime = &flexNow

	expJobcan2IntegAtt69 := i.genTestIntegAttendance(integAttID69, compID, userID, settingID, 30, "success", "jobcan")
	expJobcan2IntegAtt69.ErrSummaries = "{\"errors\":[{\"count\":1,\"errType\":\"attendance_synchronized_by_another_condition_existed\"}]}"
	expJobcan2IntegAtt69.EndTime = &flexNow

	ts69 := i.genTestTimesheet(compID, 1, true)
	tsRest69 := i.genTestTimesheetRest(compID, 1, true)
	case69 := integExecTcase{
		desc:    "OK:Jobcan_timesheet_and_rest_created with checking same sync condition, but not same target system",
		preComp: company,
		preEmps: emps,
		preIntegAtt: []tbl.IntegrationAttendance{
			expJobcan1IntegAtt69,
			i.genTestIntegAttendance(integAttID69, compID, userID, settingID, 30, "waiting", "jobcan"),
		},
		expIntegAtt:  []tbl.IntegrationAttendance{expJobcan1IntegAtt69, expJobcan2IntegAtt69},
		preTsheet:    []tbl.Timesheet{},
		preTsRest:    []tbl.TimesheetRest{},
		expTsheet:    ts69,
		expTsRest:    tsRest69,
		preIntegJob:  i.genTestIntegrationJobcan(settingID, compID, userID),
		preIntegdAtt: preIntegdAtts69,
		expIntegdAtt: expIntegdAtts69,
		input:        input69,
		mockhttpJTkn: &mockhttpJobTkn,
		mockhttpJEmp: &mockhttpJobEmps,
		mockhttpJSum: &mockhttpJobSumms69,
		cleanDB:      true,
		flexNow:      &flexNow,
	}

	// CASE70 ====================
	mockhttpJobSumms70 := mockHttpRes{
		status:   http.StatusOK,
		body:     infra.Summaries{Summaries: jobSums},
		multiRes: true,
	}
	integAttID70 := 2
	settingID70 := 100
	input70 := in
	input70.sqsMsgParams.ID = integAttID70
	preIntegdAtts70 := i.genIntegratedAttendance(compID, userID, integAttID, jobSums, "jobcan")
	expIntegdAtts70 := i.genIntegratedAttendance(compID, userID, integAttID70, jobSums, "jobcan")

	expJobcan1IntegAtt70 := i.genTestIntegAttendance(integAttID, compID, userID, settingID70, 30, "success", "jobcan")
	expJobcan1IntegAtt70.ErrSummaries = "{\"errors\":[]}"
	expJobcan1IntegAtt70.EndTime = &flexNow

	expJobcan2IntegAtt70 := i.genTestIntegAttendance(integAttID70, compID, userID, settingID, 30, "success", "jobcan")
	expJobcan2IntegAtt70.ErrSummaries = "{\"errors\":[{\"count\":1,\"errType\":\"attendance_synchronized_by_another_condition_existed\"}]}"
	expJobcan2IntegAtt70.EndTime = &flexNow

	ts70 := i.genTestTimesheet(compID, 1, true)
	tsRest70 := i.genTestTimesheetRest(compID, 1, true)
	case70 := integExecTcase{
		desc:    "OK:Jobcan_timesheet_and_rest_created with checking same sync condition, but not same setting",
		preComp: company,
		preEmps: emps,
		preIntegAtt: []tbl.IntegrationAttendance{
			expJobcan1IntegAtt70,
			i.genTestIntegAttendance(integAttID70, compID, userID, settingID, 30, "waiting", "jobcan"),
		},
		expIntegAtt:  []tbl.IntegrationAttendance{expJobcan1IntegAtt70, expJobcan2IntegAtt70},
		preTsheet:    []tbl.Timesheet{},
		preTsRest:    []tbl.TimesheetRest{},
		expTsheet:    ts70,
		expTsRest:    tsRest70,
		preIntegJob:  i.genTestIntegrationJobcan(settingID, compID, userID),
		preIntegdAtt: preIntegdAtts70,
		expIntegdAtt: expIntegdAtts70,
		input:        input70,
		mockhttpJTkn: &mockhttpJobTkn,
		mockhttpJEmp: &mockhttpJobEmps,
		mockhttpJSum: &mockhttpJobSumms70,
		cleanDB:      true,
		flexNow:      &flexNow,
	}

	// CASE71 ====================
	mockhttpJobSumms71 := mockHttpRes{
		status:   http.StatusOK,
		body:     infra.Summaries{Summaries: jobSums},
		multiRes: true,
	}
	integAttID71 := 2
	input71 := in
	input71.sqsMsgParams.ID = integAttID71
	preIntegdAtts71 := i.genIntegratedAttendance(compID, userID, 0, jobSums, "jobcan")
	expIntegdAtts71 := i.genIntegratedAttendance(compID, userID, integAttID71, jobSums, "jobcan")

	expJobcan1IntegAtt71 := i.genTestIntegAttendance(integAttID, compID, userID, settingID, 30, "success", "jobcan")
	expJobcan1IntegAtt71.ErrSummaries = "{\"errors\":[]}"
	expJobcan1IntegAtt71.EndTime = &flexNow

	expJobcan2IntegAtt71 := i.genTestIntegAttendance(integAttID71, compID, userID, settingID, 30, "success", "jobcan")
	expJobcan2IntegAtt71.ErrSummaries = "{\"errors\":[{\"count\":1,\"errType\":\"attendance_synchronized_by_another_condition_existed\"}]}"
	expJobcan2IntegAtt71.EndTime = &flexNow

	ts71 := i.genTestTimesheet(compID, 1, true)
	tsRest71 := i.genTestTimesheetRest(compID, 1, true)
	case71 := integExecTcase{
		desc:    "OK:Jobcan_timesheet_and_rest_created with checking same sync condition, but integration_attendance_id=0",
		preComp: company,
		preEmps: emps,
		preIntegAtt: []tbl.IntegrationAttendance{
			expJobcan1IntegAtt71,
			i.genTestIntegAttendance(integAttID71, compID, userID, settingID, 30, "waiting", "jobcan"),
		},
		expIntegAtt:  []tbl.IntegrationAttendance{expJobcan1IntegAtt71, expJobcan2IntegAtt71},
		preTsheet:    []tbl.Timesheet{},
		preTsRest:    []tbl.TimesheetRest{},
		expTsheet:    ts71,
		expTsRest:    tsRest71,
		preIntegJob:  i.genTestIntegrationJobcan(settingID, compID, userID),
		preIntegdAtt: preIntegdAtts71,
		expIntegdAtt: expIntegdAtts71,
		input:        input71,
		mockhttpJTkn: &mockhttpJobTkn,
		mockhttpJEmp: &mockhttpJobEmps,
		mockhttpJSum: &mockhttpJobSumms71,
		cleanDB:      true,
		flexNow:      &flexNow,
	}

	// CASE72 ====================
	mockhttpJobSumms72 := mockHttpRes{
		status:   http.StatusOK,
		body:     infra.Summaries{Summaries: jobSums},
		multiRes: true,
	}
	integAttID72 := 2
	input72 := in
	input72.sqsMsgParams.ID = integAttID72
	preIntegdAtts72 := i.genIntegratedAttendance(compID /*integAttID*/, userID, 100, jobSums, "jobcan")
	expIntegdAtts72 := i.genIntegratedAttendance(compID /*integAttID72*/, userID, 100, jobSums, "jobcan")

	expJobcan1IntegAtt72 := i.genTestIntegAttendance(integAttID, compID, userID, settingID, 30, "success", "jobcan")
	expJobcan1IntegAtt72.ErrSummaries = "{\"errors\":[]}"
	expJobcan1IntegAtt72.EndTime = &flexNow

	expJobcan2IntegAtt72 := i.genTestIntegAttendance(integAttID72, compID, userID, settingID, 30, "success", "jobcan")
	expJobcan2IntegAtt72.ErrSummaries = "{\"errors\":[{\"count\":1,\"errType\":\"cannot_save_attendance\"}]}"
	expJobcan2IntegAtt72.EndTime = &flexNow

	case72 := integExecTcase{
		desc:    "ERR:Jobcan_timesheet_and_rest_created with checking same sync condition, but integration_attendance FindSpecific() error",
		preComp: company,
		preEmps: emps,
		preIntegAtt: []tbl.IntegrationAttendance{
			expJobcan1IntegAtt72,
			i.genTestIntegAttendance(integAttID72, compID, userID, settingID, 30, "waiting", "jobcan"),
		},
		expIntegAtt:  []tbl.IntegrationAttendance{expJobcan1IntegAtt72, expJobcan2IntegAtt72},
		preTsheet:    []tbl.Timesheet{},
		preTsRest:    []tbl.TimesheetRest{},
		expTsheet:    []tbl.Timesheet{},
		expTsRest:    []tbl.TimesheetRest{},
		preIntegJob:  i.genTestIntegrationJobcan(settingID, compID, userID),
		preIntegdAtt: preIntegdAtts72,
		expIntegdAtt: expIntegdAtts72,
		input:        input72,
		mockhttpJTkn: &mockhttpJobTkn,
		mockhttpJEmp: &mockhttpJobEmps,
		mockhttpJSum: &mockhttpJobSumms72,
		cleanDB:      true,
		flexNow:      &flexNow,
	}

	// CASE73 ====================
	expIntegAtt73 := integAtt
	expIntegAtt73.Status = "failure"
	expIntegAtt73.ErrSummaries = "{\"errors\":[]}"
	expIntegAtt73.EndTime = &flexNow
	case73 := integExecTcase{
		desc:        "ERR:CASE73_invalid_jobcan_api_client_id_or_secret (invalid_client)",
		preComp:     company,
		preEmps:     emps,
		preIntegAtt: []tbl.IntegrationAttendance{integAtt},
		expIntegAtt: []tbl.IntegrationAttendance{expIntegAtt73},
		preIntegJob: jobcanSetting,
		input:       in,
		mockhttpJTkn: &mockHttpRes{
			status: http.StatusBadRequest,
			body: &util.H{
				"error": "invalid_client",
			},
		},
		cleanDB: true,
		flexNow: &flexNow,
	}

	// CASE74 ====================
	expIntegAtt74 := integAtt
	expIntegAtt74.Status = "failure"
	expIntegAtt74.ErrSummaries = "{\"errors\":[]}"
	expIntegAtt74.EndTime = &flexNow
	case74 := integExecTcase{
		desc:        "ERR:CASE74_invalid_jobcan_api_client_id_or_secret (invalid_grant)",
		preComp:     company,
		preEmps:     emps,
		preIntegAtt: []tbl.IntegrationAttendance{integAtt},
		expIntegAtt: []tbl.IntegrationAttendance{expIntegAtt74},
		preIntegJob: jobcanSetting,
		input:       in,
		mockhttpJTkn: &mockHttpRes{
			status: http.StatusBadRequest,
			body: &util.H{
				"error": "invalid_grant",
			},
		},
		cleanDB: true,
		flexNow: &flexNow,
	}

	// CASE75 ====================
	expIntegAtt75 := integAtt
	expIntegAtt75.Status = "failure"
	expIntegAtt75.ErrSummaries = "{\"errors\":[]}"
	expIntegAtt75.EndTime = &flexNow
	case75 := integExecTcase{
		desc:        "ERR:CASE75_invalid_jobcan_api_client_id_or_secret (unauthorized_client)",
		preComp:     company,
		preEmps:     emps,
		preIntegAtt: []tbl.IntegrationAttendance{integAtt},
		expIntegAtt: []tbl.IntegrationAttendance{expIntegAtt75},
		preIntegJob: jobcanSetting,
		input:       in,
		mockhttpJTkn: &mockHttpRes{
			status: http.StatusBadRequest,
			body: &util.H{
				"error": "unauthorized_client",
			},
		},
		cleanDB: true,
		flexNow: &flexNow,
	}

	// CASE76 ====================
	expIntegAtt76 := integAtt
	expIntegAtt76.Status = "failure"
	expIntegAtt76.ErrSummaries = "{\"errors\":[]}"
	expIntegAtt76.EndTime = &flexNow
	case76 := integExecTcase{
		desc:        "ERR:CASE76_invalid_jobcan_api_client_id_or_secret (unsupported_grant_type)",
		preComp:     company,
		preEmps:     emps,
		preIntegAtt: []tbl.IntegrationAttendance{integAtt},
		expIntegAtt: []tbl.IntegrationAttendance{expIntegAtt76},
		preIntegJob: jobcanSetting,
		input:       in,
		mockhttpJTkn: &mockHttpRes{
			status: http.StatusBadRequest,
			body: &util.H{
				"error": "unsupported_grant_type",
			},
		},
		cleanDB: true,
		flexNow: &flexNow,
	}

	// CASE77 ====================
	expIntegAtt77 := integAtt
	expIntegAtt77.Status = "failure"
	expIntegAtt77.ErrSummaries = "{\"errors\":[]}"
	expIntegAtt77.EndTime = &flexNow
	case77 := integExecTcase{
		desc:        "ERR:CASE77_invalid_jobcan_api_client_id_or_secret (invalid_scope)",
		preComp:     company,
		preEmps:     emps,
		preIntegAtt: []tbl.IntegrationAttendance{integAtt},
		expIntegAtt: []tbl.IntegrationAttendance{expIntegAtt77},
		preIntegJob: jobcanSetting,
		input:       in,
		mockhttpJTkn: &mockHttpRes{
			status: http.StatusBadRequest,
			body: &util.H{
				"error": "invalid_scope",
			},
		},
		cleanDB: true,
		flexNow: &flexNow,
	}

	// CASE78 ====================
	expIntegAtt78 := i.genTestIntegAttendance(integAttID, compID, userID, settingID, 30, "success", "kot")
	expIntegAtt78.ErrSummaries = "{\"errors\":[]}"
	expIntegAtt78.EndTime = &flexNow
	expIntegAtt78.Status = "failure"
	preEmps78 := i.genTestEmployee(compID, 2)
	case78 := integExecTcase{
		desc:         "NG:KOT_Employee_API_return_error(Company is invalid)",
		preComp:      company,
		preEmps:      preEmps78,
		preIntegAtt:  []tbl.IntegrationAttendance{integKOT},
		expIntegAtt:  []tbl.IntegrationAttendance{expIntegAtt78},
		preTsheet:    []tbl.Timesheet{},
		expTsheet:    []tbl.Timesheet{},
		preTsRest:    []tbl.TimesheetRest{},
		expTsRest:    []tbl.TimesheetRest{},
		preIntegKOT:  kotSetting,
		expIntegdAtt: []tbl.IntegratedAttendance{},
		input:        in,
		setupKOTHTTPMocks: func() func() {
			kotReuesterTester := kotRequesterTester{}
			cleanup := kotReuesterTester.setupHTTPMock()

			httpmock.RegisterResponder("GET", config.Integ.KOT.EmployeeApiUrl,
				func(req *http.Request) (*http.Response, error) {
					return httpmock.NewStringResponse(http.StatusBadRequest,
						"{\"errors\":[{\"message\":\"company is invalid\",\"code\":221}]}"), nil
				})
			return cleanup
		},
		cleanDB: true,
		flexNow: &flexNow,
	}

	// CASE79 ====================
	expIntegAtt79 := i.genTestIntegAttendance(integAttID, compID, userID, settingID, 30, "success", "kot")
	expIntegAtt79.ErrSummaries = "{\"errors\":[]}"
	expIntegAtt79.EndTime = &flexNow
	expIntegAtt79.Status = "failure"
	preEmps79 := i.genTestEmployee(compID, 2)
	case79 := integExecTcase{
		desc:         "NG:KOT_Employee_API_return_error(認証に失敗しました)",
		preComp:      company,
		preEmps:      preEmps79,
		preIntegAtt:  []tbl.IntegrationAttendance{integKOT},
		expIntegAtt:  []tbl.IntegrationAttendance{expIntegAtt79},
		preTsheet:    []tbl.Timesheet{},
		expTsheet:    []tbl.Timesheet{},
		preTsRest:    []tbl.TimesheetRest{},
		expTsRest:    []tbl.TimesheetRest{},
		preIntegKOT:  kotSetting,
		expIntegdAtt: []tbl.IntegratedAttendance{},
		input:        in,
		setupKOTHTTPMocks: func() func() {
			kotReuesterTester := kotRequesterTester{}
			cleanup := kotReuesterTester.setupHTTPMock()

			httpmock.RegisterResponder("GET", config.Integ.KOT.EmployeeApiUrl,
				func(req *http.Request) (*http.Response, error) {
					return httpmock.NewStringResponse(http.StatusBadRequest,
						"{\"errors\":[{\"message\":\"認証に失敗しました\",\"code\":100}]}"), nil
				})
			return cleanup
		},
		cleanDB: true,
		flexNow: &flexNow,
	}

	kinkakujiCases := i.genTestCaseKinkakujiRelated(t, compID, userID)
	hrmosCases, mockCtrls := i.genTestCaseHrmosRelated(t, compID, userID)
	for _, ctrl := range mockCtrls {
		ctrl.Finish()
	}

	// CASE80 ====================
	expIntegdAtts80 := i.genIntegratedAttendance(compID, userID, integAttID, jobSumsKOT, "kot")
	expIntegdAtts80[0].StartTime = "09:00:50"
	expIntegdAtts80[0].EndTime = "18:00:10"
	expIntegAtt80 := i.genTestIntegAttendance(integAttID, compID, userID, settingID, 30, "success", "kot")
	expIntegAtt80.ErrSummaries = "{\"errors\":[{\"count\":1,\"errType\":\"cannot_get_employee\"}]}"
	expIntegAtt80.EndTime = &flexNow
	preEmps80 := i.genTestEmployee(compID, 2)
	expTs80 := i.genTestTimesheet(compID, 1, false)
	expTsRest80 := i.genTestTimesheetRest(compID, 1, false)
	case80 := integExecTcase{
		desc:         "Case80 - OK:KOT_timesheet_and_rest_created_with_cutting_off_seconds",
		preComp:      company,
		preEmps:      preEmps80,
		preIntegAtt:  []tbl.IntegrationAttendance{integKOT},
		expIntegAtt:  []tbl.IntegrationAttendance{expIntegAtt80},
		preTsheet:    []tbl.Timesheet{},
		expTsheet:    expTs80,
		preTsRest:    []tbl.TimesheetRest{},
		expTsRest:    expTsRest80,
		preIntegKOT:  kotSetting,
		expIntegdAtt: expIntegdAtts80,
		input:        in,
		setupKOTHTTPMocks: func() func() {
			kotReuesterTester := kotRequesterTester{}
			cleanup := kotReuesterTester.setupHTTPMock()
			kotReuesterTester.setupKOTEmployeeRequesterMock(t, infra.KOTEmployeesResponse{
				{Code: "EmployeeCd1", Key: "1"},
			}, nil)
			kotReuesterTester.setupKOTDailyWorkingsRequesterMock(t, []infra.KOTDailyWorkings{
				{Date: "2022-12-01", DailyWorkings: []infra.KOTDailyWorking{
					kotReuesterTester.genKOTDailyWorkingTest(testKOTDailyWorkingProps{
						Date:        "2022-12-01",
						EmployeeKey: "1",
						BreakTime:   60,
						TotalWork:   480,
					}),
				}},
			}, nil)
			kotReuesterTester.setupKOTTimerecordsRequesterMock(t, []infra.KOTTimerecord{
				{Date: "2022-12-01", DailyWorkings: []infra.KOTTimerecordDailyWorking{
					kotReuesterTester.genTimerecordDailyWorking("2022-12-01", "1",
						kotReuesterTester.withAttendanceStartRecordTest(kotReuesterTester.genRecordTime("2022-12-01", "09:00:50")),
						kotReuesterTester.withAttendanceEndRecordTest(kotReuesterTester.genRecordTime("2022-12-01", "18:00:10")),
						kotReuesterTester.withRestStartRecordTest(kotReuesterTester.genRecordTime("2022-12-01", "12:00:50")),
						kotReuesterTester.withRestEndRecordTest(kotReuesterTester.genRecordTime("2022-12-01", "13:00:10")),
					),
				}},
			}, nil)
			return cleanup
		},
		cleanDB: true,
		flexNow: &flexNow,
	}

	cases := []integExecTcase{
		case1, case2, case3, case4, case5, case6,
		case7, case8, case9, case10, case11, case12, case13, case14,
		case15, case16, case17, case18, case19, case20, case21, case22,
		case23, case24, case25, case26, case27, case28, case29, case30,
		case31, case32, case33, case34, case35, case36, case37, case38,
		case39, case40, case41, case42, case43, case44, case45, case46,
		case47, case48, case49, case50,
		// KOT
		case51, case52, case53, case54, case55, case56, case57, case58,
		case59, case60, case61, case62, case63, case64, case65, case66,
		case67,
		// Additional
		case68, case69, case70, case71, case72, case73, case74, case75,
		case76, case77, case78, case79, case80,
	}

	cases = append(cases, kinkakujiCases...)
	cases = append(cases, hrmosCases...)

	i.execTest(t, cases)
}

type kotRequesterTester struct{}

func (kot *kotRequesterTester) setupHTTPMock() func() {
	client := infra.GetRestyClient()
	httpmock.ActivateNonDefault(client.GetClient())
	return func() {
		httpmock.DeactivateAndReset()
	}
}

func (kot *kotRequesterTester) setupKOTEmployeeRequesterMock(t *testing.T, res infra.KOTEmployeesResponse, errRes *[]infra.KOTError) {
	t.Helper()
	if errRes != nil {
		httpmock.RegisterResponder("GET", config.Integ.KOT.EmployeeApiUrl,
			func(req *http.Request) (*http.Response, error) {
				resMap := kot.structToJSONMap(t, errRes)
				resp, err := httpmock.NewJsonResponse(http.StatusBadRequest, &map[string]interface{}{"errors": resMap})
				if err != nil {
					return httpmock.NewStringResponse(http.StatusInternalServerError, ""), nil
				}
				return resp, nil
			})
		return
	}
	httpmock.RegisterResponder("GET", config.Integ.KOT.EmployeeApiUrl,
		func(req *http.Request) (*http.Response, error) {
			resMap := kot.structToJSONMap(t, res)
			resp, err := httpmock.NewJsonResponse(http.StatusOK, &resMap)
			if err != nil {
				return httpmock.NewStringResponse(http.StatusInternalServerError, ""), nil
			}
			return resp, nil
		})
}

func (kot *kotRequesterTester) setupKOTDailyWorkingsRequesterMock(t *testing.T, res []infra.KOTDailyWorkings, errRes *[]infra.KOTError) {
	t.Helper()
	if errRes != nil {
		httpmock.RegisterResponder("GET", config.Integ.KOT.DailyWorkingsApiUrl,
			func(req *http.Request) (*http.Response, error) {
				resMap := kot.structToJSONMap(t, errRes)
				resp, err := httpmock.NewJsonResponse(http.StatusBadRequest, &map[string]interface{}{"errors": resMap})
				if err != nil {
					return httpmock.NewStringResponse(http.StatusInternalServerError, ""), nil
				}
				return resp, nil
			})
		return
	}
	httpmock.RegisterResponder("GET", config.Integ.KOT.DailyWorkingsApiUrl,
		func(req *http.Request) (*http.Response, error) {
			resMap := kot.structToJSONMap(t, res)
			resp, err := httpmock.NewJsonResponse(http.StatusOK, &resMap)
			if err != nil {
				return httpmock.NewStringResponse(http.StatusInternalServerError, ""), nil
			}
			return resp, nil
		})
}

func (kot *kotRequesterTester) setupKOTTimerecordsRequesterMock(t *testing.T, res []infra.KOTTimerecord, errRes *[]infra.KOTError) {
	t.Helper()
	if errRes != nil {
		httpmock.RegisterResponder("GET", config.Integ.KOT.DailyWorkingsTimerecordApiUrl,
			func(req *http.Request) (*http.Response, error) {
				resMap := kot.structToJSONMap(t, errRes)
				resp, err := httpmock.NewJsonResponse(http.StatusBadRequest, &map[string]interface{}{"errors": resMap})
				if err != nil {
					return httpmock.NewStringResponse(http.StatusInternalServerError, ""), nil
				}
				return resp, nil
			})
		return
	}
	httpmock.RegisterResponder("GET", config.Integ.KOT.DailyWorkingsTimerecordApiUrl,
		func(req *http.Request) (*http.Response, error) {
			resMap := kot.structToJSONMap(t, res)
			resp, err := httpmock.NewJsonResponse(http.StatusOK, &resMap)
			if err != nil {
				return httpmock.NewStringResponse(http.StatusInternalServerError, ""), nil
			}
			return resp, nil
		})
}

func (kot *kotRequesterTester) setupHrmosTokenRequesterMock(t *testing.T, httpStatus int, res map[string]interface{}, errRes *infra.APIErr) {
	t.Helper()
	url := fmt.Sprintf(config.Integ.Hrmos.TokenApiUrl, "crowdworks")
	if errRes != nil {
		httpmock.RegisterResponder("GET", config.Integ.KOT.DailyWorkingsTimerecordApiUrl,
			func(req *http.Request) (*http.Response, error) {
				resMap := kot.structToJSONMap(t, errRes)
				resp, err := httpmock.NewJsonResponse(httpStatus, &map[string]interface{}{"errors": resMap})
				if err != nil {
					return httpmock.NewStringResponse(httpStatus, ""), nil
				}
				return resp, nil
			})
		return
	}
	httpmock.RegisterResponder("GET", url,
		func(req *http.Request) (*http.Response, error) {
			resp, err := httpmock.NewJsonResponse(httpStatus, &res)
			if err != nil {
				panic("httpmock error.")
			}
			return resp, nil
		})
}

func (kot *kotRequesterTester) setupHrmosWorkOutputsRequesterMock(t *testing.T, res []infra.HrmosWorkOutput) {
	t.Helper()
	url := fmt.Sprintf(config.Integ.Hrmos.WorkOutputsApiUrl, "crowdworks")
	url = url + "/2022-12-01"
	httpmock.RegisterResponder("GET", url,
		func(req *http.Request) (*http.Response, error) {
			resMap := kot.structToJSONMap(t, res)
			resp, err := httpmock.NewJsonResponse(http.StatusOK, &resMap)
			if err != nil {
				return httpmock.NewStringResponse(http.StatusInternalServerError, ""), nil
			}
			resp.Header.Add("X-Total-Page", "1")
			return resp, nil
		})
}

func (kot *kotRequesterTester) structToJSONMap(t *testing.T, res interface{}) []map[string]interface{} {
	responseJSON, err := json.Marshal(&res)
	if err != nil {
		t.Fatalf("fail to marshal json: %v", err)
	}
	resMap := []map[string]interface{}{}
	err = json.Unmarshal(responseJSON, &resMap)
	if err != nil {
		t.Fatalf("fail to marshal json: %v", err)
	}
	return resMap
}

type testKOTDailyWorkingProps struct {
	Date        string
	EmployeeKey string
	BreakTime   int
	TotalWork   int
	IsError     bool
}

type TestHolidayOptionFn func(ho *infra.KOTHolidaysObtained)

func (kot *kotRequesterTester) genKOTDailyWorkingTest(props testKOTDailyWorkingProps, optFn ...TestHolidayOptionFn) infra.KOTDailyWorking {
	holiday := infra.KOTHolidaysObtained{}
	for _, fn := range optFn {
		fn(&holiday)
	}
	return infra.KOTDailyWorking{
		Date:             props.Date,
		EmployeeKey:      props.EmployeeKey,
		BreakTime:        props.BreakTime,
		TotalWork:        props.TotalWork,
		HolidaysObtained: holiday,
		IsError:          props.IsError,
	}
}

func (kot *kotRequesterTester) withMorningHalfHolidayTest(ho *infra.KOTHolidaysObtained) {
	morningOff := infra.KOTHalfdayHoliday{
		Code:     "1",
		Name:     "有給",
		TypeName: string(config.KOTHalfdayHolidayMorningOff),
	}
	ho.HalfdayHolidays = append(ho.HalfdayHolidays, morningOff)
}

func (kot *kotRequesterTester) withAfternoonHalfHolidayTest(ho *infra.KOTHolidaysObtained) {
	afternoonOff := infra.KOTHalfdayHoliday{
		Code:     "1",
		Name:     "有給",
		TypeName: string(config.KOTHalfdayHolidayAfternoonOff),
	}
	ho.HalfdayHolidays = append(ho.HalfdayHolidays, afternoonOff)
}

func (kot *kotRequesterTester) withFulltimeHolidayTest(ho *infra.KOTHolidaysObtained) {
	n := "公休"
	c := "11"
	fullOff := infra.KOTFulltimeHoliday{
		Code: &c,
		Name: &n,
	}
	ho.FulltimeHoliday = fullOff
}

func (kot *kotRequesterTester) withHourHolidayTest(min int) TestHolidayOptionFn {
	return func(ho *infra.KOTHolidaysObtained) {
		hourHoliday := infra.KOTHourHoliday{
			// we decided to see hourHoliday as breakTime
			// so far, there is no need to check start and end
			Start:   "",
			End:     "",
			Minutes: fmt.Sprintf("%d", min),
			Code:    "11",
			Name:    "時間休",
		}
		ho.HourHolidays = append(ho.HourHolidays, hourHoliday)
	}
}

type testTimeRecordOptionFn func(trs *[]infra.KOTTimeRecord)

func (kot *kotRequesterTester) genTimerecordDailyWorking(date string, EmpKey string, optsFn ...testTimeRecordOptionFn) infra.KOTTimerecordDailyWorking {
	timerecords := []infra.KOTTimeRecord{}
	for _, fn := range optsFn {
		fn(&timerecords)
	}
	return infra.KOTTimerecordDailyWorking{
		Date:        date,
		EmployeeKey: EmpKey,
		TimeRecord:  timerecords,
	}
}

func (kot *kotRequesterTester) withAttendanceStartRecordTest(t time.Time) testTimeRecordOptionFn {
	return func(trs *[]infra.KOTTimeRecord) {
		ret := append(*trs, infra.KOTTimeRecord{
			Time: t,
			Code: string(config.KOTAttendanceStart),
		})
		*trs = ret
	}
}

func (kot *kotRequesterTester) withAttendanceEndRecordTest(t time.Time) testTimeRecordOptionFn {
	return func(trs *[]infra.KOTTimeRecord) {
		ret := append(*trs, infra.KOTTimeRecord{
			Time: t,
			Code: string(config.KOTAttendanceEnd),
		})
		*trs = ret
	}
}

func (kot *kotRequesterTester) withRestStartRecordTest(t time.Time) testTimeRecordOptionFn {
	return func(trs *[]infra.KOTTimeRecord) {
		ret := append(*trs, infra.KOTTimeRecord{
			Time: t,
			Code: "3",
		})
		*trs = ret
	}
}

func (kot *kotRequesterTester) withRestEndRecordTest(t time.Time) testTimeRecordOptionFn {
	return func(trs *[]infra.KOTTimeRecord) {
		ret := append(*trs, infra.KOTTimeRecord{
			Time: t,
			Code: "4",
		})
		*trs = ret
	}
}

func (kot *kotRequesterTester) genRecordTime(date string, timeStr string) time.Time {
	layout := "2006-01-02 15:04:05"
	base := fmt.Sprintf("%s %s", date, timeStr)
	ret, err := time.Parse(layout, base)
	if err != nil {
		panic(err)
	}
	return ret
}

func (i integExecTester) genTestCaseKinkakujiRelated(t *testing.T, compID int, userID int) (ret []integExecTcase) {
	t.Helper()
	settingID := 10
	flexNow := util.DateToTime("2023-01-01 09:01:00")
	integAttID := 1
	company := i.genTestCompany(compID)
	in := integExecArgs{
		sqsID: "xxx-yyy-zzz",
		sqsMsg: model.SQSMessage{
			UserInfo: model.SQSUserInfo{
				CompanyNo:  compID,
				EmployeeNo: userID,
			},
		},
		sqsMsgParams: integExecSqsMsgParams{
			ID: integAttID,
		},
	}

	integAtt := i.genTestIntegAttendance(integAttID, compID, userID, settingID, 10, "waiting", "kinkakuji")

	kinkakujiSetting := i.genTestIntegrationKinkakuji(settingID, compID, userID)
	jobSumsKOT := i.genTestJobcanSummary(1)

	// CASE1 ====================
	expIntegdAtt1 := i.genIntegratedAttendance(compID, userID, integAttID, []infra.Summary{
		{
			EmployeeId:   uint(1),
			Date:         util.DateToTime("2022-12-01").Format("2006-01-02"),
			Work:         uint(480),
			Rest:         uint(60),
			WorkStart:    "09:00",
			WorkEnd:      "18:00",
			VacationType: "morning_off",
			HolidayType:  2,
		},
		{
			EmployeeId:   uint(2),
			Date:         util.DateToTime("2022-12-01").Format("2006-01-02"),
			Work:         uint(480),
			Rest:         uint(60),
			WorkStart:    "09:00",
			WorkEnd:      "18:00",
			VacationType: "afternoon_off",
			HolidayType:  3,
		},
	}, "kinkakuji")
	expIntegAtt1 := i.genTestIntegAttendance(integAttID, compID, userID, settingID, 10, "success", "kinkakuji")
	expIntegAtt1.ErrSummaries = "{\"errors\":[]}"
	expIntegAtt1.EndTime = &flexNow
	preEmps1 := i.genTestEmployee(compID, 2)
	expTs1 := i.genTestTimesheet(compID, 2, false)
	expTs1[0].HolidayType = test.GenPtr(2)
	expTs1[1].HolidayType = test.GenPtr(3)
	expTsRest1 := i.genTestTimesheetRest(compID, 2, false)
	case1 := integExecTcase{
		desc:              "OK:Kinkakuji_timesheet_and_rest_created, 2 emps, with half holiyday",
		preComp:           company,
		preEmps:           preEmps1,
		preIntegAtt:       []tbl.IntegrationAttendance{integAtt},
		expIntegAtt:       []tbl.IntegrationAttendance{expIntegAtt1},
		preTsheet:         []tbl.Timesheet{},
		expTsheet:         expTs1,
		preTsRest:         []tbl.TimesheetRest{},
		expTsRest:         expTsRest1,
		preIntegKinkakuji: kinkakujiSetting,
		expIntegdAtt:      expIntegdAtt1,
		input:             in,
		setupKOTHTTPMocks: func() func() {
			kotReuesterTester := kotRequesterTester{}
			cleanup := kotReuesterTester.setupHTTPMock()
			kotReuesterTester.setupKOTEmployeeRequesterMock(t, infra.KOTEmployeesResponse{
				{Code: "EmployeeCd1", Key: "1"},
				{Code: "EmployeeCd2", Key: "2"},
			}, nil)
			kotReuesterTester.setupKOTDailyWorkingsRequesterMock(t, []infra.KOTDailyWorkings{
				{Date: "2022-12-01", DailyWorkings: []infra.KOTDailyWorking{
					kotReuesterTester.genKOTDailyWorkingTest(testKOTDailyWorkingProps{
						Date:        "2022-12-01",
						EmployeeKey: "1",
						BreakTime:   60,
						TotalWork:   480,
					}, kotReuesterTester.withMorningHalfHolidayTest),
				}},
				{Date: "2022-12-01", DailyWorkings: []infra.KOTDailyWorking{
					kotReuesterTester.genKOTDailyWorkingTest(testKOTDailyWorkingProps{
						Date:        "2022-12-01",
						EmployeeKey: "2",
						BreakTime:   60,
						TotalWork:   480,
					}, kotReuesterTester.withAfternoonHalfHolidayTest),
				}},
			}, nil)
			kotReuesterTester.setupKOTTimerecordsRequesterMock(t, []infra.KOTTimerecord{
				{Date: "2022-12-01", DailyWorkings: []infra.KOTTimerecordDailyWorking{
					kotReuesterTester.genTimerecordDailyWorking("2022-12-01", "1",
						kotReuesterTester.withAttendanceStartRecordTest(kotReuesterTester.genRecordTime("2022-12-01", "09:00:00")),
						kotReuesterTester.withAttendanceEndRecordTest(kotReuesterTester.genRecordTime("2022-12-01", "18:00:00")),
						kotReuesterTester.withRestStartRecordTest(kotReuesterTester.genRecordTime("2022-12-01", "12:00:00")),
						kotReuesterTester.withRestEndRecordTest(kotReuesterTester.genRecordTime("2022-12-01", "13:00:00")),
					),
				}},
				{Date: "2022-12-01", DailyWorkings: []infra.KOTTimerecordDailyWorking{
					kotReuesterTester.genTimerecordDailyWorking("2022-12-01", "2",
						kotReuesterTester.withAttendanceStartRecordTest(kotReuesterTester.genRecordTime("2022-12-01", "09:00:00")),
						kotReuesterTester.withAttendanceEndRecordTest(kotReuesterTester.genRecordTime("2022-12-01", "18:00:00")),
						kotReuesterTester.withRestStartRecordTest(kotReuesterTester.genRecordTime("2022-12-01", "12:00:00")),
						kotReuesterTester.withRestEndRecordTest(kotReuesterTester.genRecordTime("2022-12-01", "13:00:00")),
					),
				}},
			}, nil)
			return cleanup
		},
		cleanDB: true,
		flexNow: &flexNow,
	}

	// CASE2 ====================
	preIntegdAtts2 := i.genIntegratedAttendance(compID, userID, integAttID, jobSumsKOT, "jobcan")
	expIntegdAtts2 := i.genIntegratedAttendance(compID, userID, integAttID, jobSumsKOT, "kinkakuji")
	expIntegAtt2 := i.genTestIntegAttendance(integAttID, compID, userID, settingID, 10, "success", "kinkakuji")
	expIntegAtt2.ErrSummaries = "{\"errors\":[{\"count\":1,\"errType\":\"attendance_synchronized_by_another_condition_existed\"},{\"count\":1,\"errType\":\"cannot_get_employee\"}]}"
	expIntegAtt2.EndTime = &flexNow
	preEmps2 := i.genTestEmployee(compID, 2)
	ts2 := i.genTestTimesheet(compID, 1, true)
	tsRest2 := i.genTestTimesheetRest(compID, 1, true)
	case2 := integExecTcase{
		desc:              "OK:Kinkakuji_timesheet_and_rest_created, update jobcan with kinkakuji",
		preComp:           company,
		preEmps:           preEmps2,
		preIntegAtt:       []tbl.IntegrationAttendance{integAtt},
		expIntegAtt:       []tbl.IntegrationAttendance{expIntegAtt2},
		preTsheet:         []tbl.Timesheet{},
		preTsRest:         []tbl.TimesheetRest{},
		expTsheet:         ts2,
		expTsRest:         tsRest2,
		preIntegKinkakuji: kinkakujiSetting,
		preIntegdAtt:      preIntegdAtts2,
		expIntegdAtt:      expIntegdAtts2,
		input:             in,
		setupKOTHTTPMocks: func() func() {
			kotReuesterTester := kotRequesterTester{}
			cleanup := kotReuesterTester.setupHTTPMock()
			kotReuesterTester.setupKOTEmployeeRequesterMock(t, infra.KOTEmployeesResponse{
				{Code: "EmployeeCd1", Key: "1"},
			}, nil)
			kotReuesterTester.setupKOTDailyWorkingsRequesterMock(t, []infra.KOTDailyWorkings{
				{Date: "2022-12-01", DailyWorkings: []infra.KOTDailyWorking{
					kotReuesterTester.genKOTDailyWorkingTest(testKOTDailyWorkingProps{
						Date:        "2022-12-01",
						EmployeeKey: "1",
						BreakTime:   60,
						TotalWork:   480,
					}),
				}},
			}, nil)
			kotReuesterTester.setupKOTTimerecordsRequesterMock(t, []infra.KOTTimerecord{
				{Date: "2022-12-01", DailyWorkings: []infra.KOTTimerecordDailyWorking{
					kotReuesterTester.genTimerecordDailyWorking("2022-12-01", "1",
						kotReuesterTester.withAttendanceStartRecordTest(kotReuesterTester.genRecordTime("2022-12-01", "09:00:00")),
						kotReuesterTester.withAttendanceEndRecordTest(kotReuesterTester.genRecordTime("2022-12-01", "18:00:00")),
						kotReuesterTester.withRestStartRecordTest(kotReuesterTester.genRecordTime("2022-12-01", "12:00:00")),
						kotReuesterTester.withRestEndRecordTest(kotReuesterTester.genRecordTime("2022-12-01", "13:00:00")),
					),
				}},
			}, nil)
			return cleanup
		},
		cleanDB: true,
		flexNow: &flexNow,
	}

	return []integExecTcase{case1, case2}
}

func (i integExecTester) genTestCaseHrmosRelated(t *testing.T,
	compID, userID int) (ret []integExecTcase, mockCtrls []*gomock.Controller) {
	t.Helper()

	mockCtrls = make([]*gomock.Controller, 0)

	settingID := 10
	flexNow := util.DateToTime("2023-01-01 09:01:00")
	integAttID := 1
	company := i.genTestCompany(compID)
	in := integExecArgs{
		sqsID: "xxx-yyy-zzz",
		sqsMsg: model.SQSMessage{
			UserInfo: model.SQSUserInfo{
				CompanyNo:  compID,
				EmployeeNo: userID,
			},
		},
		sqsMsgParams: integExecSqsMsgParams{
			ID: integAttID,
		},
	}

	integAtt := i.genTestIntegAttendance(integAttID, compID, userID, settingID, 0, "waiting", "hrmos")

	hrmosSetting := i.genTestIntegrationHrmos(settingID, compID, userID)
	jobSumsKOT := i.genTestJobcanSummary(1)

	// CASE1 ====================
	expIntegdAtt1 := i.genIntegratedAttendance(compID, userID, integAttID, []infra.Summary{
		{
			EmployeeId:   uint(1),
			Date:         util.DateToTime("2022-12-01").Format("2006-01-02"),
			Work:         uint(240),
			Rest:         uint(60),
			WorkStart:    "13:00",
			WorkEnd:      "18:00",
			VacationType: "morning_off",
			HolidayType:  2,
		},
		{
			EmployeeId:   uint(2),
			Date:         util.DateToTime("2022-12-01").Format("2006-01-02"),
			Work:         uint(240),
			Rest:         uint(60),
			WorkStart:    "09:00",
			WorkEnd:      "14:00",
			VacationType: "afternoon_off",
			HolidayType:  3,
		},
		{
			EmployeeId:   uint(2),
			Date:         util.DateToTime("2022-12-02").Format("2006-01-02"),
			Work:         uint(1920),
			Rest:         uint(60),
			WorkStart:    "09:00",
			WorkEnd:      "42:00",
			VacationType: "none",
			HolidayType:  0,
		},
	}, "hrmos")

	expIntegAtt1 := i.genTestIntegAttendance(integAttID, compID, userID, settingID, 0, "success", "hrmos")
	expIntegAtt1.ErrSummaries = "{\"errors\":[{\"count\":1,\"errType\":\"cannot_convert_attendance\"}]}"
	expIntegAtt1.EndTime = &flexNow
	preEmps1 := i.genTestEmployee(compID, 2)
	expTs1 := i.genTestTimesheet(compID, 2, false)
	expTs1[0].StartTime = test.NewPtr("13:00:00").(*string)
	expTs1[0].HolidayType = test.GenPtr(2)
	expTs1[1].EndTime = test.NewPtr("14:00:00").(*string)
	expTs1[1].HolidayType = test.GenPtr(3)
	expTsRest1 := i.genTestTimesheetRest(compID, 2, false)
	expTsRest1[0].StartTime = test.NewPtr("14:00:00").(*string)
	expTsRest1[0].EndTime = test.NewPtr("15:00:00").(*string)
	expTsRest1[1].StartTime = test.NewPtr("10:00:00").(*string)
	expTsRest1[1].EndTime = test.NewPtr("11:00:00").(*string)
	resToken := map[string]interface{}{"token": "token", "expired_at": "2023-10-01T04:56:07.000+09:00"}

	case1 := integExecTcase{
		desc:          "OK:Hrmos_timesheet_and_rest_created, 2 emps, with half holiyday and over 24 hours work",
		preComp:       company,
		preEmps:       preEmps1,
		preIntegAtt:   []tbl.IntegrationAttendance{integAtt},
		expIntegAtt:   []tbl.IntegrationAttendance{expIntegAtt1},
		preTsheet:     []tbl.Timesheet{},
		expTsheet:     expTs1,
		preTsRest:     []tbl.TimesheetRest{},
		expTsRest:     expTsRest1,
		preIntegHrmos: hrmosSetting,
		expIntegdAtt:  expIntegdAtt1,
		input:         in,
		setupHrmosHTTPMocks: func() func() {
			kotReuesterTester := kotRequesterTester{}
			cleanup := kotReuesterTester.setupHTTPMock()
			kotReuesterTester.setupHrmosTokenRequesterMock(t, http.StatusOK, resToken, nil)
			kotReuesterTester.setupHrmosWorkOutputsRequesterMock(t, []infra.HrmosWorkOutput{
				{
					UserId:                  1,
					Number:                  "EmployeeCd1",
					Day:                     "2022-12-01",
					StartAt:                 "13:00",
					EndAt:                   "18:00",
					TotalBreakTime:          "01:00",
					TotalWorkingHours:       "04:00",
					TimePaidHoliday:         "00:00",
					ActualWorkingHours:      "04:00",
					PaidHolidayWithTimePaid: 0.5,
				},
				{
					UserId:                  2,
					Number:                  "EmployeeCd2",
					Day:                     "2022-12-01",
					StartAt:                 "9:00",
					EndAt:                   "14:00",
					TotalBreakTime:          "01:00",
					TotalWorkingHours:       "04:00",
					TimePaidHoliday:         "00:00",
					ActualWorkingHours:      "04:00",
					PaidHolidayWithTimePaid: 0.5,
				},
				{
					UserId:                  2,
					Number:                  "EmployeeCd2",
					Day:                     "2022-12-02",
					StartAt:                 "9:00",
					EndAt:                   "18:00",
					TotalBreakTime:          "01:00",
					TotalWorkingHours:       "32:00",
					TimePaidHoliday:         "00:00",
					ActualWorkingHours:      "32:00",
					PaidHolidayWithTimePaid: 0.0,
				},
			})
			return cleanup
		},
		cleanDB: true,
		flexNow: &flexNow,
	}

	// CASE2 ====================
	preIntegdAtts2 := i.genIntegratedAttendance(compID, userID, integAttID, jobSumsKOT, "jobcan")
	expIntegdAtts2 := i.genIntegratedAttendance(compID, userID, integAttID, jobSumsKOT, "hrmos")
	expIntegAtt2 := i.genTestIntegAttendance(integAttID, compID, userID, settingID, 0, "success", "hrmos")
	expIntegAtt2.ErrSummaries = "{\"errors\":[{\"count\":1,\"errType\":\"attendance_synchronized_by_another_condition_existed\"},{\"count\":1,\"errType\":\"cannot_get_employee\"}]}"
	expIntegAtt2.EndTime = &flexNow
	preEmps2 := i.genTestEmployee(compID, 2)
	ts2 := i.genTestTimesheet(compID, 1, true)
	tsRest2 := i.genTestTimesheetRest(compID, 1, true)

	case2 := integExecTcase{
		desc:          "OK:Hrmos_timesheet_and_rest_created, update jobcan with hrmos",
		preComp:       company,
		preEmps:       preEmps2,
		preIntegAtt:   []tbl.IntegrationAttendance{integAtt},
		expIntegAtt:   []tbl.IntegrationAttendance{expIntegAtt2},
		preTsheet:     []tbl.Timesheet{},
		preTsRest:     []tbl.TimesheetRest{},
		expTsheet:     ts2,
		expTsRest:     tsRest2,
		preIntegHrmos: hrmosSetting,
		preIntegdAtt:  preIntegdAtts2,
		expIntegdAtt:  expIntegdAtts2,
		input:         in,
		setupHrmosHTTPMocks: func() func() {
			kotReuesterTester := kotRequesterTester{}
			cleanup := kotReuesterTester.setupHTTPMock()
			kotReuesterTester.setupHrmosTokenRequesterMock(t, http.StatusOK, resToken, nil)
			kotReuesterTester.setupHrmosWorkOutputsRequesterMock(t, []infra.HrmosWorkOutput{
				{
					UserId:                  1,
					Number:                  "EmployeeCd1",
					Day:                     "2022-12-01",
					StartAt:                 "9:00",
					EndAt:                   "18:00",
					TotalBreakTime:          "01:00",
					TotalWorkingHours:       "04:00",
					TimePaidHoliday:         "00:00",
					ActualWorkingHours:      "04:00",
					PaidHolidayWithTimePaid: 0.0,
				},
			})
			return cleanup
		},
		cleanDB: true,
		flexNow: &flexNow,
	}

	// CASE3 ====================
	mockCtrl3 := gomock.NewController(t)
	// mockCtrls = append(mockCtrls, mockCtrl3)
	mockObj3 := mockrepo.NewMockIntegrationHrmos(mockCtrl3)
	gomock.InOrder(
		mockObj3.EXPECT().Exist(gomock.Any(), gomock.Any()).
			Return(true, nil),
		mockObj3.EXPECT().FindSpecific(gomock.Any(), gomock.Any()).
			Return(tbl.IntegrationHrmos{}, errors.New("AAA")),
	)
	mrr3 := &test.MockRepoRegistry{}
	mrr3.MIntegHrmos = mockObj3

	expIntegAtt3 := i.genTestIntegAttendance(integAttID, compID, userID, settingID, 0, "failure", "hrmos")
	expIntegAtt3.ErrSummaries = "{\"errors\":[{\"count\":1,\"errType\":\"server_error\"}]}"
	expIntegAtt3.EndTime = &flexNow
	preEmps3 := i.genTestEmployee(compID, 1)

	case3 := integExecTcase{
		desc:        "NG:Getting HRMOS setting error",
		preComp:     company,
		preEmps:     preEmps3,
		preIntegAtt: []tbl.IntegrationAttendance{integAtt},
		expIntegAtt: []tbl.IntegrationAttendance{expIntegAtt3},
		preTsheet:   []tbl.Timesheet{},
		preTsRest:   []tbl.TimesheetRest{},
		input:       in,
		expErr:      twisttest.ExpReturns{Err: errors.New("AAA"), ErrStr: "*errors.errorString"},
		cleanDB:     true,
		flexNow:     &flexNow,
		mrr:         mrr3,
	}

	// CASE4 ====================
	expIntegAtt4 := i.genTestIntegAttendance(integAttID, compID, userID, settingID, 0, "failure", "hrmos")
	expIntegAtt4.ErrSummaries = "{\"errors\":[{\"count\":1,\"errType\":\"integ_setting_not_found\"}]}"
	expIntegAtt4.EndTime = &flexNow
	preEmps4 := i.genTestEmployee(compID, 1)
	errRes4 := infra.APIErr{Code: 400, Message: "error"}

	case4 := integExecTcase{
		desc:        "NG:Getting HRMOS Token error (no hrmos integration setting)",
		preComp:     company,
		preEmps:     preEmps4,
		preIntegAtt: []tbl.IntegrationAttendance{integAtt},
		expIntegAtt: []tbl.IntegrationAttendance{expIntegAtt4},
		preTsheet:   []tbl.Timesheet{},
		preTsRest:   []tbl.TimesheetRest{},
		input:       in,
		setupHrmosHTTPMocks: func() func() {
			kotReuesterTester := kotRequesterTester{}
			cleanup := kotReuesterTester.setupHTTPMock()
			kotReuesterTester.setupHrmosTokenRequesterMock(t, http.StatusBadRequest, resToken, &errRes4)
			return cleanup
		},
		expErr:  twisttest.ExpReturns{Err: ierror.NewNoResourceErr(), ErrStr: "*ierror.noResourceErr"},
		cleanDB: true,
		flexNow: &flexNow,
	}

	// CASE5 ====================
	expIntegAtt5 := i.genTestIntegAttendance(integAttID, compID, userID, settingID, 0, "failure", "hrmos")
	expIntegAtt5.ErrSummaries = "{\"errors\":[]}"
	expIntegAtt5.EndTime = &flexNow
	preEmps5 := i.genTestEmployee(compID, 1)
	resToken5 := map[string]interface{}{"code": http.StatusBadRequest, "message": string(infra.HrmosAccessDenied)}

	case5 := integExecTcase{
		desc:          "NG:Getting HRMOS Token error (HTTP Token: Access denied.)",
		preComp:       company,
		preEmps:       preEmps5,
		preIntegAtt:   []tbl.IntegrationAttendance{integAtt},
		expIntegAtt:   []tbl.IntegrationAttendance{expIntegAtt5},
		preTsheet:     []tbl.Timesheet{},
		preTsRest:     []tbl.TimesheetRest{},
		preIntegHrmos: hrmosSetting,
		input:         in,
		setupHrmosHTTPMocks: func() func() {
			kotReuesterTester := kotRequesterTester{}
			cleanup := kotReuesterTester.setupHTTPMock()
			kotReuesterTester.setupHrmosTokenRequesterMock(t, http.StatusBadRequest, resToken5, nil)
			return cleanup
		},
		cleanDB: true,
		flexNow: &flexNow,
	}

	// CASE6 ====================
	expIntegAtt6 := i.genTestIntegAttendance(integAttID, compID, userID, settingID, 0, "failure", "hrmos")
	expIntegAtt6.ErrSummaries = "{\"errors\":[]}"
	expIntegAtt6.EndTime = &flexNow
	preEmps6 := i.genTestEmployee(compID, 1)
	resToken6 := map[string]interface{}{"code": http.StatusBadRequest, "message": string(infra.HrmosAuthFailed)}

	case6 := integExecTcase{
		desc:          "NG:Getting HRMOS Token error (Authentication Failed)",
		preComp:       company,
		preEmps:       preEmps6,
		preIntegAtt:   []tbl.IntegrationAttendance{integAtt},
		expIntegAtt:   []tbl.IntegrationAttendance{expIntegAtt6},
		preTsheet:     []tbl.Timesheet{},
		preTsRest:     []tbl.TimesheetRest{},
		preIntegHrmos: hrmosSetting,
		input:         in,
		setupHrmosHTTPMocks: func() func() {
			kotReuesterTester := kotRequesterTester{}
			cleanup := kotReuesterTester.setupHTTPMock()
			kotReuesterTester.setupHrmosTokenRequesterMock(t, http.StatusBadRequest, resToken6, nil)
			return cleanup
		},
		cleanDB: true,
		flexNow: &flexNow,
	}

	return []integExecTcase{
		case1, case2, case3, case4, case5, case6,
	}, mockCtrls
}
