package handler

import (
	"errors"
	"fmt"
	"net/http"
	"strings"
	"testing"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/golang/mock/gomock"
	"gitlab.com/innopm/deneb/common/idb"
	"gitlab.com/innopm/deneb/common/ilog"
	"gitlab.com/innopm/deneb/common/imsg"
	"gitlab.com/innopm/deneb/config"
	"gitlab.com/innopm/deneb/context/pcmanage/domain/model"
	"gitlab.com/innopm/deneb/context/pcmanage/domain/value"
	"gitlab.com/innopm/deneb/context/pcmanage/handler/res"
	mock_repo "gitlab.com/innopm/deneb/context/pcmanage/mock/repo"
	model2 "gitlab.com/innopm/deneb/domain/model"
	"gitlab.com/innopm/deneb/domain/model/tbl"
	"gitlab.com/innopm/deneb/test"
	"gitlab.com/innopm/deneb/test/assertmodel"
	"gitlab.com/innopm/deneb/test/seeds"
	"gitlab.com/innopm/deneb/util"
	"gorm.io/gorm"
)

type attendanceDifferenceReasonTcase struct {
	desc string

	preCompany             tbl.Company
	preEmployees           []tbl.Employee
	preTimesheets          []tbl.Timesheet
	preMeasuredAttendances []tbl.MeasuredAttendance
	preReasons             []tbl.AttendanceDifferenceReason
	preIntegAttDiff        []tbl.IntegrationAttendanceDifference

	expReasons []tbl.AttendanceDifferenceReason

	reqObj      test.HandlerReq
	resObj      test.HandlerRes
	fn          test.HFunc
	mrr         *test.MockRepoRegistry
	userSetting *model2.UserInfo
}

type attendanceDifferenceReasonTester struct{}

func (a *attendanceDifferenceReasonTester) dbClean(db *gorm.DB) {
	seeds.Truncate(
		db,
		&tbl.Company{},
		&tbl.Employee{},
		&tbl.Timesheet{},
		&tbl.MeasuredAttendance{},
		&tbl.AttendanceDifferenceReason{},
		&tbl.IntegrationAttendanceDifference{},
	)
}

func (a *attendanceDifferenceReasonTester) dbSetup(
	db *gorm.DB,
	comp tbl.Company,
	emps []tbl.Employee,
	timesheets []tbl.Timesheet,
	measuredAttendances []tbl.MeasuredAttendance,
	reasons []tbl.AttendanceDifferenceReason,
	integAttDiff []tbl.IntegrationAttendanceDifference,
) {
	db.Exec("SET FOREIGN_KEY_CHECKS = 0")

	db.Create(&comp)

	for _, emp := range emps {
		db.Create(&emp)
	}

	if len(timesheets) > 0 {
		db.Create(&timesheets)
	}

	if len(measuredAttendances) > 0 {
		db.Create(&measuredAttendances)
	}

	for _, reason := range reasons {
		db.Create(&reason)
	}

	if len(integAttDiff) > 0 {
		db.Create(&integAttDiff)
	}

	db.Exec("SET FOREIGN_KEY_CHECKS = 1")
}

func (a *attendanceDifferenceReasonTester) dbAssert(
	t *testing.T,
	db *gorm.DB,
	expReasons []tbl.AttendanceDifferenceReason,
) {

	var actReasons []tbl.AttendanceDifferenceReason
	db.Find(&actReasons)
	assertmodel.AttendanceDifferenceReasonSlice(t, expReasons, actReasons)
}

func (a *attendanceDifferenceReasonTester) ExecTest(
	t *testing.T,
	compNo, userID int,
	cases []attendanceDifferenceReasonTcase,
) {
	// Setup DB
	var ctxDB idb.DB
	ctxDB.Logger = ilog.NewDBlogger(&ilog.InpmLogger{})
	cdb, err := ctxDB.ConnectDb("common_db")
	if err != nil {
		t.Fatalf("failed to connect db: %v", err)
	}
	mdb, err := ctxDB.ConnectDb("db01")
	if err != nil {
		t.Fatalf("failed to connect db: %v", err)
	}
	defer func() {
		if sql, err := mdb.DB(); err == nil {
			_ = sql.Close()
		}
		if sql, err := cdb.DB(); err == nil {
			_ = sql.Close()
		}
	}()

	// Run test cases
	for _, cs := range cases {
		t.Run(
			cs.desc,
			func(t *testing.T) {
				ht := &test.HandlerTester{}
				if cs.mrr != nil {
					ht.MRR = cs.mrr
				}

				if cs.userSetting != nil {
					ht.UserSetting = cs.userSetting
				}

				a.dbClean(mdb)
				a.dbSetup(mdb,
					cs.preCompany,
					cs.preEmployees,
					cs.preTimesheets,
					cs.preMeasuredAttendances,
					cs.preReasons,
					cs.preIntegAttDiff)

				ht.ExecHandlerTest(cdb, mdb, compNo, userID, cs.reqObj, cs.resObj, t, cs.fn)

				a.dbAssert(t, mdb, cs.expReasons)
			},
		)
	}
}

func (a *attendanceDifferenceReasonTester) genCompany(compNo int) tbl.Company {
	return tbl.Company{
		CompanyNo:               compNo,
		CompanyName:             "かいしゃ1",
		AppliedUser:             0,
		LanguageCode:            "en",
		CurrencyCode:            "JPY",
		Timezone:                "UTC",
		CountryCode:             "JP",
		LogoFile:                "abcdefg.png",
		StartMonth:              4,
		CostMax:                 1.00,
		ReportTax:               1,
		ExpenseGroup:            1,
		JobtimeType:             1,
		JobtimeProcessCount:     0,
		UseJobtimeFlag:          true,
		SalesStatus:             true,
		WorktimeStep:            15,
		WorktimeUnit:            0,
		WorktimeRangeMin:        15,
		HoursInDay:              8.00,
		HoursInMonth:            160.00,
		TimesheetUseType:        true,
		TimesheetRestType:       true,
		TimesheetProjectOrder:   0,
		TimesheetFutureFlag:     true,
		TimesheetEqualityFlag:   true,
		EndTimeCalcFlag:         0,
		ProjectStr:              "0",
		StartTime:               "09:00:00",
		EndTime:                 "18:00:00",
		Rest1StartTime:          "12:00:00",
		Rest1EndTime:            "13:00:00",
		Rest2StartTime:          "",
		Rest2EndTime:            "",
		HalfRest1StartTime:      "09:00:00",
		HalfRest1EndTime:        "13:00:00",
		HalfRest2StartTime:      "14:00:00",
		HalfRest2EndTime:        "18:00:00",
		SyncCalendarService:     0,
		CertifyService:          0,
		PasswdExpire:            0,
		PasswdAlert:             0,
		ExpenseNo:               0,
		GanttFlag:               true,
		ProjectCdFlag:           true,
		ProjectCdStr:            "",
		CompanyProjectNo:        0,
		CompanyStatus:           0,
		AttendanceManageUseType: true,
		UseHolidayCountryCode:   "",
		OrignalHolidayUseFlag:   true,
		CreateTime:              util.DateToTime("2019-11-01 02:36:30"),
		UpdateTime:              util.DateToTime("2019-11-30 17:36:30"),
		AuthCode:                "0",
		SamlFlag:                true,
		APIFlag:                 true,
		UseTimesheetApproval:    true,
	}
}

func (a *attendanceDifferenceReasonTester) genEmployee(compNo, empNo int) tbl.Employee {
	return tbl.Employee{
		EmployeeNo:              empNo,
		CompanyNo:               compNo,
		DepartmentNo:            test.NewPtr(1).(*int),
		ContractNo:              test.NewPtr(1).(*int),
		TitleNo:                 test.NewPtr(1).(*int),
		CustomerNo:              test.NewPtr(1).(*int),
		Salary:                  test.NewPtr(6250.0).(*float64),
		FirstName:               test.NewPtr("Employee").(*string),
		FamilyName:              test.NewPtr("hoge").(*string),
		Email:                   test.NewPtr("<EMAIL>").(*string),
		Password:                nil,
		MemberCode:              test.NewPtr("EmployeeCd1").(*string),
		StartDate:               test.NewPtr(util.DateToTime("2019-12-01")).(*time.Time),
		EndDate:                 test.NewPtr(util.DateToTime("2021-01-01")).(*time.Time),
		OutputCsv:               test.NewPtr(true).(*bool),
		UseCalendarFlag:         test.NewPtr(true).(*bool),
		UseSyncCalendarType:     test.NewPtr(1).(*int),
		TimesheetProjectOrder:   test.NewPtr(1).(*int),
		EndTimeCalcFlag:         test.NewPtr(0).(*int),
		EmployeeCostGroup:       test.NewPtr(1).(*int),
		DayEndTime:              test.NewPtr("05:00:00").(*string),
		UseCertifyType:          test.NewPtr(1).(*int),
		PasswdTime:              test.NewPtr(util.DateToTime("2020-01-01")).(*time.Time),
		LanguageCode:            test.NewPtr("jp").(*string),
		Timezone:                test.NewPtr("UTC").(*string),
		UseOvertimeRequestType:  test.NewPtr(1).(*int),
		VacationMinusRejectFlag: test.NewPtr(true).(*bool),
		CreateTime:              util.DateToTime("2020-01-01"),
		UpdateTime:              util.DateToTime("2020-01-31"),
		AuthAPIUse:              test.NewPtr(true).(*bool),
	}
}

func (a *attendanceDifferenceReasonTester) genTimesheet(
	emp tbl.Employee,
	count int) []tbl.Timesheet {

	var timesheets []tbl.Timesheet
	tsheetDate := util.DateToTime("2024-08-01")
	tsheetEndDate := tsheetDate.AddDate(0, 0, count)

	for i := 1; tsheetDate.Before(tsheetEndDate); i++ {
		tsheet := tbl.Timesheet{
			// TimesheetNo:               i,
			EmployeeNo:                emp.EmployeeNo,
			TimesheetDate:             tsheetDate,
			StartTime:                 test.NewPtr("08:00:00").(*string),
			StartStampTime:            test.NewPtr("08:00:00").(*string),
			EndTime:                   test.NewPtr("17:00:00").(*string),
			EndStampTime:              test.NewPtr("17:00:00").(*string),
			HasAttendance:             test.NewPtr(false).(*bool),
			Comments:                  test.NewPtr("comments").(*string),
			Status:                    5,
			HolidayType:               test.NewPtr(0).(*int),
			TmpDeleteFlag:             false,
			Salary:                    1,
			CreateTime:                tsheetDate,
			UpdateTime:                tsheetDate,
			ApprovalStatus:            test.NewPtr(0).(*int),
			AutoCalculationAttendance: test.NewPtr(false).(*bool),
		}

		timesheets = append(timesheets, tsheet)
		tsheetDate = tsheetDate.AddDate(0, 0, 1)
	}

	return timesheets
}

func (a *attendanceDifferenceReasonTester) genMeasuredAttendances(
	compNo int,
	emp tbl.Employee,
	count int) []tbl.MeasuredAttendance {

	var mAttendances []tbl.MeasuredAttendance
	attDateStart := util.DateToTime("2024-08-01 08:00:00")
	attDateEnd := util.DateToTime("2024-08-01 17:00:00")

	for i := 1; i <= count; i++ {
		mAttend := tbl.MeasuredAttendance{
			// ID:             i,
			CompanyNo:      compNo,
			EmployeeNo:     emp.EmployeeNo,
			AttendanceDate: attDateStart,
			StartTime:      attDateStart,
			EndTime:        test.NewPtr(attDateEnd).(*time.Time),
			IdleMin:        test.NewPtr(60).(*int),
			CreatedAt:      attDateStart,
			CreateUser:     i,
			UpdatedAt:      attDateStart,
			UpdateUser:     i,
		}
		mAttendances = append(mAttendances, mAttend)

		attDateStart = attDateStart.AddDate(0, 0, 1)
		attDateEnd = attDateEnd.AddDate(0, 0, 1)
	}

	return mAttendances
}

func (a *attendanceDifferenceReasonTester) genReasons(compNo, count int, emp tbl.Employee, reasonClass value.DifferenceReasonClass) []tbl.AttendanceDifferenceReason {
	attDate := util.DateToTime("2024-08-01")

	var reasons []tbl.AttendanceDifferenceReason
	for i := 1; i <= count; i++ {
		reasonText := test.NewPtr(fmt.Sprintf("reason%d", i)).(*string)
		if reasonClass != value.DifferenceReasonClassOthers && count > 1 && i == 1 {
			reasonText = test.NewPtr("").(*string)
		}
		reason := tbl.AttendanceDifferenceReason{
			ID:                 i,
			CompanyNo:          compNo,
			EmployeeNo:         emp.EmployeeNo,
			EmployeeFirstName:  *emp.FirstName,
			EmployeeFamilyName: *emp.FamilyName,
			AttendanceDate:     attDate,
			ReasonClass:        int(reasonClass),
			Reason:             reasonText,
			CreatedAt:          attDate,
			CreateUser:         i,
			UpdatedAt:          attDate,
			UpdateUser:         i,
		}
		reasons = append(reasons, reason)

		attDate = attDate.AddDate(0, 0, 1)
	}

	return reasons
}

func (a *attendanceDifferenceReasonTester) genIntegAttDiff(recID, compID, logMode, threshold_mins int) tbl.IntegrationAttendanceDifference {
	return tbl.IntegrationAttendanceDifference{
		ID:            recID,
		CompanyNo:     compID,
		LogMode:       logMode,
		ThresholdMins: threshold_mins,
		CreatedAt:     util.DateToTime("2024-08-01"),
		UpdatedAt:     util.DateToTime("2024-08-01"),
	}
}

func (a *attendanceDifferenceReasonTester) genRes(reason tbl.AttendanceDifferenceReason) res.AttendanceDifferenceReason {
	return res.AttendanceDifferenceReason{
		ID:                 reason.ID,
		CompanyNo:          reason.CompanyNo,
		EmployeeNo:         reason.EmployeeNo,
		EmployeeFirstName:  reason.EmployeeFirstName,
		EmployeeFamilyName: reason.EmployeeFamilyName,
		AttendanceDate:     reason.AttendanceDate.Format("2006-01-02"),
		ReasonClass:        reason.ReasonClass,
		Reason:             *reason.Reason,
	}
}

func Test_AttendanceDifferenceReason_FindSpecific(t *testing.T) {
	compNo := 1
	userID := 1

	h := NewAttendanceDifferenceReasonHandler()
	at := attendanceDifferenceReasonTester{}

	// CASE1
	preComp1 := at.genCompany(compNo)
	preEmp1 := at.genEmployee(compNo, userID)
	preReason1 := at.genReasons(compNo, 1, preEmp1, value.DifferenceReasonClassOthers)

	case1 := attendanceDifferenceReasonTcase{
		desc:         "CASE1: ERR: employee does not exist",
		preCompany:   preComp1,
		preEmployees: []tbl.Employee{preEmp1},
		preReasons:   preReason1,
		expReasons:   preReason1,
		reqObj: test.HandlerReq{
			Url:    "/users/999/attendances/2024-08-01/reasons",
			Method: http.MethodGet,
			Params: gin.Params{
				{Key: "user_id", Value: "999"},
				{Key: "date", Value: "2024-08-01"},
			},
		},
		resObj: test.HandlerRes{
			Sts:    http.StatusNotFound,
			Res:    imsg.CreateErrMsg(imsg.NoResource),
			ErrStr: "*ierror.noResourceErr",
		},
		fn: h.FindSpecific,
	}

	// CASE2
	preComp2 := at.genCompany(compNo)
	preEmp2 := at.genEmployee(compNo, userID)
	preReason2 := at.genReasons(compNo, 1, preEmp2, value.DifferenceReasonClassPcUsedNotForWork)

	case2 := attendanceDifferenceReasonTcase{
		desc:         "CASE2: ERR: invalid date format",
		preCompany:   preComp2,
		preEmployees: []tbl.Employee{preEmp2},
		preReasons:   preReason2,
		expReasons:   preReason2,
		reqObj: test.HandlerReq{
			Url:    "/users/1/attendances/0000/reasons",
			Method: http.MethodGet,
			Params: gin.Params{
				{Key: "user_id", Value: "1"},
				{Key: "date", Value: "0000"},
			},
		},
		resObj: test.HandlerRes{
			Sts:    http.StatusNotFound,
			Res:    imsg.CreateErrMsg(imsg.NoResource),
			ErrStr: "*ierror.noResourceErr",
		},
		fn: h.FindSpecific,
	}

	// CASE3
	preComp3 := at.genCompany(compNo)
	preEmp3 := at.genEmployee(compNo, userID)
	preReason3 := at.genReasons(compNo, 1, preEmp3, value.DifferenceReasonClassPcUsedNotForWork)
	res3 := at.genRes(preReason3[0])

	case3 := attendanceDifferenceReasonTcase{
		desc:         "CASE3: OK: one reason exists",
		preCompany:   preComp3,
		preEmployees: []tbl.Employee{preEmp3},
		preReasons:   preReason3,
		expReasons:   preReason3,
		reqObj: test.HandlerReq{
			Url:    "/users/1/attendances/2024-08-01/reasons",
			Method: http.MethodGet,
			Params: gin.Params{
				{Key: "user_id", Value: "1"},
				{Key: "date", Value: "2024-08-01"},
			},
		},
		resObj: test.HandlerRes{
			Sts:    http.StatusOK,
			Res:    res3,
			ErrStr: nil,
		},
		fn: h.FindSpecific,
	}

	// CASE4
	preComp4 := at.genCompany(compNo)
	preEmp4 := at.genEmployee(compNo, userID)
	preReason4 := at.genReasons(compNo, 2, preEmp4, value.DifferenceReasonClassPcUnnecessarily)
	res4 := at.genRes(preReason4[0])

	case4 := attendanceDifferenceReasonTcase{
		desc:         "CASE4: OK: multi reasons exist, the first one saved with an empty reason string",
		preCompany:   preComp4,
		preEmployees: []tbl.Employee{preEmp4},
		preReasons:   preReason4,
		expReasons:   preReason4,
		reqObj: test.HandlerReq{
			Url:    "/users/1/attendances/2024-08-01/reasons",
			Method: http.MethodGet,
			Params: gin.Params{
				{Key: "user_id", Value: "1"},
				{Key: "date", Value: "2024-08-01"},
			},
		},
		resObj: test.HandlerRes{
			Sts:    http.StatusOK,
			Res:    res4,
			ErrStr: nil,
		},
		fn: h.FindSpecific,
	}

	// CASE5
	// internal server error
	preComp5 := at.genCompany(compNo)
	preEmp5 := at.genEmployee(compNo, userID)
	preReason5 := at.genReasons(compNo, 1, preEmp5, value.DifferenceReasonClassPcLogUnreadable)

	mockCtrl5 := gomock.NewController(t)
	defer mockCtrl5.Finish()
	mockObj5 := mock_repo.NewMockAttendanceDifferenceReason(mockCtrl5)
	mockObj5.
		EXPECT().
		FindSpecific(gomock.Any(), gomock.Any(), gomock.Any()).
		Return(model.AttendanceDifferenceReason{}, errors.New("AAA"))
	mrr5 := &test.MockRepoRegistry{}
	mrr5.MAttDiffReason = mockObj5

	case5 := attendanceDifferenceReasonTcase{
		desc:         "CASE5: ERR: internal server error",
		preCompany:   preComp5,
		preEmployees: []tbl.Employee{preEmp5},
		preReasons:   preReason5,
		expReasons:   preReason5,
		reqObj: test.HandlerReq{
			Url:    "/users/1/attendances/2024-08-01/reasons",
			Method: http.MethodGet,
			Params: gin.Params{
				{Key: "user_id", Value: "1"},
				{Key: "date", Value: "2024-08-01"},
			},
		},
		resObj: test.HandlerRes{
			Sts:    http.StatusInternalServerError,
			Res:    imsg.CreateErrMsg(imsg.ServerError),
			ErrStr: "*errors.errorString",
		},
		fn:  h.FindSpecific,
		mrr: mrr5,
	}

	// CASE6
	preComp6 := at.genCompany(compNo)
	preEmp6 := at.genEmployee(compNo, 2)
	preReason6 := at.genReasons(compNo, 1, preEmp6, value.DifferenceReasonClassPcOffOmission)
	res6 := at.genRes(preReason6[0])

	userSetting6 := test.CreateDefaultUserInfo()
	case6 := attendanceDifferenceReasonTcase{
		desc:         "CASE6: OK: system admin user can see other's difference reason",
		preCompany:   preComp6,
		preEmployees: []tbl.Employee{preEmp6},
		preReasons:   preReason6,
		expReasons:   preReason6,
		reqObj: test.HandlerReq{
			Url:    "/users/2/attendances/2024-08-01/reasons",
			Method: http.MethodGet,
			Params: gin.Params{
				{Key: "user_id", Value: "2"},
				{Key: "date", Value: "2024-08-01"},
			},
		},
		resObj: test.HandlerRes{
			Sts:    http.StatusOK,
			Res:    res6,
			ErrStr: nil,
		},
		fn:          h.FindSpecific,
		userSetting: &userSetting6,
	}

	// CASE7
	preComp7 := at.genCompany(compNo)
	preEmp7 := at.genEmployee(compNo, 2)
	preReason7 := at.genReasons(compNo, 1, preEmp7, value.DifferenceReasonClassOthers)

	userSetting7 := test.CreateDefaultUserInfo()
	userSetting7.SysAdminFlag = false
	case7 := attendanceDifferenceReasonTcase{
		desc:         "CASE7: ERR: regular member cannot access other's difference reason",
		preCompany:   preComp7,
		preEmployees: []tbl.Employee{preEmp7},
		preReasons:   preReason7,
		expReasons:   preReason7,
		reqObj: test.HandlerReq{
			Url:    "/users/2/attendances/2024-08-01/reasons",
			Method: http.MethodGet,
			Params: gin.Params{
				{Key: "user_id", Value: "2"},
				{Key: "date", Value: "2024-08-01"},
			},
		},
		resObj: test.HandlerRes{
			Sts:    http.StatusForbidden,
			Res:    imsg.CreatePermErrMsg(config.DefaultLanguage, imsg.PermForbidden, imsg.PermInsufficientSystemAdmin),
			ErrStr: "*ierror.permissionErr",
		},
		fn:          h.FindSpecific,
		userSetting: &userSetting7,
	}

	// CASE8
	preComp8 := at.genCompany(compNo)
	preEmp8 := at.genEmployee(compNo, 1)
	preReason8 := at.genReasons(compNo, 1, preEmp8, value.DifferenceReasonClassPcUsedNotForWork)
	res8 := at.genRes(preReason8[0])

	userSetting8 := test.CreateDefaultUserInfo()
	userSetting8.SysAdminFlag = false
	case8 := attendanceDifferenceReasonTcase{
		desc:         "CASE8: OK: regular member can see his own difference reason",
		preCompany:   preComp8,
		preEmployees: []tbl.Employee{preEmp8},
		preReasons:   preReason8,
		expReasons:   preReason8,
		reqObj: test.HandlerReq{
			Url:    "/users/1/attendances/2024-08-01/reasons",
			Method: http.MethodGet,
			Params: gin.Params{
				{Key: "user_id", Value: "1"},
				{Key: "date", Value: "2024-08-01"},
			},
		},
		resObj: test.HandlerRes{
			Sts:    http.StatusOK,
			Res:    res8,
			ErrStr: nil,
		},
		fn:          h.FindSpecific,
		userSetting: &userSetting8,
	}

	cases := []attendanceDifferenceReasonTcase{
		case1, case2, case3, case4, case5, case6, case7, case8,
	}

	at.ExecTest(t, compNo, userID, cases)
}

func Test_AttendanceDifferenceReason_Create(t *testing.T) {
	compNo := 1
	userID := 1

	h := NewAttendanceDifferenceReasonHandler()
	at := attendanceDifferenceReasonTester{}

	// CASE1
	// ERR: reason already exists (has difference)
	preComp1 := at.genCompany(compNo)
	preEmp1 := at.genEmployee(compNo, userID)
	preTimesheets1 := at.genTimesheet(preEmp1, 1)
	preMeasuredAttendances1 := at.genMeasuredAttendances(compNo, preEmp1, 1)
	preMeasuredAttendances1[0].StartTime = preMeasuredAttendances1[0].StartTime.Add(time.Minute * 30)
	preReason1 := at.genReasons(compNo, 1, preEmp1, value.DifferenceReasonClassPcUsedNotForWork)
	preIntegAttDiff1 := at.genIntegAttDiff(1, compNo, int(value.TKLogPCMode), 30)

	case1 := attendanceDifferenceReasonTcase{
		desc:                   "CASE1: ERR: reason already exists (has difference)",
		preCompany:             preComp1,
		preEmployees:           []tbl.Employee{preEmp1},
		preTimesheets:          preTimesheets1,
		preMeasuredAttendances: preMeasuredAttendances1,
		preReasons:             preReason1,
		preIntegAttDiff:        []tbl.IntegrationAttendanceDifference{preIntegAttDiff1},
		expReasons:             preReason1,
		reqObj: test.HandlerReq{
			Url:    "/users/1/attendances/2024-08-01/reasons",
			Method: http.MethodPost,
			Params: gin.Params{
				{Key: "user_id", Value: "1"},
				{Key: "date", Value: "2024-08-01"},
			},
			Body: gin.H{
				"reason_class": 1,
				"reason":       "reason1",
			},
		},
		resObj: test.HandlerRes{
			Sts:    http.StatusBadRequest,
			Res:    imsg.CreateErrMsg(imsg.AlreadyExist, imsg.WordAttendanceDifferenceReason),
			ErrStr: "*ierror.consistencyErr",
		},
		fn: h.Create,
	}

	// CASE2
	// ERR: employee does not exist
	preComp2 := at.genCompany(compNo)
	preEmp2 := at.genEmployee(compNo, userID)
	preTimesheets2 := at.genTimesheet(preEmp2, 1)
	preMeasuredAttendances2 := at.genMeasuredAttendances(compNo, preEmp2, 1)
	preMeasuredAttendances2[0].StartTime = preMeasuredAttendances2[0].StartTime.Add(time.Minute * 30)
	preIntegAttDiff2 := at.genIntegAttDiff(1, compNo, int(value.TKLogPCMode), 30)

	case2 := attendanceDifferenceReasonTcase{
		desc:                   "CASE2: ERR: employee does not exist",
		preCompany:             preComp2,
		preEmployees:           []tbl.Employee{preEmp2},
		preTimesheets:          preTimesheets2,
		preMeasuredAttendances: preMeasuredAttendances2,
		preIntegAttDiff:        []tbl.IntegrationAttendanceDifference{preIntegAttDiff2},
		reqObj: test.HandlerReq{
			Url:    "/users/999/attendances/2024-08-01/reasons",
			Method: http.MethodPost,
			Params: gin.Params{
				{Key: "user_id", Value: "999"},
				{Key: "date", Value: "2024-08-01"},
			},
			Body: gin.H{
				"reason_class": 1,
				"reason":       "reason1",
			},
		},
		resObj: test.HandlerRes{
			Sts:    http.StatusNotFound,
			Res:    imsg.CreateErrMsg(imsg.NoResource),
			ErrStr: "*ierror.noResourceErr",
		},
		fn: h.Create,
	}

	// CASE3
	// ERR: invalid date format
	preComp3 := at.genCompany(compNo)
	preEmp3 := at.genEmployee(compNo, userID)
	preTimesheets3 := at.genTimesheet(preEmp3, 1)
	preMeasuredAttendances3 := at.genMeasuredAttendances(compNo, preEmp3, 1)
	preMeasuredAttendances3[0].StartTime = preMeasuredAttendances3[0].StartTime.Add(time.Minute * 30)
	preIntegAttDiff3 := at.genIntegAttDiff(1, compNo, int(value.TKLogPCMode), 30)

	case3 := attendanceDifferenceReasonTcase{
		desc:                   "CASE3: ERR: invalid date format",
		preCompany:             preComp3,
		preEmployees:           []tbl.Employee{preEmp3},
		preTimesheets:          preTimesheets3,
		preMeasuredAttendances: preMeasuredAttendances3,
		preIntegAttDiff:        []tbl.IntegrationAttendanceDifference{preIntegAttDiff3},
		reqObj: test.HandlerReq{
			Url:    "/users/1/attendances/0000/reasons",
			Method: http.MethodPost,
			Params: gin.Params{
				{Key: "user_id", Value: "1"},
				{Key: "date", Value: "0000"},
			},
			Body: gin.H{
				"reason_class": 1,
				"reason":       "reason1",
			},
		},
		resObj: test.HandlerRes{
			Sts:    http.StatusBadRequest,
			Res:    imsg.CreateErrMsg(imsg.NoDifference, imsg.WordAttendance, imsg.WordPCLog),
			ErrStr: "*ierror.consistencyErr",
		},
		fn: h.Create,
	}

	// CASE4
	// ERR: length of reason is too long
	preComp4 := at.genCompany(compNo)
	preEmp4 := at.genEmployee(compNo, userID)
	preTimesheets4 := at.genTimesheet(preEmp4, 1)
	preMeasuredAttendances4 := at.genMeasuredAttendances(compNo, preEmp4, 1)
	preMeasuredAttendances4[0].StartTime = preMeasuredAttendances4[0].StartTime.Add(time.Minute * 30)
	reasonString4 := strings.Repeat("a", 257)
	preIntegAttDiff4 := at.genIntegAttDiff(1, compNo, int(value.TKLogPCMode), 30)

	case4 := attendanceDifferenceReasonTcase{
		desc:            "CASE4: ERR: length of reason is too long",
		preCompany:      preComp4,
		preEmployees:    []tbl.Employee{preEmp4},
		preTimesheets:   preTimesheets4,
		preIntegAttDiff: []tbl.IntegrationAttendanceDifference{preIntegAttDiff4},
		reqObj: test.HandlerReq{
			Url:    "/users/1/attendances/2024-08-01/reasons",
			Method: http.MethodPost,
			Params: gin.Params{
				{Key: "user_id", Value: "1"},
				{Key: "date", Value: "2024-08-01"},
			},
			Body: gin.H{
				"reason_class": 1,
				"reason":       reasonString4,
			},
		},
		resObj: test.HandlerRes{
			Sts:    http.StatusBadRequest,
			Res:    "reason must be a maximum of 256 characters in length",
			ErrStr: "validator.ValidationErrors",
		},
		fn: h.Create,
	}

	// CASE5
	// OK: Successfully created
	preComp5 := at.genCompany(compNo)
	preEmp5 := at.genEmployee(compNo, userID)
	preTimesheets5 := at.genTimesheet(preEmp5, 1)
	preMeasuredAttendances5 := at.genMeasuredAttendances(compNo, preEmp5, 1)
	preMeasuredAttendances5[0].StartTime = preMeasuredAttendances5[0].StartTime.Add(time.Minute * 30)
	preReason5 := at.genReasons(compNo, 1, preEmp5, value.DifferenceReasonClassPcUnnecessarily)
	preIntegAttDiff5 := at.genIntegAttDiff(1, compNo, int(value.TKLogPCMode), 30)

	case5 := attendanceDifferenceReasonTcase{
		desc:                   "CASE5: OK: Successfully created",
		preCompany:             preComp5,
		preEmployees:           []tbl.Employee{preEmp5},
		preTimesheets:          preTimesheets5,
		preMeasuredAttendances: preMeasuredAttendances5,
		expReasons:             preReason5,
		preIntegAttDiff:        []tbl.IntegrationAttendanceDifference{preIntegAttDiff5},
		reqObj: test.HandlerReq{
			Url:    "/users/1/attendances/2024-08-01/reasons",
			Method: http.MethodPost,
			Params: gin.Params{
				{Key: "user_id", Value: "1"},
				{Key: "date", Value: "2024-08-01"},
			},
			Body: gin.H{
				"reason_class": 2,
				"reason":       "reason1",
			},
		},
		resObj: test.HandlerRes{
			Sts:    http.StatusOK,
			Res:    nil,
			ErrStr: nil,
		},
		fn: h.Create,
	}

	// CASE6
	// ERR: Create() difference reason error
	preComp6 := at.genCompany(compNo)
	preEmp6 := at.genEmployee(compNo, userID)
	preTimesheets6 := at.genTimesheet(preEmp6, 1)
	preMeasuredAttendances6 := at.genMeasuredAttendances(compNo, preEmp6, 1)
	preMeasuredAttendances6[0].StartTime = preMeasuredAttendances6[0].StartTime.Add(time.Minute * 30)
	preIntegAttDiff6 := at.genIntegAttDiff(1, compNo, int(value.TKLogPCMode), 30)

	mockCtrl6 := gomock.NewController(t)
	defer mockCtrl6.Finish()
	mockObj6 := mock_repo.NewMockAttendanceDifferenceReason(mockCtrl6)
	mockObj6.EXPECT().
		FindSpecific(gomock.Any(), gomock.Any(), gomock.Any()).
		Return(model.AttendanceDifferenceReason{}, nil)
	mockObj6.EXPECT().
		Create(gomock.Any(), gomock.Any()).
		Return(0, errors.New("AAA"))
	mrr6 := &test.MockRepoRegistry{}
	mrr6.MAttDiffReason = mockObj6

	case6 := attendanceDifferenceReasonTcase{
		desc:                   "CASE6: ERR: Create() difference reason error",
		preCompany:             preComp6,
		preEmployees:           []tbl.Employee{preEmp6},
		preTimesheets:          preTimesheets6,
		preMeasuredAttendances: preMeasuredAttendances6,
		preIntegAttDiff:        []tbl.IntegrationAttendanceDifference{preIntegAttDiff6},
		reqObj: test.HandlerReq{
			Url:    "/users/1/attendances/2024-08-01/reasons",
			Method: http.MethodPost,
			Params: gin.Params{
				{Key: "user_id", Value: "1"},
				{Key: "date", Value: "2024-08-01"},
			},
			Body: gin.H{
				"reason_class": 1,
				"reason":       "reason1",
			},
		},
		resObj: test.HandlerRes{
			Sts:    http.StatusInternalServerError,
			Res:    imsg.CreateErrMsg(imsg.ServerError),
			ErrStr: "*errors.errorString",
		},
		fn:  h.Create,
		mrr: mrr6,
	}

	// CASE7
	// OK: max number of strings
	preComp7 := at.genCompany(compNo)
	preEmp7 := at.genEmployee(compNo, userID)
	preTimesheets7 := at.genTimesheet(preEmp7, 1)
	preMeasuredAttendances7 := at.genMeasuredAttendances(compNo, preEmp7, 1)
	preMeasuredAttendances7[0].StartTime = preMeasuredAttendances7[0].StartTime.Add(time.Minute * 30)
	preReason7 := at.genReasons(compNo, 1, preEmp7, value.DifferenceReasonClassPcLogUnreadable)
	reason7 := strings.Repeat("a", 256)
	preReason7[0].Reason = &reason7
	preIntegAttDiff7 := at.genIntegAttDiff(1, compNo, int(value.TKLogPCMode), 30)

	case7 := attendanceDifferenceReasonTcase{
		desc:                   "CASE7: OK: max number of strings",
		preCompany:             preComp7,
		preEmployees:           []tbl.Employee{preEmp7},
		preTimesheets:          preTimesheets7,
		preMeasuredAttendances: preMeasuredAttendances7,
		expReasons:             preReason7,
		preIntegAttDiff:        []tbl.IntegrationAttendanceDifference{preIntegAttDiff7},
		reqObj: test.HandlerReq{
			Url:    "/users/1/attendances/2024-08-01/reasons",
			Method: http.MethodPost,
			Params: gin.Params{
				{Key: "user_id", Value: "1"},
				{Key: "date", Value: "2024-08-01"},
			},
			Body: gin.H{
				"reason_class": 3,
				"reason":       reason7,
			},
		},
		resObj: test.HandlerRes{
			Sts:    http.StatusOK,
			Res:    nil,
			ErrStr: nil,
		},
		fn: h.Create,
	}

	// CASE8
	// ERR: No difference
	preComp8 := at.genCompany(compNo)
	preEmp8 := at.genEmployee(compNo, userID)
	preTimesheets8 := at.genTimesheet(preEmp8, 1)
	preMeasuredAttendances8 := at.genMeasuredAttendances(compNo, preEmp8, 1)
	preMeasuredAttendances8[0].StartTime = preMeasuredAttendances8[0].StartTime.Add(time.Minute * 29)
	preReason8 := at.genReasons(compNo, 1, preEmp8, value.DifferenceReasonClassPcOffOmission)
	reason8 := "reason8"
	preReason8[0].Reason = &reason8
	preIntegAttDiff8 := at.genIntegAttDiff(1, compNo, int(value.TKLogPCMode), 30)

	case8 := attendanceDifferenceReasonTcase{
		desc:                   "CASE8: ERR: No difference",
		preCompany:             preComp8,
		preEmployees:           []tbl.Employee{preEmp8},
		preTimesheets:          preTimesheets8,
		preMeasuredAttendances: preMeasuredAttendances8,
		preIntegAttDiff:        []tbl.IntegrationAttendanceDifference{preIntegAttDiff8},
		reqObj: test.HandlerReq{
			Url:    "/users/1/attendances/2024-08-01/reasons",
			Method: http.MethodPost,
			Params: gin.Params{
				{Key: "user_id", Value: "1"},
				{Key: "date", Value: "2024-08-01"},
			},
			Body: gin.H{
				"reason_class": 4,
				"reason":       reason8,
			},
		},
		resObj: test.HandlerRes{
			Sts:    http.StatusBadRequest,
			Res:    "There is no differece between Attendance and PCLog",
			ErrStr: "*ierror.consistencyErr",
		},
		fn: h.Create,
	}

	// CASE9
	// OK: 1 length of reason
	preComp9 := at.genCompany(compNo)
	preEmp9 := at.genEmployee(compNo, userID)
	preTimesheets9 := at.genTimesheet(preEmp9, 1)
	preMeasuredAttendances9 := at.genMeasuredAttendances(compNo, preEmp9, 1)
	preMeasuredAttendances9[0].StartTime = preMeasuredAttendances9[0].StartTime.Add(time.Minute * 30)
	preReason9 := at.genReasons(compNo, 1, preEmp9, value.DifferenceReasonClassOthers)
	reason9 := "a"
	preReason9[0].Reason = &reason9
	preIntegAttDiff9 := at.genIntegAttDiff(1, compNo, int(value.TKLogPCMode), 30)

	case9 := attendanceDifferenceReasonTcase{
		desc:                   "CASE9: OK: 1 length of reason",
		preCompany:             preComp9,
		preEmployees:           []tbl.Employee{preEmp9},
		preTimesheets:          preTimesheets9,
		preMeasuredAttendances: preMeasuredAttendances9,
		expReasons:             preReason9,
		preIntegAttDiff:        []tbl.IntegrationAttendanceDifference{preIntegAttDiff9},
		reqObj: test.HandlerReq{
			Url:    "/users/1/attendances/2024-08-01/reasons",
			Method: http.MethodPost,
			Params: gin.Params{
				{Key: "user_id", Value: "1"},
				{Key: "date", Value: "2024-08-01"},
			},
			Body: gin.H{
				"reason_class": 0,
				"reason":       reason9,
			},
		},
		resObj: test.HandlerRes{
			Sts:    http.StatusOK,
			Res:    nil,
			ErrStr: nil,
		},
		fn: h.Create,
	}

	// CASE10
	// ERR: Empty reason
	preComp10 := at.genCompany(compNo)
	preEmp10 := at.genEmployee(compNo, userID)
	preTimesheets10 := at.genTimesheet(preEmp10, 1)
	preMeasuredAttendances10 := at.genMeasuredAttendances(compNo, preEmp10, 1)
	preMeasuredAttendances10[0].StartTime = preMeasuredAttendances10[0].StartTime.Add(time.Minute * 30)
	preIntegAttDiff10 := at.genIntegAttDiff(1, compNo, int(value.TKLogPCMode), 30)

	case10 := attendanceDifferenceReasonTcase{
		desc:                   "CASE10: ERR: Empty reason",
		preCompany:             preComp10,
		preEmployees:           []tbl.Employee{preEmp10},
		preTimesheets:          preTimesheets10,
		preMeasuredAttendances: preMeasuredAttendances10,
		preIntegAttDiff:        []tbl.IntegrationAttendanceDifference{preIntegAttDiff10},
		reqObj: test.HandlerReq{
			Url:    "/users/1/attendances/2024-08-01/reasons",
			Method: http.MethodPost,
			Params: gin.Params{
				{Key: "user_id", Value: "1"},
				{Key: "date", Value: "2024-08-01"},
			},
			Body: gin.H{"reason_class": 0},
		},
		resObj: test.HandlerRes{
			Sts:    http.StatusBadRequest,
			Res:    "reason is a required field",
			ErrStr: "validator.ValidationErrors",
		},
		fn: h.Create,
	}

	// CASE11
	// ERR: FindOne() integration attendance difference error
	preComp11 := at.genCompany(compNo)
	preEmp11 := at.genEmployee(compNo, userID)
	preTimesheets11 := at.genTimesheet(preEmp11, 1)
	preMeasuredAttendances11 := at.genMeasuredAttendances(compNo, preEmp11, 1)
	preMeasuredAttendances11[0].StartTime = preMeasuredAttendances11[0].StartTime.Add(time.Minute * 30)
	preIntegAttDiff11 := at.genIntegAttDiff(1, compNo, int(value.TKLogPCMode), 30)
	reason11 := strings.Repeat("a", 10)

	mockCtrl11 := gomock.NewController(t)
	defer mockCtrl11.Finish()
	mockObj11 := mock_repo.NewMockIntegrationAttendanceDifference(mockCtrl11)
	mockObj11.EXPECT().
		FindOne(compNo).Return(model.IntegrationAttendanceDifference{}, errors.New("AAA"))
	mrr11 := &test.MockRepoRegistry{}
	mrr11.MIntegAttDiff = mockObj11

	case11 := attendanceDifferenceReasonTcase{
		desc:                   "CASE11: ERR: FindOne() integration attendance difference error",
		preCompany:             preComp11,
		preEmployees:           []tbl.Employee{preEmp11},
		preTimesheets:          preTimesheets11,
		preMeasuredAttendances: preMeasuredAttendances11,
		preIntegAttDiff:        []tbl.IntegrationAttendanceDifference{preIntegAttDiff11},
		reqObj: test.HandlerReq{
			Url:    "/users/1/attendances/2024-08-01/reasons",
			Method: http.MethodPost,
			Params: gin.Params{
				{Key: "user_id", Value: "1"},
				{Key: "date", Value: "2024-08-01"},
			},
			Body: gin.H{
				"reason_class": 1,
				"reason":       reason11,
			},
		},
		resObj: test.HandlerRes{
			Sts:    http.StatusInternalServerError,
			Res:    "internal server error",
			ErrStr: "*errors.errorString",
		},
		fn:  h.Create,
		mrr: mrr11,
	}

	// CASE12
	// ERR: Find() integration difference error
	preComp12 := at.genCompany(compNo)
	preEmp12 := at.genEmployee(compNo, userID)
	preTimesheets12 := at.genTimesheet(preEmp12, 1)
	preMeasuredAttendances12 := at.genMeasuredAttendances(compNo, preEmp12, 1)
	preMeasuredAttendances12[0].StartTime = preMeasuredAttendances12[0].StartTime.Add(time.Minute * 30)
	preIntegAttDiff12 := at.genIntegAttDiff(1, compNo, int(value.TKLogPCMode), 30)
	reason12 := strings.Repeat("a", 10)

	mockCtrl12 := gomock.NewController(t)
	defer mockCtrl12.Finish()
	mockObj12 := mock_repo.NewMockAttendanceDifference(mockCtrl12)
	mockObj12.EXPECT().
		Find(compNo, gomock.Any(), gomock.Any()).Return([]model.AttendanceDifference{}, 0, errors.New("AAA"))
	mrr12 := &test.MockRepoRegistry{}
	mrr12.MAttDiff = mockObj12

	case12 := attendanceDifferenceReasonTcase{
		desc:                   "CASE12: ERR: Find() integration difference error",
		preCompany:             preComp12,
		preEmployees:           []tbl.Employee{preEmp12},
		preTimesheets:          preTimesheets12,
		preMeasuredAttendances: preMeasuredAttendances12,
		preIntegAttDiff:        []tbl.IntegrationAttendanceDifference{preIntegAttDiff12},
		reqObj: test.HandlerReq{
			Url:    "/users/1/attendances/2024-08-01/reasons",
			Method: http.MethodPost,
			Params: gin.Params{
				{Key: "user_id", Value: "1"},
				{Key: "date", Value: "2024-08-01"},
			},
			Body: gin.H{
				"reason_class": 1,
				"reason":       reason12,
			},
		},
		resObj: test.HandlerRes{
			Sts:    http.StatusInternalServerError,
			Res:    "internal server error",
			ErrStr: "*errors.errorString",
		},
		fn:  h.Create,
		mrr: mrr12,
	}

	// CASE13
	// ERR: No attendance and PCLog
	preComp13 := at.genCompany(compNo)
	preEmp13 := at.genEmployee(compNo, userID)
	reason13 := "aaaa"
	preIntegAttDiff13 := at.genIntegAttDiff(1, compNo, int(value.TKLogPCMode), 30)

	case13 := attendanceDifferenceReasonTcase{
		desc:            "CASE13: ERR: No attendance and PCLog",
		preCompany:      preComp13,
		preEmployees:    []tbl.Employee{preEmp13},
		preIntegAttDiff: []tbl.IntegrationAttendanceDifference{preIntegAttDiff13},
		reqObj: test.HandlerReq{
			Url:    "/users/1/attendances/2024-08-01/reasons",
			Method: http.MethodPost,
			Params: gin.Params{
				{Key: "user_id", Value: "1"},
				{Key: "date", Value: "2024-08-01"},
			},
			Body: gin.H{
				"reason_class": 1,
				"reason":       reason13,
			},
		},
		resObj: test.HandlerRes{
			Sts:    http.StatusBadRequest,
			Res:    imsg.CreateErrMsg(imsg.NoDifference, imsg.WordAttendance, imsg.WordPCLog),
			ErrStr: "*ierror.consistencyErr",
		},
		fn: h.Create,
	}

	// CASE14
	// OK: No Attendance (PCLog exists)
	preComp14 := at.genCompany(compNo)
	preEmp14 := at.genEmployee(compNo, userID)
	preMeasuredAttendances14 := at.genMeasuredAttendances(compNo, preEmp14, 1)
	preMeasuredAttendances14[0].StartTime = preMeasuredAttendances14[0].StartTime.Add(time.Minute * 30)
	preReason14 := at.genReasons(compNo, 1, preEmp14, value.DifferenceReasonClassPcUsedNotForWork)
	reason14 := "aaaa"
	preReason14[0].Reason = &reason14
	preIntegAttDiff14 := at.genIntegAttDiff(1, compNo, int(value.TKLogPCMode), 30)

	case14 := attendanceDifferenceReasonTcase{
		desc:                   "CASE14: OK: No Attendance (PCLog exists)",
		preCompany:             preComp14,
		preEmployees:           []tbl.Employee{preEmp14},
		preMeasuredAttendances: preMeasuredAttendances14,
		preIntegAttDiff:        []tbl.IntegrationAttendanceDifference{preIntegAttDiff14},
		expReasons:             preReason14,
		reqObj: test.HandlerReq{
			Url:    "/users/1/attendances/2024-08-01/reasons",
			Method: http.MethodPost,
			Params: gin.Params{
				{Key: "user_id", Value: "1"},
				{Key: "date", Value: "2024-08-01"},
			},
			Body: gin.H{
				"reason_class": 1,
				"reason":       reason14,
			},
		},
		resObj: test.HandlerRes{
			Sts:    http.StatusOK,
			Res:    nil,
			ErrStr: nil,
		},
		fn: h.Create,
	}

	// CASE15
	// OK: No PCLog (Attendance exists)
	preComp15 := at.genCompany(compNo)
	preEmp15 := at.genEmployee(compNo, userID)
	preTimesheets15 := at.genTimesheet(preEmp15, 1)
	preReason15 := at.genReasons(compNo, 1, preEmp15, value.DifferenceReasonClassPcUsedNotForWork)
	reason15 := "aaaa"
	preReason15[0].Reason = &reason14
	preIntegAttDiff15 := at.genIntegAttDiff(1, compNo, int(value.TKLogPCMode), 30)

	case15 := attendanceDifferenceReasonTcase{
		desc:            "CASE15: OK: No PCLog (Attendance exists)",
		preCompany:      preComp15,
		preEmployees:    []tbl.Employee{preEmp15},
		preTimesheets:   preTimesheets15,
		preIntegAttDiff: []tbl.IntegrationAttendanceDifference{preIntegAttDiff15},
		expReasons:      preReason15,
		reqObj: test.HandlerReq{
			Url:    "/users/1/attendances/2024-08-01/reasons",
			Method: http.MethodPost,
			Params: gin.Params{
				{Key: "user_id", Value: "1"},
				{Key: "date", Value: "2024-08-01"},
			},
			Body: gin.H{
				"reason_class": 1,
				"reason":       reason15,
			},
		},
		resObj: test.HandlerRes{
			Sts:    http.StatusOK,
			Res:    nil,
			ErrStr: nil,
		},
		fn: h.Create,
	}

	// CASE16
	// OK: System admin user has created other's reason successfully
	preComp16 := at.genCompany(compNo)
	preEmp16 := at.genEmployee(compNo, 2)
	preTimesheets16 := at.genTimesheet(preEmp16, 1)
	preMeasuredAttendances16 := at.genMeasuredAttendances(compNo, preEmp16, 1)
	preMeasuredAttendances16[0].StartTime = preMeasuredAttendances16[0].StartTime.Add(time.Minute * 30)
	preReason16 := at.genReasons(compNo, 1, preEmp16, value.DifferenceReasonClassPcUnnecessarily)
	preIntegAttDiff16 := at.genIntegAttDiff(1, compNo, int(value.TKLogPCMode), 30)

	userSetting16 := test.CreateDefaultUserInfo()
	case16 := attendanceDifferenceReasonTcase{
		desc:                   "CASE16: OK: System admin user has created other's reason successfully",
		preCompany:             preComp16,
		preEmployees:           []tbl.Employee{preEmp16},
		preTimesheets:          preTimesheets16,
		preMeasuredAttendances: preMeasuredAttendances16,
		expReasons:             preReason16,
		preIntegAttDiff:        []tbl.IntegrationAttendanceDifference{preIntegAttDiff16},
		reqObj: test.HandlerReq{
			Url:    "/users/2/attendances/2024-08-01/reasons",
			Method: http.MethodPost,
			Params: gin.Params{
				{Key: "user_id", Value: "2"},
				{Key: "date", Value: "2024-08-01"},
			},
			Body: gin.H{
				"reason_class": 2,
				"reason":       "reason1",
			},
		},
		resObj: test.HandlerRes{
			Sts:    http.StatusOK,
			Res:    nil,
			ErrStr: nil,
		},
		fn:          h.Create,
		userSetting: &userSetting16,
	}

	// CASE17
	// ERR: Regular member cannot create other's reason
	preComp17 := at.genCompany(compNo)
	preEmp17 := at.genEmployee(compNo, 2)
	preTimesheets17 := at.genTimesheet(preEmp17, 1)
	preMeasuredAttendances17 := at.genMeasuredAttendances(compNo, preEmp17, 1)
	preMeasuredAttendances17[0].StartTime = preMeasuredAttendances17[0].StartTime.Add(time.Minute * 30)
	preIntegAttDiff17 := at.genIntegAttDiff(1, compNo, int(value.TKLogPCMode), 30)

	userSetting17 := test.CreateDefaultUserInfo()
	userSetting17.SysAdminFlag = false
	case17 := attendanceDifferenceReasonTcase{
		desc:                   "CASE17: ERR: Regular member cannot create other's reason",
		preCompany:             preComp17,
		preEmployees:           []tbl.Employee{preEmp17},
		preTimesheets:          preTimesheets17,
		preMeasuredAttendances: preMeasuredAttendances17,
		expReasons:             []tbl.AttendanceDifferenceReason{},
		preIntegAttDiff:        []tbl.IntegrationAttendanceDifference{preIntegAttDiff17},
		reqObj: test.HandlerReq{
			Url:    "/users/2/attendances/2024-08-01/reasons",
			Method: http.MethodPost,
			Params: gin.Params{
				{Key: "user_id", Value: "2"},
				{Key: "date", Value: "2024-08-01"},
			},
			Body: gin.H{
				"reason_class": 1,
				"reason":       "reason1",
			},
		},
		resObj: test.HandlerRes{
			Sts:    http.StatusForbidden,
			Res:    imsg.CreatePermErrMsg(config.DefaultLanguage, imsg.PermForbidden, imsg.PermInsufficientSystemAdmin),
			ErrStr: "*ierror.permissionErr",
		},
		fn:          h.Create,
		userSetting: &userSetting17,
	}

	// CASE18
	// OK: Regular member has created his own reason successfully
	preComp18 := at.genCompany(compNo)
	preEmp18 := at.genEmployee(compNo, 1)
	preTimesheets18 := at.genTimesheet(preEmp18, 1)
	preMeasuredAttendances18 := at.genMeasuredAttendances(compNo, preEmp18, 1)
	preMeasuredAttendances18[0].StartTime = preMeasuredAttendances18[0].StartTime.Add(time.Minute * 30)
	preReason18 := at.genReasons(compNo, 1, preEmp18, value.DifferenceReasonClassPcLogUnreadable)
	preIntegAttDiff18 := at.genIntegAttDiff(1, compNo, int(value.TKLogPCMode), 30)

	userSetting18 := test.CreateDefaultUserInfo()
	userSetting18.SysAdminFlag = false
	case18 := attendanceDifferenceReasonTcase{
		desc:                   "CASE18: OK: Regular member has created his own reason successfully",
		preCompany:             preComp18,
		preEmployees:           []tbl.Employee{preEmp18},
		preTimesheets:          preTimesheets18,
		preMeasuredAttendances: preMeasuredAttendances18,
		expReasons:             preReason18,
		preIntegAttDiff:        []tbl.IntegrationAttendanceDifference{preIntegAttDiff18},
		reqObj: test.HandlerReq{
			Url:    "/users/1/attendances/2024-08-01/reasons",
			Method: http.MethodPost,
			Params: gin.Params{
				{Key: "user_id", Value: "1"},
				{Key: "date", Value: "2024-08-01"},
			},
			Body: gin.H{
				"reason_class": 3,
				"reason":       "reason1",
			},
		},
		resObj: test.HandlerRes{
			Sts:    http.StatusOK,
			Res:    nil,
			ErrStr: nil,
		},
		fn:          h.Create,
		userSetting: &userSetting18,
	}

	// CASE19
	// ERR: Empty reason class
	preComp19 := at.genCompany(compNo)
	preEmp19 := at.genEmployee(compNo, userID)
	preTimesheets19 := at.genTimesheet(preEmp19, 1)
	preMeasuredAttendances19 := at.genMeasuredAttendances(compNo, preEmp19, 1)
	preMeasuredAttendances19[0].StartTime = preMeasuredAttendances19[0].StartTime.Add(time.Minute * 30)
	preIntegAttDiff19 := at.genIntegAttDiff(1, compNo, int(value.TKLogPCMode), 30)

	case19 := attendanceDifferenceReasonTcase{
		desc:                   "CASE19: ERR: Empty reason class",
		preCompany:             preComp19,
		preEmployees:           []tbl.Employee{preEmp19},
		preTimesheets:          preTimesheets19,
		preMeasuredAttendances: preMeasuredAttendances19,
		preIntegAttDiff:        []tbl.IntegrationAttendanceDifference{preIntegAttDiff19},
		reqObj: test.HandlerReq{
			Url:    "/users/1/attendances/2024-08-01/reasons",
			Method: http.MethodPost,
			Params: gin.Params{
				{Key: "user_id", Value: "1"},
				{Key: "date", Value: "2024-08-01"},
			},
			Body: gin.H{"reason": "reason1"},
		},
		resObj: test.HandlerRes{
			Sts:    http.StatusBadRequest,
			Res:    "reason_class is a required field",
			ErrStr: "validator.ValidationErrors",
		},
		fn: h.Create,
	}

	// CASE20
	// ERR: Invalid reason class (not int value)
	preComp20 := at.genCompany(compNo)
	preEmp20 := at.genEmployee(compNo, userID)
	preTimesheets20 := at.genTimesheet(preEmp20, 1)
	preMeasuredAttendances20 := at.genMeasuredAttendances(compNo, preEmp20, 1)
	preMeasuredAttendances20[0].StartTime = preMeasuredAttendances20[0].StartTime.Add(time.Minute * 30)
	preIntegAttDiff20 := at.genIntegAttDiff(1, compNo, int(value.TKLogPCMode), 30)

	case20 := attendanceDifferenceReasonTcase{
		desc:                   "CASE20: ERR: Invalid reason class (not int value)",
		preCompany:             preComp20,
		preEmployees:           []tbl.Employee{preEmp20},
		preTimesheets:          preTimesheets20,
		preMeasuredAttendances: preMeasuredAttendances20,
		preIntegAttDiff:        []tbl.IntegrationAttendanceDifference{preIntegAttDiff20},
		reqObj: test.HandlerReq{
			Url:    "/users/1/attendances/2024-08-01/reasons",
			Method: http.MethodPost,
			Params: gin.Params{
				{Key: "user_id", Value: "1"},
				{Key: "date", Value: "2024-08-01"},
			},
			Body: gin.H{
				"reason_class": 1.5,
				"reason":       "reason1",
			},
		},
		resObj: test.HandlerRes{
			Sts:    http.StatusBadRequest,
			Res:    "Invalid input",
			ErrStr: "*json.UnmarshalTypeError",
		},
		fn: h.Create,
	}

	// CASE21
	// ERR: Invalid reason class (less than 0)
	preComp21 := at.genCompany(compNo)
	preEmp21 := at.genEmployee(compNo, userID)
	preTimesheets21 := at.genTimesheet(preEmp21, 1)
	preMeasuredAttendances21 := at.genMeasuredAttendances(compNo, preEmp21, 1)
	preMeasuredAttendances21[0].StartTime = preMeasuredAttendances21[0].StartTime.Add(time.Minute * 30)
	preIntegAttDiff21 := at.genIntegAttDiff(1, compNo, int(value.TKLogPCMode), 30)

	case21 := attendanceDifferenceReasonTcase{
		desc:                   "CASE21: ERR: Invalid reason class (less than 0)",
		preCompany:             preComp21,
		preEmployees:           []tbl.Employee{preEmp21},
		preTimesheets:          preTimesheets21,
		preMeasuredAttendances: preMeasuredAttendances21,
		preIntegAttDiff:        []tbl.IntegrationAttendanceDifference{preIntegAttDiff21},
		reqObj: test.HandlerReq{
			Url:    "/users/1/attendances/2024-08-01/reasons",
			Method: http.MethodPost,
			Params: gin.Params{
				{Key: "user_id", Value: "1"},
				{Key: "date", Value: "2024-08-01"},
			},
			Body: gin.H{
				"reason_class": -1,
				"reason":       "reason1",
			},
		},
		resObj: test.HandlerRes{
			Sts:    http.StatusBadRequest,
			Res:    "reason_class must be 0 or greater",
			ErrStr: "validator.ValidationErrors",
		},
		fn: h.Create,
	}

	// CASE22
	// ERR: Out of reason class range
	preComp22 := at.genCompany(compNo)
	preEmp22 := at.genEmployee(compNo, userID)
	preTimesheets22 := at.genTimesheet(preEmp22, 1)
	preMeasuredAttendances22 := at.genMeasuredAttendances(compNo, preEmp22, 1)
	preMeasuredAttendances22[0].StartTime = preMeasuredAttendances22[0].StartTime.Add(time.Minute * 30)
	preIntegAttDiff22 := at.genIntegAttDiff(1, compNo, int(value.TKLogPCMode), 30)

	case22 := attendanceDifferenceReasonTcase{
		desc:                   "CASE22: ERR: Out of reason class range",
		preCompany:             preComp22,
		preEmployees:           []tbl.Employee{preEmp22},
		preTimesheets:          preTimesheets22,
		preMeasuredAttendances: preMeasuredAttendances22,
		preIntegAttDiff:        []tbl.IntegrationAttendanceDifference{preIntegAttDiff22},
		reqObj: test.HandlerReq{
			Url:    "/users/1/attendances/2024-08-01/reasons",
			Method: http.MethodPost,
			Params: gin.Params{
				{Key: "user_id", Value: "1"},
				{Key: "date", Value: "2024-08-01"},
			},
			Body: gin.H{
				"reason_class": 6,
				"reason":       "reason1",
			},
		},
		resObj: test.HandlerRes{
			Sts:    http.StatusBadRequest,
			Res:    "reason_class must be 5 or less",
			ErrStr: "validator.ValidationErrors",
		},
		fn: h.Create,
	}

	// CASE23
	// ERR: reason is an empty string when reason class is 0
	preComp23 := at.genCompany(compNo)
	preEmp23 := at.genEmployee(compNo, userID)
	preTimesheets23 := at.genTimesheet(preEmp23, 1)
	preMeasuredAttendances23 := at.genMeasuredAttendances(compNo, preEmp23, 1)
	preMeasuredAttendances23[0].StartTime = preMeasuredAttendances23[0].StartTime.Add(time.Minute * 30)
	preIntegAttDiff23 := at.genIntegAttDiff(1, compNo, int(value.TKLogPCMode), 30)

	case23 := attendanceDifferenceReasonTcase{
		desc:                   "CASE23: ERR: reason is an empty string when reason class is 0",
		preCompany:             preComp23,
		preEmployees:           []tbl.Employee{preEmp23},
		preTimesheets:          preTimesheets23,
		preMeasuredAttendances: preMeasuredAttendances23,
		preIntegAttDiff:        []tbl.IntegrationAttendanceDifference{preIntegAttDiff23},
		reqObj: test.HandlerReq{
			Url:    "/users/1/attendances/2024-08-01/reasons",
			Method: http.MethodPost,
			Params: gin.Params{
				{Key: "user_id", Value: "1"},
				{Key: "date", Value: "2024-08-01"},
			},
			Body: gin.H{
				"reason_class": 0,
				"reason":       "",
			},
		},
		resObj: test.HandlerRes{
			Sts:    http.StatusBadRequest,
			Res:    "attendance difference reason is required",
			ErrStr: "*ierror.customValidation",
		},
		fn: h.Create,
	}

	// CASE24
	// OK: reason is an empty string when reason class is not 0
	preComp24 := at.genCompany(compNo)
	preEmp24 := at.genEmployee(compNo, userID)
	preTimesheets24 := at.genTimesheet(preEmp24, 1)
	preMeasuredAttendances24 := at.genMeasuredAttendances(compNo, preEmp24, 1)
	preMeasuredAttendances24[0].StartTime = preMeasuredAttendances24[0].StartTime.Add(time.Minute * 30)
	preReason24 := at.genReasons(compNo, 1, preEmp24, value.DifferenceReasonClassPcUnnecessarily)
	reason24 := ""
	preReason24[0].Reason = &reason24
	preIntegAttDiff24 := at.genIntegAttDiff(1, compNo, int(value.TKLogPCMode), 30)

	case24 := attendanceDifferenceReasonTcase{
		desc:                   "CASE24: OK: reason is an empty string when reason class is not 0",
		preCompany:             preComp24,
		preEmployees:           []tbl.Employee{preEmp24},
		preTimesheets:          preTimesheets24,
		preMeasuredAttendances: preMeasuredAttendances24,
		expReasons:             preReason24,
		preIntegAttDiff:        []tbl.IntegrationAttendanceDifference{preIntegAttDiff24},
		reqObj: test.HandlerReq{
			Url:    "/users/1/attendances/2024-08-01/reasons",
			Method: http.MethodPost,
			Params: gin.Params{
				{Key: "user_id", Value: "1"},
				{Key: "date", Value: "2024-08-01"},
			},
			Body: gin.H{
				"reason_class": 2,
				"reason":       reason24,
			},
		},
		resObj: test.HandlerRes{
			Sts:    http.StatusOK,
			Res:    nil,
			ErrStr: nil,
		},
		fn: h.Create,
	}

	cases := []attendanceDifferenceReasonTcase{
		case1, case2, case3, case4, case5,
		case6, case7, case8, case9, case10,
		case11, case12, case13, case14, case15,
		case16, case17, case18, case19, case20,
		case21, case22, case23, case24,
	}

	at.ExecTest(t, compNo, userID, cases)
}

func Test_AttendanceDifferenceReason_Update(t *testing.T) {
	compNo := 1
	userID := 1

	h := NewAttendanceDifferenceReasonHandler()
	at := attendanceDifferenceReasonTester{}

	// CASE1
	// reason does not exist
	preComp1 := at.genCompany(compNo)
	preEmp1 := at.genEmployee(compNo, userID)

	case1 := attendanceDifferenceReasonTcase{
		desc:         "CASE1: ERR: reason does not exist",
		preCompany:   preComp1,
		preEmployees: []tbl.Employee{preEmp1},
		reqObj: test.HandlerReq{
			Url:    "/users/1/attendances/2024-08-01/reasons",
			Method: http.MethodPut,
			Params: gin.Params{
				{Key: "user_id", Value: "1"},
				{Key: "date", Value: "2024-08-01"},
			},
			Body: gin.H{
				"reason_class": 1,
				"reason":       "reason1",
			},
		},
		resObj: test.HandlerRes{
			Sts:    http.StatusNotFound,
			Res:    imsg.CreateErrMsg(imsg.NoResource),
			ErrStr: "*ierror.noResourceErr",
		},
		fn: h.Update,
	}

	// CASE2
	// employee does not exist
	preComp2 := at.genCompany(compNo)
	preEmp2 := at.genEmployee(compNo, userID)

	case2 := attendanceDifferenceReasonTcase{
		desc:         "CASE2: ERR: employee does not exist",
		preCompany:   preComp2,
		preEmployees: []tbl.Employee{preEmp2},
		reqObj: test.HandlerReq{
			Url:    "/users/999/attendances/2024-08-01/reasons",
			Method: http.MethodPut,
			Params: gin.Params{
				{Key: "user_id", Value: "999"},
				{Key: "date", Value: "2024-08-01"},
			},
			Body: gin.H{
				"reason_class": 1,
				"reason":       "reason1",
			},
		},
		resObj: test.HandlerRes{
			Sts:    http.StatusNotFound,
			Res:    imsg.CreateErrMsg(imsg.NoResource),
			ErrStr: "*ierror.noResourceErr",
		},
		fn: h.Update,
	}

	// CASE3
	// invalid date format
	preComp3 := at.genCompany(compNo)
	preEmp3 := at.genEmployee(compNo, userID)

	case3 := attendanceDifferenceReasonTcase{
		desc:         "CASE3: ERR: invalid date format",
		preCompany:   preComp3,
		preEmployees: []tbl.Employee{preEmp3},
		reqObj: test.HandlerReq{
			Url:    "/users/1/attendances/0000/reasons",
			Method: http.MethodPut,
			Params: gin.Params{
				{Key: "user_id", Value: "1"},
				{Key: "date", Value: "0000"},
			},
			Body: gin.H{
				"reason_class": 1,
				"reason":       "reason1",
			},
		},
		resObj: test.HandlerRes{
			Sts:    http.StatusNotFound,
			Res:    imsg.CreateErrMsg(imsg.NoResource),
			ErrStr: "*ierror.noResourceErr",
		},
		fn: h.Update,
	}
	// CASE4
	// reason string is too long
	preComp4 := at.genCompany(compNo)
	preEmp4 := at.genEmployee(compNo, userID)
	reasonString4 := strings.Repeat("a", 257)

	case4 := attendanceDifferenceReasonTcase{
		desc:         "CASE4: ERR: length of reason is too long",
		preCompany:   preComp4,
		preEmployees: []tbl.Employee{preEmp4},
		reqObj: test.HandlerReq{
			Url:    "/users/1/attendances/2024-08-01/reasons",
			Method: http.MethodPut,
			Params: gin.Params{
				{Key: "user_id", Value: "1"},
				{Key: "date", Value: "2024-08-01"},
			},
			Body: gin.H{
				"reason_class": 1,
				"reason":       reasonString4,
			},
		},
		resObj: test.HandlerRes{
			Sts:    http.StatusBadRequest,
			Res:    "reason must be a maximum of 256 characters in length",
			ErrStr: "validator.ValidationErrors",
		},
		fn: h.Update,
	}

	// CASE5
	// okay
	preComp5 := at.genCompany(compNo)
	preEmp5 := at.genEmployee(compNo, userID)
	preReason5 := at.genReasons(compNo, 1, preEmp5, value.DifferenceReasonClassPcOffOmission)
	expReasons5 := preReason5
	expReasons5[0].Reason = test.NewPtr("reason2").(*string)

	case5 := attendanceDifferenceReasonTcase{
		desc:         "CASE5: OK",
		preCompany:   preComp5,
		preEmployees: []tbl.Employee{preEmp5},
		preReasons:   preReason5,
		expReasons:   expReasons5,
		reqObj: test.HandlerReq{
			Url:    "/users/1/attendances/2024-08-01/reasons",
			Method: http.MethodPut,
			Params: gin.Params{
				{Key: "user_id", Value: "1"},
				{Key: "date", Value: "2024-08-01"},
			},
			Body: gin.H{
				"reason_class": 4,
				"reason":       "reason2",
			},
		},
		resObj: test.HandlerRes{
			Sts:    http.StatusOK,
			Res:    nil,
			ErrStr: nil,
		},
		fn: h.Update,
	}

	// CASE6
	// internal server error
	preComp6 := at.genCompany(compNo)
	preEmp6 := at.genEmployee(compNo, userID)
	preReason6 := at.genReasons(compNo, 1, preEmp6, value.DifferenceReasonClassOthers)

	mockCtrl6 := gomock.NewController(t)
	defer mockCtrl6.Finish()
	mockObj6 := mock_repo.NewMockAttendanceDifferenceReason(mockCtrl6)
	mockObj6.EXPECT().
		FindSpecific(gomock.Any(), gomock.Any(), gomock.Any()).
		Return(model.AttendanceDifferenceReason{}, nil)
	mockObj6.EXPECT().
		Update(gomock.Any(), gomock.Any()).
		Return(errors.New("AAA"))
	mrr6 := &test.MockRepoRegistry{}
	mrr6.MAttDiffReason = mockObj6

	case6 := attendanceDifferenceReasonTcase{
		desc:         "CASE6: ERR: internal server error",
		preCompany:   preComp6,
		preEmployees: []tbl.Employee{preEmp6},
		preReasons:   preReason6,
		expReasons:   preReason6,
		reqObj: test.HandlerReq{
			Url:    "/users/1/attendances/2024-08-01/reasons",
			Method: http.MethodPut,
			Params: gin.Params{
				{Key: "user_id", Value: "1"},
				{Key: "date", Value: "2024-08-01"},
			},
			Body: gin.H{
				"reason_class": 0,
				"reason":       "reason2",
			},
		},
		resObj: test.HandlerRes{
			Sts:    http.StatusInternalServerError,
			Res:    imsg.CreateErrMsg(imsg.ServerError),
			ErrStr: "*errors.errorString",
		},
		fn:  h.Update,
		mrr: mrr6,
	}

	// CASE7
	// max number of strings
	preComp7 := at.genCompany(compNo)
	preEmp7 := at.genEmployee(compNo, userID)
	preReason7 := at.genReasons(compNo, 1, preEmp7, value.DifferenceReasonClassPcUsedNotForWork)
	expReasons7 := preReason7
	reason7 := strings.Repeat("a", 256)
	expReasons5[0].Reason = &reason7

	case7 := attendanceDifferenceReasonTcase{
		desc:         "CASE7: OK, max number of strings",
		preCompany:   preComp7,
		preEmployees: []tbl.Employee{preEmp7},
		preReasons:   preReason7,
		expReasons:   expReasons7,
		reqObj: test.HandlerReq{
			Url:    "/users/1/attendances/2024-08-01/reasons",
			Method: http.MethodPut,
			Params: gin.Params{
				{Key: "user_id", Value: "1"},
				{Key: "date", Value: "2024-08-01"},
			},
			Body: gin.H{
				"reason_class": 1,
				"reason":       reason7,
			},
		},
		resObj: test.HandlerRes{
			Sts:    http.StatusOK,
			Res:    nil,
			ErrStr: nil,
		},
		fn: h.Update,
	}

	// CASE8
	// 1 length of reason for reason class 0
	preComp8 := at.genCompany(compNo)
	preEmp8 := at.genEmployee(compNo, userID)
	preReason8 := at.genReasons(compNo, 1, preEmp8, value.DifferenceReasonClassPcUnnecessarily)
	expReasons8 := preReason8
	reason8 := "1"
	expReasons8[0].Reason = &reason8
	expReasons8[0].ReasonClass = int(value.DifferenceReasonClassOthers)

	case8 := attendanceDifferenceReasonTcase{
		desc:         "CASE8: OK, 1 length of reason for reason class 0",
		preCompany:   preComp8,
		preEmployees: []tbl.Employee{preEmp8},
		preReasons:   preReason8,
		expReasons:   expReasons8,
		reqObj: test.HandlerReq{
			Url:    "/users/1/attendances/2024-08-01/reasons",
			Method: http.MethodPut,
			Params: gin.Params{
				{Key: "user_id", Value: "1"},
				{Key: "date", Value: "2024-08-01"},
			},
			Body: gin.H{
				"reason_class": 0,
				"reason":       reason8,
			},
		},
		resObj: test.HandlerRes{
			Sts:    http.StatusOK,
			Res:    nil,
			ErrStr: nil,
		},
		fn: h.Update,
	}

	// CASE9
	// 1 length of reason for class that other than 0
	preComp9 := at.genCompany(compNo)
	preEmp9 := at.genEmployee(compNo, userID)
	preReason9 := at.genReasons(compNo, 1, preEmp9, value.DifferenceReasonClassPcUnnecessarily)
	expReasons9 := preReason9
	reason9 := "a"
	expReasons9[0].Reason = &reason9
	expReasons9[0].ReasonClass = int(value.DifferenceReasonClassPcUsedNotForWork)

	case9 := attendanceDifferenceReasonTcase{
		desc:         "CASE9: OK, 1 length of reason for class that other than 0",
		preCompany:   preComp9,
		preEmployees: []tbl.Employee{preEmp9},
		preReasons:   preReason9,
		expReasons:   expReasons9,
		reqObj: test.HandlerReq{
			Url:    "/users/1/attendances/2024-08-01/reasons",
			Method: http.MethodPut,
			Params: gin.Params{
				{Key: "user_id", Value: "1"},
				{Key: "date", Value: "2024-08-01"},
			},
			Body: gin.H{
				"reason_class": 1,
				"reason":       reason9,
			},
		},
		resObj: test.HandlerRes{
			Sts:    http.StatusOK,
			Res:    nil,
			ErrStr: nil,
		},
		fn: h.Update,
	}

	// CASE10
	// empty reason
	preComp10 := at.genCompany(compNo)
	preEmp10 := at.genEmployee(compNo, userID)

	preReason10 := at.genReasons(compNo, 1, preEmp10, value.DifferenceReasonClassPcLogUnreadable)

	case10 := attendanceDifferenceReasonTcase{
		desc:         "CASE10: ERR, empty reason",
		preCompany:   preComp10,
		preEmployees: []tbl.Employee{preEmp10},
		preReasons:   preReason10,
		expReasons:   preReason10,
		reqObj: test.HandlerReq{
			Url:    "/users/1/attendances/2024-08-01/reasons",
			Method: http.MethodPut,
			Params: gin.Params{
				{Key: "user_id", Value: "1"},
				{Key: "date", Value: "2024-08-01"},
			},
			Body: gin.H{"reason_class": 4},
		},
		resObj: test.HandlerRes{
			Sts:    http.StatusBadRequest,
			Res:    "reason is a required field",
			ErrStr: "validator.ValidationErrors",
		},
		fn: h.Update,
	}

	// CASE11
	// OK: System admin user has updated other's reason successfully
	preComp11 := at.genCompany(compNo)
	preEmp11 := at.genEmployee(compNo, 2)
	preReason11 := at.genReasons(compNo, 1, preEmp11, value.DifferenceReasonClassPcOffOmission)
	expReasons11 := preReason11
	expReasons11[0].Reason = test.NewPtr("reason2").(*string)

	userSetting11 := test.CreateDefaultUserInfo()
	case11 := attendanceDifferenceReasonTcase{
		desc:         "CASE11: OK: System admin user has updated other's reason successfully",
		preCompany:   preComp11,
		preEmployees: []tbl.Employee{preEmp11},
		preReasons:   preReason11,
		expReasons:   expReasons11,
		reqObj: test.HandlerReq{
			Url:    "/users/2/attendances/2024-08-01/reasons",
			Method: http.MethodPut,
			Params: gin.Params{
				{Key: "user_id", Value: "2"},
				{Key: "date", Value: "2024-08-01"},
			},
			Body: gin.H{
				"reason_class": 4,
				"reason":       "reason2",
			},
		},
		resObj: test.HandlerRes{
			Sts:    http.StatusOK,
			Res:    nil,
			ErrStr: nil,
		},
		fn:          h.Update,
		userSetting: &userSetting11,
	}

	// CASE12
	// ERR: Regular member cannot update other's reason
	preComp12 := at.genCompany(compNo)
	preEmp12 := at.genEmployee(compNo, 2)
	preReason12 := at.genReasons(compNo, 1, preEmp12, value.DifferenceReasonClassOthers)

	userSetting12 := test.CreateDefaultUserInfo()
	userSetting12.SysAdminFlag = false
	case12 := attendanceDifferenceReasonTcase{
		desc:         "CASE12: ERR: Regular member cannot update other's reason",
		preCompany:   preComp12,
		preEmployees: []tbl.Employee{preEmp12},
		preReasons:   preReason12,
		expReasons:   preReason12,
		reqObj: test.HandlerReq{
			Url:    "/users/2/attendances/2024-08-01/reasons",
			Method: http.MethodPut,
			Params: gin.Params{
				{Key: "user_id", Value: "2"},
				{Key: "date", Value: "2024-08-01"},
			},
			Body: gin.H{
				"reason_class": 0,
				"reason":       "reason2",
			},
		},
		resObj: test.HandlerRes{
			Sts:    http.StatusForbidden,
			Res:    imsg.CreatePermErrMsg(config.DefaultLanguage, imsg.PermForbidden, imsg.PermInsufficientSystemAdmin),
			ErrStr: "*ierror.permissionErr",
		},
		fn:          h.Update,
		userSetting: &userSetting12,
	}

	// CASE13
	// OK: Regular member has updated his own reason successfully
	preComp13 := at.genCompany(compNo)
	preEmp13 := at.genEmployee(compNo, 1)
	preReason13 := at.genReasons(compNo, 1, preEmp13, value.DifferenceReasonClassPcUsedNotForWork)
	expReasons13 := preReason13
	expReasons13[0].Reason = test.NewPtr("reason2").(*string)

	userSetting13 := test.CreateDefaultUserInfo()
	userSetting13.SysAdminFlag = false
	case13 := attendanceDifferenceReasonTcase{
		desc:         "CASE13: OK: Regular member has updated his own reason successfully",
		preCompany:   preComp13,
		preEmployees: []tbl.Employee{preEmp13},
		preReasons:   preReason13,
		expReasons:   expReasons13,
		reqObj: test.HandlerReq{
			Url:    "/users/1/attendances/2024-08-01/reasons",
			Method: http.MethodPut,
			Params: gin.Params{
				{Key: "user_id", Value: "1"},
				{Key: "date", Value: "2024-08-01"},
			},
			Body: gin.H{
				"reason_class": 1,
				"reason":       "reason2",
			},
		},
		resObj: test.HandlerRes{
			Sts:    http.StatusOK,
			Res:    nil,
			ErrStr: nil,
		},
		fn:          h.Update,
		userSetting: &userSetting13,
	}

	// CASE14
	// empty reason class
	preComp14 := at.genCompany(compNo)
	preEmp14 := at.genEmployee(compNo, userID)

	preReason14 := at.genReasons(compNo, 1, preEmp14, value.DifferenceReasonClassOthers)

	case14 := attendanceDifferenceReasonTcase{
		desc:         "CASE14: ERR, empty reason class",
		preCompany:   preComp14,
		preEmployees: []tbl.Employee{preEmp14},
		preReasons:   preReason14,
		expReasons:   preReason14,
		reqObj: test.HandlerReq{
			Url:    "/users/1/attendances/2024-08-01/reasons",
			Method: http.MethodPut,
			Params: gin.Params{
				{Key: "user_id", Value: "1"},
				{Key: "date", Value: "2024-08-01"},
			},
			Body: gin.H{"reason": "reason1"},
		},
		resObj: test.HandlerRes{
			Sts:    http.StatusBadRequest,
			Res:    "reason_class is a required field",
			ErrStr: "validator.ValidationErrors",
		},
		fn: h.Update,
	}

	// CASE15
	// Invalid reason class (not int value)
	preComp15 := at.genCompany(compNo)
	preEmp15 := at.genEmployee(compNo, userID)

	preReason15 := at.genReasons(compNo, 1, preEmp15, value.DifferenceReasonClassOthers)

	case15 := attendanceDifferenceReasonTcase{
		desc:         "CASE15: ERR, Invalid reason class (not int value)",
		preCompany:   preComp15,
		preEmployees: []tbl.Employee{preEmp15},
		preReasons:   preReason15,
		expReasons:   preReason15,
		reqObj: test.HandlerReq{
			Url:    "/users/1/attendances/2024-08-01/reasons",
			Method: http.MethodPut,
			Params: gin.Params{
				{Key: "user_id", Value: "1"},
				{Key: "date", Value: "2024-08-01"},
			},
			Body: gin.H{
				"reason_class": "test",
				"reason":       "reason1",
			},
		},
		resObj: test.HandlerRes{
			Sts:    http.StatusBadRequest,
			Res:    "Invalid input",
			ErrStr: "*json.UnmarshalTypeError",
		},
		fn: h.Update,
	}

	// CASE16
	// Invalid reason class (less than 0)
	preComp16 := at.genCompany(compNo)
	preEmp16 := at.genEmployee(compNo, userID)

	preReason16 := at.genReasons(compNo, 1, preEmp16, value.DifferenceReasonClassOthers)

	case16 := attendanceDifferenceReasonTcase{
		desc:         "CASE16: ERR, Invalid reason class (less than 0)",
		preCompany:   preComp16,
		preEmployees: []tbl.Employee{preEmp16},
		preReasons:   preReason16,
		expReasons:   preReason16,
		reqObj: test.HandlerReq{
			Url:    "/users/1/attendances/2024-08-01/reasons",
			Method: http.MethodPut,
			Params: gin.Params{
				{Key: "user_id", Value: "1"},
				{Key: "date", Value: "2024-08-01"},
			},
			Body: gin.H{
				"reason_class": -1,
				"reason":       "reason1",
			},
		},
		resObj: test.HandlerRes{
			Sts:    http.StatusBadRequest,
			Res:    "reason_class must be 0 or greater",
			ErrStr: "validator.ValidationErrors",
		},
		fn: h.Update,
	}

	// CASE17
	// Out of reason class range
	preComp17 := at.genCompany(compNo)
	preEmp17 := at.genEmployee(compNo, userID)

	preReason17 := at.genReasons(compNo, 1, preEmp17, value.DifferenceReasonClassOthers)

	case17 := attendanceDifferenceReasonTcase{
		desc:         "CASE17: ERR: Out of reason class range",
		preCompany:   preComp17,
		preEmployees: []tbl.Employee{preEmp17},
		preReasons:   preReason17,
		expReasons:   preReason17,
		reqObj: test.HandlerReq{
			Url:    "/users/1/attendances/2024-08-01/reasons",
			Method: http.MethodPut,
			Params: gin.Params{
				{Key: "user_id", Value: "1"},
				{Key: "date", Value: "2024-08-01"},
			},
			Body: gin.H{
				"reason_class": 6,
				"reason":       "reason1",
			},
		},
		resObj: test.HandlerRes{
			Sts:    http.StatusBadRequest,
			Res:    "reason_class must be 5 or less",
			ErrStr: "validator.ValidationErrors",
		},
		fn: h.Update,
	}

	// CASE18
	// reason is an empty string when reason class is 0
	preComp18 := at.genCompany(compNo)
	preEmp18 := at.genEmployee(compNo, userID)
	preReason18 := at.genReasons(compNo, 1, preEmp18, value.DifferenceReasonClassPcLogUnreadable)

	case18 := attendanceDifferenceReasonTcase{
		desc:         "CASE18: ERR, reason is an empty string when reason class is 0",
		preCompany:   preComp18,
		preEmployees: []tbl.Employee{preEmp18},
		preReasons:   preReason18,
		expReasons:   preReason18,
		reqObj: test.HandlerReq{
			Url:    "/users/1/attendances/2024-08-01/reasons",
			Method: http.MethodPut,
			Params: gin.Params{
				{Key: "user_id", Value: "1"},
				{Key: "date", Value: "2024-08-01"},
			},
			Body: gin.H{
				"reason_class": 0,
				"reason":       "",
			},
		},
		resObj: test.HandlerRes{
			Sts:    http.StatusBadRequest,
			Res:    "attendance difference reason is required",
			ErrStr: "*ierror.customValidation",
		},
		fn: h.Update,
	}

	// CASE19
	// OK: reason is an empty string when reason class is not 0
	preComp19 := at.genCompany(compNo)
	preEmp19 := at.genEmployee(compNo, userID)
	preReason19 := at.genReasons(compNo, 1, preEmp19, value.DifferenceReasonClassPcUnnecessarily)
	expReasons19 := preReason19
	reason19 := ""
	expReasons19[0].Reason = &reason19
	expReasons19[0].ReasonClass = int(value.DifferenceReasonClassPcOffOmission)

	case19 := attendanceDifferenceReasonTcase{
		desc:         "CASE19: OK, reason is an empty string when reason class is not 0",
		preCompany:   preComp19,
		preEmployees: []tbl.Employee{preEmp19},
		preReasons:   preReason19,
		expReasons:   expReasons19,
		reqObj: test.HandlerReq{
			Url:    "/users/1/attendances/2024-08-01/reasons",
			Method: http.MethodPut,
			Params: gin.Params{
				{Key: "user_id", Value: "1"},
				{Key: "date", Value: "2024-08-01"},
			},
			Body: gin.H{
				"reason_class": 4,
				"reason":       reason19,
			},
		},
		resObj: test.HandlerRes{
			Sts:    http.StatusOK,
			Res:    nil,
			ErrStr: nil,
		},
		fn: h.Update,
	}

	cases := []attendanceDifferenceReasonTcase{
		case1, case2, case3, case4, case5,
		case6, case7, case8, case9, case10,
		case11, case12, case13, case14, case15,
		case16, case17, case18, case19,
	}

	at.ExecTest(t, compNo, userID, cases)
}

func Test_AttendanceDifferenceReason_Delete(t *testing.T) {
	compNo := 1
	userID := 1

	h := NewAttendanceDifferenceReasonHandler()
	at := attendanceDifferenceReasonTester{}

	// CASE1
	// reason does not exist
	preComp1 := at.genCompany(compNo)
	preEmp1 := at.genEmployee(compNo, userID)

	case1 := attendanceDifferenceReasonTcase{
		desc:         "CASE1: ERR: reason does not exist",
		preCompany:   preComp1,
		preEmployees: []tbl.Employee{preEmp1},
		reqObj: test.HandlerReq{
			Url:    "/users/1/attendances/2024-08-01/reasons",
			Method: http.MethodDelete,
			Params: gin.Params{
				{Key: "user_id", Value: "1"},
				{Key: "date", Value: "2024-08-01"},
			},
		},
		resObj: test.HandlerRes{
			Sts:    http.StatusNotFound,
			Res:    imsg.CreateErrMsg(imsg.NoResource),
			ErrStr: "*ierror.noResourceErr",
		},
		fn: h.Delete,
	}

	// CASE2
	// employee does not exist
	preComp2 := at.genCompany(compNo)
	preEmp2 := at.genEmployee(compNo, userID)

	case2 := attendanceDifferenceReasonTcase{
		desc:         "CASE2: ERR: employee does not exist",
		preCompany:   preComp2,
		preEmployees: []tbl.Employee{preEmp2},
		reqObj: test.HandlerReq{
			Url:    "/users/999/attendances/2024-08-01/reasons",
			Method: http.MethodDelete,
			Params: gin.Params{
				{Key: "user_id", Value: "999"},
				{Key: "date", Value: "2024-08-01"},
			},
		},
		resObj: test.HandlerRes{
			Sts:    http.StatusNotFound,
			Res:    imsg.CreateErrMsg(imsg.NoResource),
			ErrStr: "*ierror.noResourceErr",
		},
		fn: h.Delete,
	}

	// CASE3
	// invalid date format
	preComp3 := at.genCompany(compNo)
	preEmp3 := at.genEmployee(compNo, userID)

	case3 := attendanceDifferenceReasonTcase{
		desc:         "CASE3: ERR: invalid date format",
		preCompany:   preComp3,
		preEmployees: []tbl.Employee{preEmp3},
		reqObj: test.HandlerReq{
			Url:    "/users/1/attendances/0000/reasons",
			Method: http.MethodDelete,
			Params: gin.Params{
				{Key: "user_id", Value: "1"},
				{Key: "date", Value: "0000"},
			},
		},
		resObj: test.HandlerRes{
			Sts:    http.StatusNotFound,
			Res:    imsg.CreateErrMsg(imsg.NoResource),
			ErrStr: "*ierror.noResourceErr",
		},
		fn: h.Delete,
	}

	// CASE4
	// okay
	preComp4 := at.genCompany(compNo)
	preEmp4 := at.genEmployee(compNo, userID)
	preReason4 := at.genReasons(compNo, 1, preEmp4, value.DifferenceReasonClassPcUsedNotForWork)

	case4 := attendanceDifferenceReasonTcase{
		desc:         "CASE4: OK",
		preCompany:   preComp4,
		preEmployees: []tbl.Employee{preEmp4},
		preReasons:   preReason4,
		expReasons:   []tbl.AttendanceDifferenceReason{},
		reqObj: test.HandlerReq{
			Url:    "/users/1/attendances/2024-08-01/reasons",
			Method: http.MethodDelete,
			Params: gin.Params{
				{Key: "user_id", Value: "1"},
				{Key: "date", Value: "2024-08-01"},
			},
		},
		resObj: test.HandlerRes{
			Sts:    http.StatusNoContent,
			Res:    nil,
			ErrStr: nil,
		},
		fn: h.Delete,
	}

	// CASE5
	// internal server error
	preComp5 := at.genCompany(compNo)
	preEmp5 := at.genEmployee(compNo, userID)
	preReason5 := at.genReasons(compNo, 1, preEmp5, value.DifferenceReasonClassPcUnnecessarily)

	mockCtrl5 := gomock.NewController(t)
	defer mockCtrl5.Finish()
	mockObj5 := mock_repo.NewMockAttendanceDifferenceReason(mockCtrl5)
	mockObj5.
		EXPECT().
		FindSpecific(gomock.Any(), gomock.Any(), gomock.Any()).
		Return(model.AttendanceDifferenceReason{}, nil)
	mockObj5.EXPECT().Delete(gomock.Any()).Return(errors.New("AAA"))
	mrr5 := &test.MockRepoRegistry{}
	mrr5.MAttDiffReason = mockObj5

	case5 := attendanceDifferenceReasonTcase{
		desc:         "CASE5: ERR: internal server error",
		preCompany:   preComp5,
		preEmployees: []tbl.Employee{preEmp5},
		preReasons:   preReason5,
		expReasons:   preReason5,
		reqObj: test.HandlerReq{
			Url:    "/users/1/attendances/2024-08-01/reasons",
			Method: http.MethodDelete,
			Params: gin.Params{
				{Key: "user_id", Value: "1"},
				{Key: "date", Value: "2024-08-01"},
			},
		},
		resObj: test.HandlerRes{
			Sts:    http.StatusInternalServerError,
			Res:    imsg.CreateErrMsg(imsg.ServerError),
			ErrStr: "*errors.errorString",
		},
		fn:  h.Delete,
		mrr: mrr5,
	}

	// CASE6
	// OK: System admin user has deleted other's reason successfully
	preComp6 := at.genCompany(compNo)
	preEmp6 := at.genEmployee(compNo, 2)
	preReason6 := at.genReasons(compNo, 1, preEmp6, value.DifferenceReasonClassPcLogUnreadable)

	userSetting6 := test.CreateDefaultUserInfo()
	case6 := attendanceDifferenceReasonTcase{
		desc:         "CASE6: OK: System admin user has deleted other's reason successfully",
		preCompany:   preComp6,
		preEmployees: []tbl.Employee{preEmp6},
		preReasons:   preReason6,
		expReasons:   []tbl.AttendanceDifferenceReason{},
		reqObj: test.HandlerReq{
			Url:    "/users/2/attendances/2024-08-01/reasons",
			Method: http.MethodDelete,
			Params: gin.Params{
				{Key: "user_id", Value: "2"},
				{Key: "date", Value: "2024-08-01"},
			},
		},
		resObj: test.HandlerRes{
			Sts:    http.StatusNoContent,
			Res:    nil,
			ErrStr: nil,
		},
		fn:          h.Delete,
		userSetting: &userSetting6,
	}

	// CASE7
	// ERR: Regular member cannot delete other's reason
	preComp7 := at.genCompany(compNo)
	preEmp7 := at.genEmployee(compNo, 2)
	preReason7 := at.genReasons(compNo, 1, preEmp7, value.DifferenceReasonClassPcOffOmission)

	userSetting7 := test.CreateDefaultUserInfo()
	userSetting7.SysAdminFlag = false
	case7 := attendanceDifferenceReasonTcase{
		desc:         "CASE7: ERR: Regular member cannot delete other's reason",
		preCompany:   preComp7,
		preEmployees: []tbl.Employee{preEmp7},
		preReasons:   preReason7,
		expReasons:   preReason7,
		reqObj: test.HandlerReq{
			Url:    "/users/2/attendances/2024-08-01/reasons",
			Method: http.MethodDelete,
			Params: gin.Params{
				{Key: "user_id", Value: "2"},
				{Key: "date", Value: "2024-08-01"},
			},
		},
		resObj: test.HandlerRes{
			Sts:    http.StatusForbidden,
			Res:    imsg.CreatePermErrMsg(config.DefaultLanguage, imsg.PermForbidden, imsg.PermInsufficientSystemAdmin),
			ErrStr: "*ierror.permissionErr",
		},
		fn:          h.Delete,
		userSetting: &userSetting7,
	}

	// CASE8
	// OK: Regular member has deleted his own reason successfully
	preComp8 := at.genCompany(compNo)
	preEmp8 := at.genEmployee(compNo, 1)
	preReason8 := at.genReasons(compNo, 1, preEmp8, value.DifferenceReasonClassOthers)

	userSetting8 := test.CreateDefaultUserInfo()
	userSetting8.SysAdminFlag = false
	case8 := attendanceDifferenceReasonTcase{
		desc:         "CASE8: OK: Regular member has deleted his own reason successfully",
		preCompany:   preComp8,
		preEmployees: []tbl.Employee{preEmp8},
		preReasons:   preReason8,
		expReasons:   []tbl.AttendanceDifferenceReason{},
		reqObj: test.HandlerReq{
			Url:    "/users/1/attendances/2024-08-01/reasons",
			Method: http.MethodDelete,
			Params: gin.Params{
				{Key: "user_id", Value: "1"},
				{Key: "date", Value: "2024-08-01"},
			},
		},
		resObj: test.HandlerRes{
			Sts:    http.StatusNoContent,
			Res:    nil,
			ErrStr: nil,
		},
		fn:          h.Delete,
		userSetting: &userSetting8,
	}

	cases := []attendanceDifferenceReasonTcase{
		case1, case2, case3, case4, case5, case6, case7, case8,
	}

	at.ExecTest(t, compNo, userID, cases)
}
