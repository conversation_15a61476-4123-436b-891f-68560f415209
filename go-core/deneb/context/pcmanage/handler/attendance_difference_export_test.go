package handler

import (
	"errors"
	"fmt"
	"net/http"
	xurl "net/url"
	"sort"
	"testing"
	"time"

	"github.com/golang/mock/gomock"
	"gitlab.com/innopm/deneb/common/idb"
	"gitlab.com/innopm/deneb/common/ilog"
	"gitlab.com/innopm/deneb/config"
	new_model "gitlab.com/innopm/deneb/context/pcmanage/domain/model"
	"gitlab.com/innopm/deneb/context/pcmanage/domain/value"
	cmockrepo "gitlab.com/innopm/deneb/context/pcmanage/mock/repo"
	"gitlab.com/innopm/deneb/domain/model"
	"gitlab.com/innopm/deneb/domain/model/tbl"
	"gitlab.com/innopm/deneb/test"
	"gitlab.com/innopm/deneb/test/assertmodel"
	"gitlab.com/innopm/deneb/test/mockrepo"
	"gitlab.com/innopm/deneb/util"
	"gitlab.com/innopm/deneb/util/intutil"
	"gitlab.com/innopm/deneb/util/langutil"
	"gitlab.com/innopm/deneb/util/strutil"
	"gorm.io/gorm"
)

type attendanceDifferencesExportTcase struct {
	desc string

	// prepare data
	preCompany        tbl.Company
	preEmployees      []tbl.Employee
	preTitles         []tbl.Title
	preContracts      []tbl.Contract
	preCustomers      []tbl.Customer
	preDepartments    []tbl.Department
	preTSheets        []tbl.Timesheet
	preTSheetRests    []tbl.TimesheetRest
	preMAtts          []tbl.MeasuredAttendance
	preAttDiffReasons []tbl.AttendanceDifferenceReason
	preIntegAttDiff   []tbl.IntegrationAttendanceDifference

	// exp data
	expActLogs []tbl.ActivityLog

	// tester
	reqObj      test.HandlerReq
	resObj      test.ExportHandlerRes
	fn          test.ExportHFunc
	mrr         *test.MockRepoRegistry
	userSetting *model.UserInfo
	accessInfo  *model.AccessInfo
}

type attendanceDifferencesExportTester struct{}

func (a *attendanceDifferencesExportTester) genCompany(compNo int) tbl.Company {
	return tbl.Company{
		CompanyNo:               compNo,
		CompanyName:             "かいしゃ1",
		AppliedUser:             0,
		LanguageCode:            "en",
		CurrencyCode:            "JPY",
		Timezone:                "UTC",
		CountryCode:             "JP",
		LogoFile:                "abcdefg.png",
		StartMonth:              4,
		CostMax:                 1.00,
		ReportTax:               1,
		ExpenseGroup:            1,
		JobtimeType:             1,
		JobtimeProcessCount:     0,
		UseJobtimeFlag:          true,
		SalesStatus:             true,
		WorktimeStep:            15,
		WorktimeUnit:            0,
		WorktimeRangeMin:        15,
		HoursInDay:              8.00,
		HoursInMonth:            160.00,
		TimesheetUseType:        true,
		TimesheetRestType:       true,
		TimesheetProjectOrder:   0,
		TimesheetFutureFlag:     true,
		TimesheetEqualityFlag:   true,
		EndTimeCalcFlag:         0,
		ProjectStr:              "0",
		StartTime:               "09:00:00",
		EndTime:                 "18:00:00",
		Rest1StartTime:          "12:00:00",
		Rest1EndTime:            "13:00:00",
		Rest2StartTime:          "",
		Rest2EndTime:            "",
		HalfRest1StartTime:      "09:00:00",
		HalfRest1EndTime:        "13:00:00",
		HalfRest2StartTime:      "14:00:00",
		HalfRest2EndTime:        "18:00:00",
		SyncCalendarService:     0,
		CertifyService:          0,
		PasswdExpire:            0,
		PasswdAlert:             0,
		ExpenseNo:               0,
		GanttFlag:               true,
		ProjectCdFlag:           true,
		ProjectCdStr:            "",
		CompanyProjectNo:        0,
		CompanyStatus:           0,
		AttendanceManageUseType: true,
		UseHolidayCountryCode:   "",
		OrignalHolidayUseFlag:   true,
		CreateTime:              util.DateToTime("2019-11-01 02:36:30"),
		UpdateTime:              util.DateToTime("2019-11-30 17:36:30"),
		AuthCode:                "0",
		SamlFlag:                true,
		APIFlag:                 true,
		UseTimesheetApproval:    true,
	}
}

func (a *attendanceDifferencesExportTester) genEmployee(compNo, count int) []tbl.Employee {
	var emps []tbl.Employee
	for i := 1; i <= count; i++ {
		emp := tbl.Employee{
			EmployeeNo:              i,
			CompanyNo:               compNo,
			DepartmentNo:            test.NewPtr(i).(*int),
			ContractNo:              test.NewPtr(i).(*int),
			TitleNo:                 test.NewPtr(i).(*int),
			CustomerNo:              test.NewPtr(i).(*int),
			Salary:                  test.NewPtr(6250.0).(*float64),
			FirstName:               test.NewPtr(fmt.Sprintf("Employee%d", i)).(*string),
			FamilyName:              test.NewPtr(fmt.Sprintf("Hoge%d", i)).(*string),
			Email:                   test.NewPtr(fmt.Sprintf("<EMAIL>", i)).(*string),
			Password:                nil,
			MemberCode:              test.NewPtr(fmt.Sprintf("EmployeeCode%d", i)).(*string),
			StartDate:               test.NewPtr(util.DateToTime("2019-12-01")).(*time.Time),
			EndDate:                 test.NewPtr(util.DateToTime("2024-12-31")).(*time.Time),
			OutputCsv:               test.NewPtr(true).(*bool),
			UseCalendarFlag:         test.NewPtr(true).(*bool),
			UseSyncCalendarType:     test.NewPtr(1).(*int),
			TimesheetProjectOrder:   test.NewPtr(1).(*int),
			EndTimeCalcFlag:         test.NewPtr(0).(*int),
			EmployeeCostGroup:       test.NewPtr(1).(*int),
			DayEndTime:              test.NewPtr("05:00:00").(*string),
			UseCertifyType:          test.NewPtr(1).(*int),
			PasswdTime:              test.NewPtr(util.DateToTime("2020-01-01")).(*time.Time),
			LanguageCode:            test.NewPtr("jp").(*string),
			Timezone:                test.NewPtr("UTC").(*string),
			UseOvertimeRequestType:  test.NewPtr(1).(*int),
			VacationMinusRejectFlag: test.NewPtr(true).(*bool),
			CreateTime:              util.DateToTime("2020-01-01"),
			UpdateTime:              util.DateToTime("2020-01-31"),
			AuthAPIUse:              test.NewPtr(true).(*bool),
		}

		emps = append(emps, emp)
	}

	return emps
}

func (a *attendanceDifferencesExportTester) genTitles(
	compNo, count int,
) []tbl.Title {
	var titles []tbl.Title
	for i := 1; i <= count; i++ {
		title := tbl.Title{
			TitleNo:    i,
			CompanyNo:  compNo,
			TitleName:  test.NewPtr(fmt.Sprintf("Title%d", i)).(*string),
			CreateTime: util.DateToTime("2020-01-01"),
			UpdateTime: util.DateToTime("2020-01-31"),
		}

		titles = append(titles, title)
	}

	return titles
}

func (a *attendanceDifferencesExportTester) genContracts(
	compNo, count int,
) []tbl.Contract {
	var contracts []tbl.Contract
	for i := 1; i <= count; i++ {
		contract := tbl.Contract{
			ContractNo:   i,
			CompanyNo:    compNo,
			ContractName: test.NewPtr(fmt.Sprintf("Contract%d", i)).(*string),
			CreateTime:   util.DateToTime("2020-01-01"),
			UpdateTime:   util.DateToTime("2020-01-31"),
		}

		contracts = append(contracts, contract)
	}

	return contracts
}

func (a *attendanceDifferencesExportTester) genCustomers(
	compNo, count int,
) []tbl.Customer {
	var customers []tbl.Customer
	for i := 1; i <= count; i++ {
		customer := tbl.Customer{
			CustomerNo:   i,
			CompanyNo:    compNo,
			CustomerName: test.NewPtr(fmt.Sprintf("Customer%d", i)).(*string),
			CreateTime:   util.DateToTime("2020-01-01"),
			UpdateTime:   util.DateToTime("2020-01-31"),
		}

		customers = append(customers, customer)
	}

	return customers
}

func (a *attendanceDifferencesExportTester) genDepartments(
	compNo, count int,
) []tbl.Department {
	var departments []tbl.Department
	for i := 1; i <= count; i++ {
		department := tbl.Department{
			DepartmentNo:   i,
			CompanyNo:      compNo,
			ManagementCode: test.NewPtr(fmt.Sprintf("DepartmentCode%d", i)).(*string),
			DepartmentName: test.NewPtr(fmt.Sprintf("Department%d", i)).(*string),
			CreateTime:     util.DateToTime("2020-01-01"),
			UpdateTime:     util.DateToTime("2020-01-31"),
		}

		departments = append(departments, department)
	}

	return departments
}

func (a *attendanceDifferencesExportTester) genReasons(
	compNo int,
	emps []tbl.Employee,
	count int,
	reasonClass int,
) []tbl.AttendanceDifferenceReason {
	total := 1
	attendanceDate := util.DateToTime("2024-08-01")
	var reasons []tbl.AttendanceDifferenceReason

	for _, emp := range emps {
		for i := 1; i <= count; i++ {
			reason := tbl.AttendanceDifferenceReason{
				ID:                 total,
				CompanyNo:          compNo,
				EmployeeNo:         emp.EmployeeNo,
				EmployeeFirstName:  *emp.FirstName,
				EmployeeFamilyName: *emp.FamilyName,
				AttendanceDate:     attendanceDate,
				ReasonClass:        reasonClass,
				Reason:             test.NewPtr(fmt.Sprintf("reason%d", i)).(*string),
				CreatedAt:          attendanceDate,
				CreateUser:         i,
				UpdatedAt:          attendanceDate,
				UpdateUser:         i,
			}
			reasons = append(reasons, reason)

			attendanceDate = attendanceDate.AddDate(0, 0, 1)
			total++
		}

		attendanceDate = util.DateToTime("2024-08-01")
	}

	return reasons
}

func (a *attendanceDifferencesExportTester) genTimesheets(
	emp tbl.Employee,
	count int,
) []tbl.Timesheet {
	var timesheets []tbl.Timesheet
	tsheetDate := util.DateToTime("2024-08-01")
	tsheetEndDate := tsheetDate.AddDate(0, 0, count)

	for i := 1; tsheetDate.Before(tsheetEndDate); i++ {
		tsheet := tbl.Timesheet{
			// TimesheetNo:               i,
			EmployeeNo:                emp.EmployeeNo,
			TimesheetDate:             tsheetDate,
			StartTime:                 test.NewPtr("08:00:00").(*string),
			StartStampTime:            test.NewPtr("08:00:00").(*string),
			EndTime:                   test.NewPtr("17:00:00").(*string),
			EndStampTime:              test.NewPtr("17:00:00").(*string),
			HasAttendance:             test.NewPtr(false).(*bool),
			Comments:                  test.NewPtr("comments").(*string),
			Status:                    5,
			HolidayType:               test.NewPtr(0).(*int),
			TmpDeleteFlag:             false,
			Salary:                    1,
			CreateTime:                tsheetDate,
			UpdateTime:                tsheetDate,
			ApprovalStatus:            test.NewPtr(0).(*int),
			AutoCalculationAttendance: test.NewPtr(false).(*bool),
		}

		timesheets = append(timesheets, tsheet)
		tsheetDate = tsheetDate.AddDate(0, 0, 1)
	}

	return timesheets
}

func (a *attendanceDifferencesExportTester) genRests(
	timeSheets []tbl.Timesheet,
) []tbl.TimesheetRest {
	var rests []tbl.TimesheetRest

	for i, tsheet := range timeSheets {
		rest := tbl.TimesheetRest{
			RestNo:      i + 1,
			TimesheetNo: i + 1,
			StartDate:   test.NewPtr(tsheet.TimesheetDate).(*time.Time),
			StartTime:   test.NewPtr("12:00:00").(*string),
			EndDate:     test.NewPtr(tsheet.TimesheetDate).(*time.Time),
			EndTime:     test.NewPtr("13:00:00").(*string),
			CreateTime:  tsheet.CreateTime,
			UpdateTime:  tsheet.UpdateTime,
		}

		rests = append(rests, rest)
	}

	return rests
}

func (a *attendanceDifferencesExportTester) genMeasuredAttendances(
	compNo int,
	emp tbl.Employee,
	count int,
) []tbl.MeasuredAttendance {
	var mAttendances []tbl.MeasuredAttendance
	attDateStart := util.DateToTime("2024-08-01 08:00:00")
	attDateEnd := util.DateToTime("2024-08-01 17:00:00")

	for i := 1; i <= count; i++ {
		mAttend := tbl.MeasuredAttendance{
			// ID:             i,
			CompanyNo:      compNo,
			EmployeeNo:     emp.EmployeeNo,
			AttendanceDate: attDateStart,
			StartTime:      attDateStart,
			EndTime:        test.NewPtr(attDateEnd).(*time.Time),
			IdleMin:        test.NewPtr(60).(*int),
			CreatedAt:      attDateStart,
			CreateUser:     i,
			UpdatedAt:      attDateStart,
			UpdateUser:     i,
		}
		mAttendances = append(mAttendances, mAttend)

		attDateStart = attDateStart.AddDate(0, 0, 1)
		attDateEnd = attDateEnd.AddDate(0, 0, 1)
	}

	return mAttendances
}

func (a *attendanceDifferencesExportTester) genBodies(
	startDay time.Time,
	endDay time.Time,
	emps []tbl.Employee,
	tSheets []tbl.Timesheet,
	mAtts []tbl.MeasuredAttendance,
	reasons []tbl.AttendanceDifferenceReason,
) []util.H {
	var emptyEodies []util.H
	for _, emp := range emps {
		for date := startDay; date.Before(endDay) || date.Equal(endDay); date = date.AddDate(0, 0, 1) {
			emptyBody := util.H{
				"attendance_date":      date.Format("2006/01/02"),
				"member_code":          fmt.Sprintf("EmployeeCode%d", emp.EmployeeNo),
				"member_name":          fmt.Sprintf("Hoge%d Employee%d", emp.EmployeeNo, emp.EmployeeNo),
				"email":                fmt.Sprintf("<EMAIL>", emp.EmployeeNo),
				"title_name":           fmt.Sprintf("Title%d", emp.EmployeeNo),
				"contract_name":        fmt.Sprintf("Contract%d", emp.EmployeeNo),
				"customer_name":        fmt.Sprintf("Customer%d", emp.EmployeeNo),
				"department_code":      fmt.Sprintf("DepartmentCode%d", emp.EmployeeNo),
				"department_name":      fmt.Sprintf("Department%d", emp.EmployeeNo),
				"start_time":           "",
				"pclog_start_time":     "",
				"start_time_diff":      "",
				"start_time_diff_flag": false,
				"end_time":             "",
				"pclog_end_time":       "",
				"end_time_diff":        "",
				"end_time_diff_flag":   false,
				"work_time":            "",
				"pclog_work_time":      "",
				"work_time_diff":       "",
				"idle_time":            "",
				"reas_reason_class":    "",
				"reas_reason":          "",
				"reas_time":            "",
				"fixed_status":         true,
			}
			emptyEodies = append(emptyEodies, emptyBody)
		}
	}

	var bodies []util.H
	for _, emp := range emps {
		for i, tSheet := range tSheets {
			var reason, reasonClass, reasonTime string
			reasonPtr := a.getReasonByEmpIDAndDate(emp.EmployeeNo, tSheet.TimesheetDate, reasons)
			if reasonPtr != nil {
				reasonTime = reasonPtr.CreatedAt.Format("2006/01/02 15:04:05")
				reason = strutil.ToEmptyIfNil(reasonPtr.Reason)
				langKey, ok := value.DifferenceReasonClassConv[value.DifferenceReasonClass(reasonPtr.ReasonClass)]
				if ok {
					reasonClass = langutil.GetLangMap(config.DefaultLanguage).Print(string(langKey))
				}
			}

			var pclogStartTime, pclogEndTime string
			if len(mAtts) > 0 {
				pclogStartTime = mAtts[i].StartTime.Format("15:04")
				if mAtts[i].EndTime != nil {
					pclogEndTime = mAtts[i].EndTime.Format("15:04")
				}
			}
			tsEndTime := ""
			if tSheet.EndTime != nil {
				tsEndTime = (*tSheet.EndTime)[:5]
			}
			body := util.H{
				"attendance_date":      tSheet.TimesheetDate.Format("2006/01/02"),
				"member_code":          fmt.Sprintf("EmployeeCode%d", emp.EmployeeNo),
				"member_name":          fmt.Sprintf("Hoge%d Employee%d", emp.EmployeeNo, emp.EmployeeNo),
				"email":                fmt.Sprintf("<EMAIL>", emp.EmployeeNo),
				"title_name":           fmt.Sprintf("Title%d", emp.EmployeeNo),
				"contract_name":        fmt.Sprintf("Contract%d", emp.EmployeeNo),
				"customer_name":        fmt.Sprintf("Customer%d", emp.EmployeeNo),
				"department_code":      fmt.Sprintf("DepartmentCode%d", emp.EmployeeNo),
				"department_name":      fmt.Sprintf("Department%d", emp.EmployeeNo),
				"start_time":           (*tSheet.StartTime)[:5],
				"pclog_start_time":     pclogStartTime,
				"start_time_diff":      "00:00",
				"start_time_diff_flag": false,
				"end_time":             tsEndTime,
				"pclog_end_time":       pclogEndTime,
				"end_time_diff":        "00:00",
				"end_time_diff_flag":   false,
				"work_time":            "08:00",
				"pclog_work_time":      "08:00",
				"work_time_diff":       "00:00",
				"idle_time":            "01:00",
				"reas_reason_class":    reasonClass,
				"reas_reason":          reason,
				"reas_time":            reasonTime,
				"fixed_status":         true,
			}

			bodies = append(bodies, body)
		}
	}

	// Merge bodies to emptyBodies
	var retBodies []util.H
	for _, body := range emptyEodies {
		data := a.getRowFromCSVByEmailAndDate(body["email"].(string), body["attendance_date"].(string), bodies)
		if data != nil {
			retBodies = append(retBodies, data)
		} else {
			retBodies = append(retBodies, body)
		}
	}

	return retBodies
}

func (a *attendanceDifferencesExportTester) getRowFromCSVByEmailAndDate(
	email, date string, bodies []util.H) util.H {
	for _, body := range bodies {
		if body["email"] == email && body["attendance_date"] == date {
			return body
		}
	}
	return nil
}

func (a *attendanceDifferencesExportTester) getReasonByEmpIDAndDate(
	empID int,
	date time.Time,
	reasons []tbl.AttendanceDifferenceReason) *tbl.AttendanceDifferenceReason {

	for _, reason := range reasons {
		if reason.EmployeeNo == empID && reason.AttendanceDate.Equal(date) {
			return &reason
		}
	}
	return nil
}

func (a *attendanceDifferencesExportTester) genActLog(
	compID, userID int,
	accessInfo model.AccessInfo,
) []tbl.ActivityLog {
	actLog := tbl.ActivityLog{
		LogNo:       1,
		EmployeeNo:  userID,
		CompanyNo:   compID,
		FamilyName:  "Crowdlog",
		FirstName:   "Devs",
		Function:    string(config.AttendanceDifferenceExport),
		Description: "",
		Action:      string(config.Export),
		AccessType:  string(accessInfo.AccessType),
		IPAddress:   accessInfo.IPAddress,
	}
	return []tbl.ActivityLog{
		actLog,
	}
}

func (a *attendanceDifferencesExportTester) genIntegAttDiff(
	id int,
	compNo int,
	logMode int,
	thresholdMins int,
) tbl.IntegrationAttendanceDifference {
	return tbl.IntegrationAttendanceDifference{
		ID:            id,
		CompanyNo:     compNo,
		LogMode:       logMode,
		ThresholdMins: thresholdMins,
		CreatedAt:     util.DateToTime("2024-10-10"),
		UpdatedAt:     util.DateToTime("2024-10-10"),
	}
}

func (a *attendanceDifferencesExportTester) dbAssert(
	t *testing.T,
	db *gorm.DB,
	expActLogs []tbl.ActivityLog,
) {
	var actActLogs []tbl.ActivityLog
	db.Find(&actActLogs)
	assertmodel.ActivityLogSlice(t, expActLogs, actActLogs)
}

func (a *attendanceDifferencesExportTester) ExecExportTest(
	t *testing.T,
	compNo, userID int,
	cases []attendanceDifferencesExportTcase,
) {
	var ctxDB idb.DB
	ctxDB.Logger = ilog.NewDBlogger(&ilog.InpmLogger{})
	cdb, err := ctxDB.ConnectDb("common_db")
	if err != nil {
		t.Fatalf("failed to connect db: %v", err)
	}
	mdb, err := ctxDB.ConnectDb("db01")
	if err != nil {
		t.Fatalf("failed to connect db: %v", err)
	}

	defer func() {
		if sql, err := mdb.DB(); err == nil {
			_ = sql.Close()
		}
		if sql, err := cdb.DB(); err == nil {
			_ = sql.Close()
		}
	}()

	for _, cs := range cases {
		t.Run(
			cs.desc,
			func(t *testing.T) {
				ht := &test.ExportHandlerTester{
					CleanDB: true,
				}

				if cs.mrr != nil {
					ht.MRR = cs.mrr
				}

				if cs.userSetting != nil {
					ht.UserSetting = cs.userSetting
				}

				if cs.accessInfo != nil {
					ht.AccessInfo = cs.accessInfo
				}

				dbData := []any{
					cs.preCompany,
					cs.preEmployees,
					cs.preTitles,
					cs.preContracts,
					cs.preCustomers,
					cs.preDepartments,
					cs.preTSheets,
					cs.preTSheetRests,
					cs.preMAtts,
					cs.preAttDiffReasons,
					cs.preIntegAttDiff,
					[]tbl.ActivityLog{},
				}

				ht.ExecHandlerTest(
					cdb,
					mdb,
					compNo,
					userID,
					cs.reqObj,
					cs.resObj,
					t,
					cs.fn,
					dbData,
				)

				a.dbAssert(t, mdb, cs.expActLogs)
			},
		)
	}
}

func Test_AttendanceDifference_Export(t *testing.T) {
	compNo, userID, recID := 1, 1, 1

	baseURL := "/exports/attendances/differences/"
	duration := "since=2024-08-01&until=2024-08-31"
	url := fmt.Sprintf("%s?%s", baseURL, duration)

	csvHeader := []string{
		"attendance_date",
		"member_code",
		"member_name",
		"email",
		"title_name",
		"contract_name",
		"customer_name",
		"department_code",
		"department_name",
		"start_time",
		"pclog_start_time",
		"start_time_diff",
		"start_time_diff_flag",
		"end_time",
		"pclog_end_time",
		"end_time_diff",
		"end_time_diff_flag",
		"work_time",
		"pclog_work_time",
		"work_time_diff",
		"idle_time",
		"reas_reason_class",
		"reas_reason",
		"reas_time",
		"fixed_status",
	}

	// csvHeaderJa := []string{
	// 	"日付",
	// 	"社員コード",
	// 	"社員名",
	// 	"メールアドレス",
	// 	"メールアドレス",
	// 	"雇用形態",
	// 	"所属",
	// 	"部署コード",
	// 	"部署名",
	// 	"開始時刻",
	// 	"PCログ開始時刻",
	// 	"開始時刻乖離時間",
	// 	"開始時刻乖離フラグ",
	// 	"終了時刻",
	// 	"PCログ終了時刻",
	// 	"終了時刻乖離時間",
	// 	"終了時刻乖離フラグ",
	// 	"労働時間",
	// 	"PCログ労働時間",
	// 	"労働時間乖離時間",
	// 	"無操作時間",
	// 	"乖離理由分類",
	// 	"乖離理由",
	// 	"乖離理由登録日時",
	// 	"確定済み",
	// }

	at := &attendanceDifferencesExportTester{}
	h := NewAttendanceDifferencesHandler()

	integAttDiffs := []tbl.IntegrationAttendanceDifference{
		at.genIntegAttDiff(recID, compNo, value.TKLogPCMode, 30),
	}

	startTime := util.DateToTime("2024-08-01")
	endTime := util.DateToTime("2024-08-31")

	// CASE1
	// 1 data, no diffs
	preCompany1 := at.genCompany(compNo)
	preEmps1 := at.genEmployee(compNo, 1)
	preTitles1 := at.genTitles(compNo, 1)
	preContracts1 := at.genContracts(compNo, 1)
	preCustomers1 := at.genCustomers(compNo, 1)
	preDepartments1 := at.genDepartments(compNo, 1)
	preTSheets1 := at.genTimesheets(preEmps1[0], 1)
	preRests1 := at.genRests(preTSheets1)
	preMAtts1 := at.genMeasuredAttendances(compNo, preEmps1[0], 1)
	preReasons1 := at.genReasons(compNo, preEmps1, 1, int(value.DifferenceReasonClassOthers))

	expBody1 := at.genBodies(startTime, endTime, preEmps1, preTSheets1, preMAtts1, preReasons1)
	expContent1 := test.NewexportTestContent("attendance_difference", csvHeader, expBody1)
	userSetting1 := test.CreateDefaultUserInfo()

	q1 := xurl.Values{}
	q1.Add("file_type", "json")
	q1.Add("char_code", "utf8")
	uri1 := fmt.Sprintf("%s&%s", url, q1.Encode())

	case1 := attendanceDifferencesExportTcase{
		desc: "CASE1: 1 data, no diffs",

		preCompany:        preCompany1,
		preEmployees:      preEmps1,
		preTitles:         preTitles1,
		preContracts:      preContracts1,
		preCustomers:      preCustomers1,
		preDepartments:    preDepartments1,
		preTSheets:        preTSheets1,
		preTSheetRests:    preRests1,
		preMAtts:          preMAtts1,
		preAttDiffReasons: preReasons1,
		preIntegAttDiff:   integAttDiffs,

		reqObj: test.HandlerReq{
			Url:    uri1,
			Method: http.MethodGet,
		},
		resObj: test.ExportHandlerRes{
			Res:    expContent1,
			ErrStr: nil,
		},
		fn:          h.Export,
		userSetting: &userSetting1,
	}

	// CASE2
	// 30 mins difference: start time
	days2 := 2
	preCompany2 := at.genCompany(compNo)
	preEmps2 := at.genEmployee(compNo, 1)
	preTitles2 := at.genTitles(compNo, 1)
	preContracts2 := at.genContracts(compNo, 1)
	preCustomers2 := at.genCustomers(compNo, 1)
	preDepartments2 := at.genDepartments(compNo, 1)

	preTSheets2 := at.genTimesheets(preEmps2[0], days2)
	preRests2 := at.genRests(preTSheets2)

	preMAtts2 := at.genMeasuredAttendances(compNo, preEmps2[0], days2)
	preMAtts2[0].StartTime = preMAtts2[0].StartTime.Add(time.Minute * 30)

	preReasons2 := at.genReasons(compNo, preEmps2, days2, int(value.DifferenceReasonClassOthers))

	expBody2 := at.genBodies(startTime, endTime, preEmps2, preTSheets2, preMAtts2, preReasons2)
	expBody2[0]["start_time_diff"] = "00:30"
	expBody2[0]["start_time_diff_flag"] = true
	expBody2[0]["pclog_work_time"] = "07:30"
	expBody2[0]["work_time_diff"] = "00:30"
	expContent2 := test.NewexportTestContent("attendance_difference", csvHeader, expBody2)

	q2 := xurl.Values{}
	q2.Add("file_type", "json")
	q2.Add("char_code", "utf8")
	uri2 := fmt.Sprintf("%s&%s", url, q2.Encode())

	case2 := attendanceDifferencesExportTcase{
		desc: "CASE2: start time diff 30 mins",

		preCompany:        preCompany2,
		preEmployees:      preEmps2,
		preTitles:         preTitles2,
		preContracts:      preContracts2,
		preCustomers:      preCustomers2,
		preDepartments:    preDepartments2,
		preTSheets:        preTSheets2,
		preTSheetRests:    preRests2,
		preMAtts:          preMAtts2,
		preAttDiffReasons: preReasons2,
		preIntegAttDiff:   integAttDiffs,

		reqObj: test.HandlerReq{
			Url:    uri2,
			Method: http.MethodGet,
		},
		resObj: test.ExportHandlerRes{
			Res:    expContent2,
			ErrStr: nil,
		},
		fn: h.Export,
	}

	// CASE3
	// 30 mins difference: end time
	days3 := 2
	preCompany3 := at.genCompany(compNo)
	preEmps3 := at.genEmployee(compNo, 1)
	preTitles3 := at.genTitles(compNo, 1)
	preContracts3 := at.genContracts(compNo, 1)
	preCustomers3 := at.genCustomers(compNo, 1)
	preDepartments3 := at.genDepartments(compNo, 1)

	preTSheets3 := at.genTimesheets(preEmps3[0], days3)
	preRests3 := at.genRests(preTSheets3)

	preMAtts3 := at.genMeasuredAttendances(compNo, preEmps3[0], days3)
	preMAtts3[0].EndTime = test.NewPtr(preMAtts3[0].EndTime.Add(time.Minute * 30)).(*time.Time)

	preReasons3 := at.genReasons(compNo, preEmps3, days3, int(value.DifferenceReasonClassOthers))

	expBody3 := at.genBodies(startTime, endTime, preEmps3, preTSheets3, preMAtts3, preReasons3)
	expBody3[0]["end_time_diff"] = "00:30"
	expBody3[0]["end_time_diff_flag"] = true
	expBody3[0]["pclog_work_time"] = "08:30"
	expBody3[0]["work_time_diff"] = "00:30"
	expContent3 := test.NewexportTestContent("attendance_difference", csvHeader, expBody3)

	q3 := xurl.Values{}
	q3.Add("file_type", "json")
	q3.Add("char_code", "utf8")
	uri3 := fmt.Sprintf("%s&%s", url, q3.Encode())

	case3 := attendanceDifferencesExportTcase{
		desc: "CASE3: end time diff 30 mins",

		preCompany:        preCompany3,
		preEmployees:      preEmps3,
		preTitles:         preTitles3,
		preContracts:      preContracts3,
		preCustomers:      preCustomers3,
		preDepartments:    preDepartments3,
		preTSheets:        preTSheets3,
		preTSheetRests:    preRests3,
		preMAtts:          preMAtts3,
		preAttDiffReasons: preReasons3,
		preIntegAttDiff:   integAttDiffs,

		reqObj: test.HandlerReq{
			Url:    uri3,
			Method: http.MethodGet,
		},
		resObj: test.ExportHandlerRes{
			Res:    expContent3,
			ErrStr: nil,
		},
		fn: h.Export,
	}

	// CASE4
	// 480 mins difference: start time
	days4 := 2
	preCompany4 := at.genCompany(compNo)
	preEmps4 := at.genEmployee(compNo, 1)
	preTitles4 := at.genTitles(compNo, 1)
	preContracts4 := at.genContracts(compNo, 1)
	preCustomers4 := at.genCustomers(compNo, 1)
	preDepartments4 := at.genDepartments(compNo, 1)

	preTSheets4 := at.genTimesheets(preEmps4[0], days4)
	preRests4 := at.genRests(preTSheets4)

	preMAtts4 := at.genMeasuredAttendances(compNo, preEmps4[0], days4)
	preMAtts4[0].StartTime = preMAtts4[0].StartTime.Add(time.Minute * 480)

	preReasons4 := at.genReasons(compNo, preEmps4, days4, int(value.DifferenceReasonClassOthers))

	expBody4 := at.genBodies(startTime, endTime, preEmps4, preTSheets4, preMAtts4, preReasons4)
	expBody4[0]["start_time_diff"] = "08:00"
	expBody4[0]["start_time_diff_flag"] = true
	expBody4[0]["pclog_work_time"] = "00:00"
	expBody4[0]["work_time_diff"] = "08:00"
	expContent4 := test.NewexportTestContent("attendance_difference", csvHeader, expBody4)

	q4 := xurl.Values{}
	q4.Add("file_type", "json")
	q4.Add("char_code", "utf8")
	uri4 := fmt.Sprintf("%s&%s", url, q4.Encode())

	case4 := attendanceDifferencesExportTcase{
		desc: "CASE4: start time diff 480 mins",

		preCompany:        preCompany4,
		preEmployees:      preEmps4,
		preTitles:         preTitles4,
		preContracts:      preContracts4,
		preCustomers:      preCustomers4,
		preDepartments:    preDepartments4,
		preTSheets:        preTSheets4,
		preTSheetRests:    preRests4,
		preMAtts:          preMAtts4,
		preAttDiffReasons: preReasons4,
		preIntegAttDiff: []tbl.IntegrationAttendanceDifference{
			at.genIntegAttDiff(recID, compNo, value.TKLogPCMode, 480),
		},

		reqObj: test.HandlerReq{
			Url:    uri4,
			Method: http.MethodGet,
		},
		resObj: test.ExportHandlerRes{
			Res:    expContent4,
			ErrStr: nil,
		},
		fn: h.Export,
	}

	// CASE5
	// 480 mins difference: end time
	days5 := 2
	preCompany5 := at.genCompany(compNo)
	preEmps5 := at.genEmployee(compNo, 1)
	preTitles5 := at.genTitles(compNo, 1)
	preContracts5 := at.genContracts(compNo, 1)
	preCustomers5 := at.genCustomers(compNo, 1)
	preDepartments5 := at.genDepartments(compNo, 1)

	preTSheets5 := at.genTimesheets(preEmps5[0], days5)
	preRests5 := at.genRests(preTSheets5)

	preMAtts5 := at.genMeasuredAttendances(compNo, preEmps5[0], days5)
	preMAtts5[0].EndTime = test.NewPtr(preMAtts5[0].EndTime.Add(time.Minute * 480)).(*time.Time)

	preReasons5 := at.genReasons(compNo, preEmps5, days5, int(value.DifferenceReasonClassOthers))

	expBody5 := at.genBodies(startTime, endTime, preEmps5, preTSheets5, preMAtts5, preReasons5)
	expBody5[0]["end_time_diff"] = "08:00"
	expBody5[0]["end_time_diff_flag"] = true
	expBody5[0]["pclog_end_time"] = "25:00"
	expBody5[0]["pclog_work_time"] = "16:00"
	expBody5[0]["work_time_diff"] = "08:00"
	expContent5 := test.NewexportTestContent("attendance_difference", csvHeader, expBody5)

	q5 := xurl.Values{}
	q5.Add("file_type", "json")
	q5.Add("char_code", "utf8")
	uri5 := fmt.Sprintf("%s&%s", url, q5.Encode())

	case5 := attendanceDifferencesExportTcase{
		desc: "CASE5: end time diff 480 mins",

		preCompany:        preCompany5,
		preEmployees:      preEmps5,
		preTitles:         preTitles5,
		preContracts:      preContracts5,
		preCustomers:      preCustomers5,
		preDepartments:    preDepartments5,
		preTSheets:        preTSheets5,
		preTSheetRests:    preRests5,
		preMAtts:          preMAtts5,
		preAttDiffReasons: preReasons5,
		preIntegAttDiff: []tbl.IntegrationAttendanceDifference{
			at.genIntegAttDiff(recID, compNo, value.TKLogPCMode, 480),
		},

		reqObj: test.HandlerReq{
			Url:    uri5,
			Method: http.MethodGet,
		},
		resObj: test.ExportHandlerRes{
			Res:    expContent5,
			ErrStr: nil,
		},
		fn: h.Export,
	}

	// CASE6
	// Attendance's start time within today, end time within today with difference threshold 60 mins
	days6 := 3
	preCompany6 := at.genCompany(compNo)
	preEmps6 := at.genEmployee(compNo, 1)
	preTitles6 := at.genTitles(compNo, 1)
	preContracts6 := at.genContracts(compNo, 1)
	preCustomers6 := at.genCustomers(compNo, 1)
	preDepartments6 := at.genDepartments(compNo, 1)

	preTSheets6 := at.genTimesheets(preEmps6[0], days6)
	preRests6 := at.genRests(preTSheets6)

	preMAtts6 := at.genMeasuredAttendances(compNo, preEmps6[0], days6)
	preMAtts6[0].StartTime = util.DateToTime("2024-08-01 08:30:00")
	preMAtts6[0].EndTime = test.NewPtr(util.DateToTime("2024-08-01 17:30:00")).(*time.Time)
	preMAtts6[1].StartTime = util.DateToTime("2024-08-02 19:00:00")
	preMAtts6[1].EndTime = test.NewPtr(util.DateToTime("2024-08-03 01:00:00")).(*time.Time)
	preMAtts6[2].StartTime = util.DateToTime("2024-08-04 01:00:00")
	preMAtts6[2].EndTime = test.NewPtr(util.DateToTime("2024-08-04 04:00:00")).(*time.Time)

	preReasons6 := at.genReasons(compNo, preEmps6, days6, int(value.DifferenceReasonClassPcUsedNotForWork))

	expBody6 := at.genBodies(startTime, endTime, preEmps6, preTSheets6, preMAtts6, preReasons6)
	expBody6[0]["start_time_diff"] = "00:30"
	expBody6[0]["start_time_diff_flag"] = true
	expBody6[0]["end_time_diff"] = "00:30"
	expBody6[0]["end_time_diff_flag"] = true
	expBody6[0]["pclog_work_time"] = "08:00"
	expBody6[0]["work_time_diff"] = "00:00"

	expBody6[1]["start_time_diff"] = "11:00"
	expBody6[1]["start_time_diff_flag"] = true
	expBody6[1]["end_time_diff"] = "08:00"
	expBody6[1]["end_time_diff_flag"] = true
	expBody6[1]["pclog_work_time"] = "05:00"
	expBody6[1]["pclog_end_time"] = "25:00"
	expBody6[1]["work_time_diff"] = "03:00"

	expBody6[2]["start_time_diff"] = "17:00"
	expBody6[2]["start_time_diff_flag"] = true
	expBody6[2]["end_time_diff"] = "11:00"
	expBody6[2]["end_time_diff_flag"] = true
	expBody6[2]["pclog_work_time"] = "02:00"
	expBody6[2]["pclog_start_time"] = "25:00"
	expBody6[2]["pclog_end_time"] = "28:00"
	expBody6[2]["work_time_diff"] = "06:00"

	expContent6 := test.NewexportTestContent("attendance_difference", csvHeader, expBody6)

	q6 := xurl.Values{}
	q6.Add("file_type", "json")
	q6.Add("char_code", "utf8")
	uri6 := fmt.Sprintf("%s&%s", url, q6.Encode())

	case6 := attendanceDifferencesExportTcase{
		desc: "CASE6: Attendance's start time within today, end time within today with difference threshold 30 mins",

		preCompany:        preCompany6,
		preEmployees:      preEmps6,
		preTitles:         preTitles6,
		preContracts:      preContracts6,
		preCustomers:      preCustomers6,
		preDepartments:    preDepartments6,
		preTSheets:        preTSheets6,
		preTSheetRests:    preRests6,
		preMAtts:          preMAtts6,
		preAttDiffReasons: preReasons6,
		preIntegAttDiff:   integAttDiffs,

		reqObj: test.HandlerReq{
			Url:    uri6,
			Method: http.MethodGet,
		},
		resObj: test.ExportHandlerRes{
			Res:    expContent6,
			ErrStr: nil,
		},
		fn: h.Export,
	}

	// CASE7
	// Attendance's start time within today, end time within next day with difference threshold 30 mins
	days7 := 3
	preCompany7 := at.genCompany(compNo)
	preEmps7 := at.genEmployee(compNo, 1)
	preTitles7 := at.genTitles(compNo, 1)
	preContracts7 := at.genContracts(compNo, 1)
	preCustomers7 := at.genCustomers(compNo, 1)
	preDepartments7 := at.genDepartments(compNo, 1)

	preTSheets7 := at.genTimesheets(preEmps7[0], days7)
	preTSheets7[0].StartTime = strutil.ToPtr("21:00:00")
	preTSheets7[0].EndTime = strutil.ToPtr("25:00:00")
	preTSheets7[1].StartTime = strutil.ToPtr("21:00:00")
	preTSheets7[1].EndTime = strutil.ToPtr("25:00:00")
	preTSheets7[2].StartTime = strutil.ToPtr("21:00:00")
	preTSheets7[2].EndTime = strutil.ToPtr("25:00:00")

	preRests7 := at.genRests(preTSheets7)
	preRests7[0].StartTime = strutil.ToPtr("22:00:00")
	preRests7[0].EndTime = strutil.ToPtr("23:00:00")
	preRests7[1].StartTime = strutil.ToPtr("22:00:00")
	preRests7[1].EndTime = strutil.ToPtr("23:00:00")
	preRests7[2].StartTime = strutil.ToPtr("22:00:00")
	preRests7[2].EndTime = strutil.ToPtr("23:00:00")

	preMAtts7 := at.genMeasuredAttendances(compNo, preEmps7[0], days7)
	preMAtts7[0].StartTime = util.DateToTime("2024-08-01 08:00:00")
	preMAtts7[0].EndTime = test.NewPtr(util.DateToTime("2024-08-01 17:00:00")).(*time.Time)
	preMAtts7[1].StartTime = util.DateToTime("2024-08-02 19:00:00")
	preMAtts7[1].EndTime = test.NewPtr(util.DateToTime("2024-08-03 01:30:00")).(*time.Time)
	preMAtts7[2].StartTime = util.DateToTime("2024-08-04 00:30:00")
	preMAtts7[2].EndTime = test.NewPtr(util.DateToTime("2024-08-04 03:00:00")).(*time.Time)

	preReasons7 := at.genReasons(compNo, preEmps7, days7, int(value.DifferenceReasonClassPcUsedNotForWork))

	expBody7 := at.genBodies(startTime, endTime, preEmps7, preTSheets7, preMAtts7, preReasons7)
	expBody7[0]["start_time_diff"] = "13:00"
	expBody7[0]["start_time_diff_flag"] = true
	expBody7[0]["end_time_diff"] = "08:00"
	expBody7[0]["end_time_diff_flag"] = true
	expBody7[0]["work_time"] = "03:00"
	expBody7[0]["pclog_work_time"] = "08:00"
	expBody7[0]["work_time_diff"] = "05:00"

	expBody7[1]["start_time_diff"] = "02:00"
	expBody7[1]["start_time_diff_flag"] = true
	expBody7[1]["end_time_diff"] = "00:30"
	expBody7[1]["end_time_diff_flag"] = true
	expBody7[1]["pclog_end_time"] = "25:30"
	expBody7[1]["pclog_work_time"] = "05:30"
	expBody7[1]["work_time"] = "03:00"
	expBody7[1]["work_time_diff"] = "02:30"

	expBody7[2]["start_time_diff"] = "03:30"
	expBody7[2]["start_time_diff_flag"] = true
	expBody7[2]["end_time_diff"] = "02:00"
	expBody7[2]["end_time_diff_flag"] = true
	expBody7[2]["pclog_start_time"] = "24:30"
	expBody7[2]["pclog_end_time"] = "27:00"
	expBody7[2]["pclog_work_time"] = "01:30"
	expBody7[2]["work_time"] = "03:00"
	expBody7[2]["work_time_diff"] = "01:30"

	expContent7 := test.NewexportTestContent("attendance_difference", csvHeader, expBody7)

	q7 := xurl.Values{}
	q7.Add("file_type", "json")
	q7.Add("char_code", "utf8")
	uri7 := fmt.Sprintf("%s&%s", url, q7.Encode())

	case7 := attendanceDifferencesExportTcase{
		desc: "CASE7: Attendance's start time within today, end time within next day with difference threshold 30 mins",

		preCompany:        preCompany7,
		preEmployees:      preEmps7,
		preTitles:         preTitles7,
		preContracts:      preContracts7,
		preCustomers:      preCustomers7,
		preDepartments:    preDepartments7,
		preTSheets:        preTSheets7,
		preTSheetRests:    preRests7,
		preMAtts:          preMAtts7,
		preAttDiffReasons: preReasons7,
		preIntegAttDiff:   integAttDiffs,

		reqObj: test.HandlerReq{
			Url:    uri7,
			Method: http.MethodGet,
		},
		resObj: test.ExportHandlerRes{
			Res:    expContent7,
			ErrStr: nil,
		},
		fn: h.Export,
	}

	// CASE8
	// Attendance's start time within next day, end time within next day with difference threshold 30 mins
	days8 := 3
	preCompany8 := at.genCompany(compNo)
	preEmps8 := at.genEmployee(compNo, 1)
	preTitles8 := at.genTitles(compNo, 1)
	preContracts8 := at.genContracts(compNo, 1)
	preCustomers8 := at.genCustomers(compNo, 1)
	preDepartments8 := at.genDepartments(compNo, 1)

	preTSheets8 := at.genTimesheets(preEmps8[0], days8)
	preTSheets8[0].StartTime = strutil.ToPtr("25:00:00")
	preTSheets8[0].EndTime = strutil.ToPtr("28:00:00")
	preTSheets8[1].StartTime = strutil.ToPtr("25:00:00")
	preTSheets8[1].EndTime = strutil.ToPtr("28:00:00")
	preTSheets8[2].StartTime = strutil.ToPtr("25:00:00")
	preTSheets8[2].EndTime = strutil.ToPtr("28:00:00")

	preRests8 := at.genRests(preTSheets8)
	preRests8[0].StartDate = test.NewPtr(util.DateToTime("2024-08-02 00:00:00")).(*time.Time)
	preRests8[0].StartTime = strutil.ToPtr("02:00:00")
	preRests8[0].EndDate = test.NewPtr(util.DateToTime("2024-08-02 00:00:00")).(*time.Time)
	preRests8[0].EndTime = strutil.ToPtr("03:00:00")
	preRests8[1].StartDate = test.NewPtr(util.DateToTime("2024-08-03 00:00:00")).(*time.Time)
	preRests8[1].StartTime = strutil.ToPtr("02:00:00")
	preRests8[1].EndDate = test.NewPtr(util.DateToTime("2024-08-03 00:00:00")).(*time.Time)
	preRests8[1].EndTime = strutil.ToPtr("03:00:00")
	preRests8[2].StartDate = test.NewPtr(util.DateToTime("2024-08-04 00:00:00")).(*time.Time)
	preRests8[2].StartTime = strutil.ToPtr("02:00:00")
	preRests8[2].EndDate = test.NewPtr(util.DateToTime("2024-08-04 00:00:00")).(*time.Time)
	preRests8[2].EndTime = strutil.ToPtr("03:00:00")

	preMAtts8 := at.genMeasuredAttendances(compNo, preEmps8[0], days8)
	preMAtts8[0].StartTime = util.DateToTime("2024-08-01 08:00:00")
	preMAtts8[0].EndTime = test.NewPtr(util.DateToTime("2024-08-01 17:00:00")).(*time.Time)
	preMAtts8[1].StartTime = util.DateToTime("2024-08-02 19:00:00")
	preMAtts8[1].EndTime = test.NewPtr(util.DateToTime("2024-08-03 01:00:00")).(*time.Time)
	preMAtts8[2].StartTime = util.DateToTime("2024-08-04 01:00:00")
	preMAtts8[2].EndTime = test.NewPtr(util.DateToTime("2024-08-04 03:00:00")).(*time.Time)

	preReasons8 := at.genReasons(compNo, preEmps8, days8, int(value.DifferenceReasonClassPcUsedNotForWork))

	expBody8 := at.genBodies(startTime, endTime, preEmps8, preTSheets8, preMAtts8, preReasons8)
	expBody8[0]["start_time_diff"] = "17:00"
	expBody8[0]["start_time_diff_flag"] = true
	expBody8[0]["end_time_diff"] = "11:00"
	expBody8[0]["end_time_diff_flag"] = true
	expBody8[0]["work_time"] = "02:00"
	expBody8[0]["work_time_diff"] = "06:00"

	expBody8[1]["start_time_diff"] = "06:00"
	expBody8[1]["start_time_diff_flag"] = true
	expBody8[1]["end_time_diff"] = "03:00"
	expBody8[1]["end_time_diff_flag"] = true
	expBody8[1]["pclog_start_time"] = "19:00"
	expBody8[1]["pclog_end_time"] = "25:00"
	expBody8[1]["work_time"] = "02:00"
	expBody8[1]["pclog_work_time"] = "05:00"
	expBody8[1]["work_time_diff"] = "03:00"

	expBody8[2]["start_time_diff"] = "00:00"
	expBody8[2]["start_time_diff_flag"] = false
	expBody8[2]["end_time_diff"] = "01:00"
	expBody8[2]["end_time_diff_flag"] = true
	expBody8[2]["pclog_start_time"] = "25:00"
	expBody8[2]["pclog_end_time"] = "27:00"
	expBody8[2]["work_time"] = "02:00"
	expBody8[2]["pclog_work_time"] = "01:00"
	expBody8[2]["work_time_diff"] = "01:00"

	expContent8 := test.NewexportTestContent("attendance_difference", csvHeader, expBody8)

	q8 := xurl.Values{}
	q8.Add("file_type", "json")
	q8.Add("char_code", "utf8")
	uri8 := fmt.Sprintf("%s&%s", url, q8.Encode())

	case8 := attendanceDifferencesExportTcase{
		desc: "CASE8: Attendance's start time within next day, end time within next day with difference threshold 30 mins",

		preCompany:        preCompany8,
		preEmployees:      preEmps8,
		preTitles:         preTitles8,
		preContracts:      preContracts8,
		preCustomers:      preCustomers8,
		preDepartments:    preDepartments8,
		preTSheets:        preTSheets8,
		preTSheetRests:    preRests8,
		preMAtts:          preMAtts8,
		preAttDiffReasons: preReasons8,
		preIntegAttDiff:   integAttDiffs,

		reqObj: test.HandlerReq{
			Url:    uri8,
			Method: http.MethodGet,
		},
		resObj: test.ExportHandlerRes{
			Res:    expContent8,
			ErrStr: nil,
		},
		fn: h.Export,
	}

	// CASE10
	// filter by title
	preCompany10 := at.genCompany(compNo)
	preEmps10 := at.genEmployee(compNo, 2)
	preEmps10[1].TitleNo = test.NewPtr(2).(*int)

	preTitles10 := at.genTitles(compNo, 2)
	preContracts10 := at.genContracts(compNo, 1)
	preCustomers10 := at.genCustomers(compNo, 1)
	preDepartments10 := at.genDepartments(compNo, 1)

	tSheet101 := at.genTimesheets(preEmps10[0], 1)
	tSheet102 := at.genTimesheets(preEmps10[1], 1)
	preTSheets10 := append(tSheet101, tSheet102...)

	preRests10 := at.genRests(preTSheets10)

	mAtts101 := at.genMeasuredAttendances(compNo, preEmps10[0], 1)
	mAtts102 := at.genMeasuredAttendances(compNo, preEmps10[1], 1)
	preMAtts10 := append(mAtts101, mAtts102...)

	preReasons10 := at.genReasons(compNo, preEmps10, 1, int(value.DifferenceReasonClassPcUsedNotForWork))

	expBody10 := at.genBodies(startTime, endTime, preEmps10, preTSheets10, preMAtts10, preReasons10)
	expContent10 := test.NewexportTestContent("attendance_difference", csvHeader, expBody10[0:31])

	q10 := xurl.Values{}
	q10.Add("file_type", "json")
	q10.Add("char_code", "utf8")
	q10.Add("title_ids", "1")
	uri10 := fmt.Sprintf("%s&%s", url, q10.Encode())

	case10 := attendanceDifferencesExportTcase{
		desc: "CASE10: filter by title",

		preCompany:        preCompany10,
		preEmployees:      preEmps10,
		preTitles:         preTitles10,
		preContracts:      preContracts10,
		preCustomers:      preCustomers10,
		preDepartments:    preDepartments10,
		preTSheets:        preTSheets10,
		preTSheetRests:    preRests10,
		preMAtts:          preMAtts10,
		preAttDiffReasons: preReasons10,
		preIntegAttDiff:   integAttDiffs,

		reqObj: test.HandlerReq{
			Url:    uri10,
			Method: http.MethodGet,
		},
		resObj: test.ExportHandlerRes{
			Res:    expContent10,
			ErrStr: nil,
		},
		fn: h.Export,
	}

	// CASE11
	// filter by contract
	preCompany11 := at.genCompany(compNo)
	preEmps11 := at.genEmployee(compNo, 2)
	preEmps11[1].ContractNo = test.NewPtr(2).(*int)

	preTitles11 := at.genTitles(compNo, 1)
	preContracts11 := at.genContracts(compNo, 2)
	preCustomers11 := at.genCustomers(compNo, 1)
	preDepartments11 := at.genDepartments(compNo, 1)

	tSheet111 := at.genTimesheets(preEmps11[0], 1)
	tSheet112 := at.genTimesheets(preEmps11[1], 1)
	preTSheets11 := append(tSheet111, tSheet112...)

	preRests11 := at.genRests(preTSheets11)

	mAtts111 := at.genMeasuredAttendances(compNo, preEmps11[0], 1)
	mAtts112 := at.genMeasuredAttendances(compNo, preEmps11[1], 1)
	preMAtts11 := append(mAtts111, mAtts112...)

	preReasons11 := at.genReasons(compNo, preEmps11, 1, int(value.DifferenceReasonClassPcUsedNotForWork))

	expBody11 := at.genBodies(startTime, endTime, preEmps11, preTSheets11, preMAtts11, preReasons11)
	expContent11 := test.NewexportTestContent("attendance_difference", csvHeader, expBody11[0:31])

	q11 := xurl.Values{}
	q11.Add("file_type", "json")
	q11.Add("char_code", "utf8")
	q11.Add("contract_ids", "1")
	uri11 := fmt.Sprintf("%s&%s", url, q11.Encode())

	case11 := attendanceDifferencesExportTcase{
		desc: "CASE11: filter by contract",

		preCompany:        preCompany11,
		preEmployees:      preEmps11,
		preTitles:         preTitles11,
		preContracts:      preContracts11,
		preCustomers:      preCustomers11,
		preDepartments:    preDepartments11,
		preTSheets:        preTSheets11,
		preTSheetRests:    preRests11,
		preMAtts:          preMAtts11,
		preAttDiffReasons: preReasons11,
		preIntegAttDiff:   integAttDiffs,

		reqObj: test.HandlerReq{
			Url:    uri11,
			Method: http.MethodGet,
		},
		resObj: test.ExportHandlerRes{
			Res:    expContent11,
			ErrStr: nil,
		},
		fn: h.Export,
	}

	// CASE12
	// filter by department
	preCompany12 := at.genCompany(compNo)
	preEmps12 := at.genEmployee(compNo, 2)
	preEmps12[1].DepartmentNo = test.NewPtr(2).(*int)

	preTitles12 := at.genTitles(compNo, 1)
	preContracts12 := at.genContracts(compNo, 1)
	preCustomers12 := at.genCustomers(compNo, 1)
	preDepartments12 := at.genDepartments(compNo, 2)

	tSheet121 := at.genTimesheets(preEmps12[0], 1)
	tSheet122 := at.genTimesheets(preEmps12[1], 1)
	preTSheets12 := append(tSheet121, tSheet122...)

	preRests12 := at.genRests(preTSheets11)

	mAtts121 := at.genMeasuredAttendances(compNo, preEmps12[0], 1)
	mAtts122 := at.genMeasuredAttendances(compNo, preEmps12[1], 1)
	preMAtts12 := append(mAtts121, mAtts122...)

	preReasons12 := at.genReasons(compNo, preEmps12, 1, int(value.DifferenceReasonClassPcUnnecessarily))

	expBody12 := at.genBodies(startTime, endTime, preEmps12, preTSheets12, preMAtts12, preReasons12)
	expContent12 := test.NewexportTestContent("attendance_difference", csvHeader, expBody12[0:31])

	q12 := xurl.Values{}
	q12.Add("file_type", "json")
	q12.Add("char_code", "utf8")
	q12.Add("department_id", "1")
	uri12 := fmt.Sprintf("%s&%s", url, q12.Encode())

	case12 := attendanceDifferencesExportTcase{
		desc: "CASE12: filter by department",

		preCompany:        preCompany12,
		preEmployees:      preEmps12,
		preTitles:         preTitles12,
		preContracts:      preContracts12,
		preCustomers:      preCustomers12,
		preDepartments:    preDepartments12,
		preTSheets:        preTSheets12,
		preTSheetRests:    preRests12,
		preMAtts:          preMAtts12,
		preAttDiffReasons: preReasons12,
		preIntegAttDiff:   integAttDiffs,

		reqObj: test.HandlerReq{
			Url:    uri12,
			Method: http.MethodGet,
		},
		resObj: test.ExportHandlerRes{
			Res:    expContent12,
			ErrStr: nil,
		},
		fn: h.Export,
	}

	// CASE13
	// no employee
	preCompany13 := at.genCompany(compNo)
	expContent13 := test.NewexportTestContent("attendance_difference", csvHeader, []util.H{})

	q13 := xurl.Values{}
	q13.Add("file_type", "json")
	q13.Add("char_code", "utf8")
	uri13 := fmt.Sprintf("%s&%s", url, q12.Encode())

	case13 := attendanceDifferencesExportTcase{
		desc: "CASE13: no employee",

		preCompany:      preCompany13,
		preIntegAttDiff: integAttDiffs,

		reqObj: test.HandlerReq{
			Url:    uri13,
			Method: http.MethodGet,
		},
		resObj: test.ExportHandlerRes{
			Res:    expContent13,
			ErrStr: nil,
		},
		fn: h.Export,
	}

	// CASE14
	// sort: attendance_date, asc
	days14 := 2
	preCompany14 := at.genCompany(compNo)
	preEmps14 := at.genEmployee(compNo, 1)
	preTitles14 := at.genTitles(compNo, 1)
	preContracts14 := at.genContracts(compNo, 1)
	preCustomers14 := at.genCustomers(compNo, 1)
	preDepartments14 := at.genDepartments(compNo, 1)

	preTSheets14 := at.genTimesheets(preEmps14[0], days14)
	preRests14 := at.genRests(preTSheets14)

	preMAtts14 := at.genMeasuredAttendances(compNo, preEmps14[0], days14)
	preReasons14 := at.genReasons(compNo, preEmps14, days14, int(value.DifferenceReasonClassPcUnnecessarily))

	expBody14 := at.genBodies(startTime, endTime, preEmps14, preTSheets14, preMAtts14, preReasons14)
	expContent14 := test.NewexportTestContent("attendance_difference", csvHeader, expBody14)

	q14 := xurl.Values{}
	q14.Add("file_type", "json")
	q14.Add("char_code", "utf8")
	q14.Add("order_by", "attendance_date")
	q14.Add("sort", "asc")
	uri14 := fmt.Sprintf("%s&%s", url, q14.Encode())

	case14 := attendanceDifferencesExportTcase{
		desc: "CASE14: order by attendance_date asc",

		preCompany:        preCompany14,
		preEmployees:      preEmps14,
		preTitles:         preTitles14,
		preContracts:      preContracts14,
		preCustomers:      preCustomers14,
		preDepartments:    preDepartments14,
		preTSheets:        preTSheets14,
		preTSheetRests:    preRests14,
		preMAtts:          preMAtts14,
		preAttDiffReasons: preReasons14,
		preIntegAttDiff:   integAttDiffs,

		reqObj: test.HandlerReq{
			Url:    uri14,
			Method: http.MethodGet,
		},
		resObj: test.ExportHandlerRes{
			Res:    expContent14,
			ErrStr: nil,
		},
		fn: h.Export,
	}

	// CASE15
	// sort: attendance_date, asc
	days15 := 2
	preCompany15 := at.genCompany(compNo)
	preEmps15 := at.genEmployee(compNo, 1)
	preTitles15 := at.genTitles(compNo, 1)
	preContracts15 := at.genContracts(compNo, 1)
	preCustomers15 := at.genCustomers(compNo, 1)
	preDepartments15 := at.genDepartments(compNo, 1)

	preTSheets15 := at.genTimesheets(preEmps14[0], days15)
	preRests15 := at.genRests(preTSheets15)

	preMAtts15 := at.genMeasuredAttendances(compNo, preEmps15[0], days15)
	preReasons15 := at.genReasons(compNo, preEmps15, days15, int(value.DifferenceReasonClassPcUnnecessarily))

	expBody15 := at.genBodies(startTime, endTime, preEmps15, preTSheets15, preMAtts15, preReasons15)
	sort.Slice(expBody15, func(i, j int) bool {
		dateI, _ := time.Parse("2006/01/02", expBody15[i]["attendance_date"].(string))
		dateJ, _ := time.Parse("2006/01/02", expBody15[j]["attendance_date"].(string))
		return dateI.After(dateJ)
	})

	expContent15 := test.NewexportTestContent("attendance_difference", csvHeader, expBody15)

	q15 := xurl.Values{}
	q15.Add("file_type", "json")
	q15.Add("char_code", "utf8")
	q15.Add("order_by", "attendance_date")
	q15.Add("sort", "desc")
	uri15 := fmt.Sprintf("%s&%s", url, q15.Encode())

	case15 := attendanceDifferencesExportTcase{
		desc: "CASE15: order by attendance_date desc",

		preCompany:        preCompany15,
		preEmployees:      preEmps15,
		preTitles:         preTitles15,
		preContracts:      preContracts15,
		preCustomers:      preCustomers15,
		preDepartments:    preDepartments15,
		preTSheets:        preTSheets15,
		preTSheetRests:    preRests15,
		preMAtts:          preMAtts15,
		preAttDiffReasons: preReasons15,
		preIntegAttDiff:   integAttDiffs,

		reqObj: test.HandlerReq{
			Url:    uri15,
			Method: http.MethodGet,
		},
		resObj: test.ExportHandlerRes{
			Res:    expContent15,
			ErrStr: nil,
		},
		fn: h.Export,
	}

	// CASE16
	// sort: start_time_diff, asc
	days16 := 2
	preCompany16 := at.genCompany(compNo)
	preEmps16 := at.genEmployee(compNo, 1)
	preTitles16 := at.genTitles(compNo, 1)
	preContracts16 := at.genContracts(compNo, 1)
	preCustomers16 := at.genCustomers(compNo, 1)
	preDepartments16 := at.genDepartments(compNo, 1)

	preTSheets16 := at.genTimesheets(preEmps16[0], days16)
	preRests16 := at.genRests(preTSheets16)

	preMAtts16 := at.genMeasuredAttendances(compNo, preEmps16[0], days16)
	preMAtts16[0].StartTime = preMAtts16[0].StartTime.Add(time.Minute * 1)
	preMAtts16[1].StartTime = preMAtts16[1].StartTime.Add(time.Minute * 2)

	preReasons16 := at.genReasons(compNo, preEmps16, days16, int(value.DifferenceReasonClassPcUnnecessarily))

	expBody16 := at.genBodies(startTime, endTime, preEmps16, preTSheets16, preMAtts16, preReasons16)
	expBody16[0]["start_time_diff"] = "00:01"
	expBody16[0]["pclog_work_time"] = "07:59"
	expBody16[0]["work_time_diff"] = "00:01"

	expBody16[1]["start_time_diff"] = "00:02"
	expBody16[1]["pclog_work_time"] = "07:58"
	expBody16[1]["work_time_diff"] = "00:02"
	sort.Slice(expBody16, func(i, j int) bool {
		var diffI, diffJ time.Duration
		if expBody16[i]["start_time_diff"].(string) == "" {
			diffI, _ = time.ParseDuration("00m00s")
		} else {
			diffI, _ = time.ParseDuration(fmt.Sprintf("%sm%ss", expBody16[i]["start_time_diff"].(string)[:2], expBody16[i]["start_time_diff"].(string)[3:]))
		}
		if expBody16[j]["start_time_diff"].(string) == "" {
			diffJ, _ = time.ParseDuration("00m00s")
		} else {
			diffJ, _ = time.ParseDuration(fmt.Sprintf("%sm%ss", expBody16[j]["start_time_diff"].(string)[:2], expBody16[j]["start_time_diff"].(string)[3:]))
		}

		if diffI < diffJ {
			return true
		} else if diffI == diffJ {
			if expBody16[i]["attendance_date"].(string) < expBody16[j]["attendance_date"].(string) {
				return true
			}
		}
		return false
	})

	expContent16 := test.NewexportTestContent("attendance_difference", csvHeader, expBody16)

	q16 := xurl.Values{}
	q16.Add("file_type", "json")
	q16.Add("char_code", "utf8")
	q16.Add("order_by", "start_time_diff_min")
	q16.Add("sort", "asc")
	uri16 := fmt.Sprintf("%s&%s", url, q16.Encode())

	case16 := attendanceDifferencesExportTcase{
		desc: "CASE16: order by start_time_diff asc",

		preCompany:        preCompany16,
		preEmployees:      preEmps16,
		preTitles:         preTitles16,
		preContracts:      preContracts16,
		preCustomers:      preCustomers16,
		preDepartments:    preDepartments16,
		preTSheets:        preTSheets16,
		preTSheetRests:    preRests16,
		preMAtts:          preMAtts16,
		preAttDiffReasons: preReasons16,
		preIntegAttDiff:   integAttDiffs,

		reqObj: test.HandlerReq{
			Url:    uri16,
			Method: http.MethodGet,
		},
		resObj: test.ExportHandlerRes{
			Res:    expContent16,
			ErrStr: nil,
		},
		fn: h.Export,
	}

	// CASE17
	// sort: start_time_diff, desc
	days17 := 2
	preCompany17 := at.genCompany(compNo)
	preEmps17 := at.genEmployee(compNo, 1)
	preTitles17 := at.genTitles(compNo, 1)
	preContracts17 := at.genContracts(compNo, 1)
	preCustomers17 := at.genCustomers(compNo, 1)
	preDepartments17 := at.genDepartments(compNo, 1)

	preTSheets17 := at.genTimesheets(preEmps17[0], days17)
	preRests17 := at.genRests(preTSheets17)

	preMAtts17 := at.genMeasuredAttendances(compNo, preEmps17[0], days17)
	preMAtts17[0].StartTime = preMAtts17[0].StartTime.Add(time.Minute * 1)
	preMAtts17[1].StartTime = preMAtts17[1].StartTime.Add(time.Minute * 2)

	preReasons17 := at.genReasons(compNo, preEmps17, days17, int(value.DifferenceReasonClassPcUnnecessarily))

	expBody17 := at.genBodies(startTime, endTime, preEmps17, preTSheets17, preMAtts17, preReasons17)
	expBody17[0]["start_time_diff"] = "00:01"
	expBody17[0]["pclog_work_time"] = "07:59"
	expBody17[0]["work_time_diff"] = "00:01"

	expBody17[1]["start_time_diff"] = "00:02"
	expBody17[1]["pclog_work_time"] = "07:58"
	expBody17[1]["work_time_diff"] = "00:02"
	sort.Slice(expBody17, func(i, j int) bool {
		var diffI, diffJ time.Duration
		if expBody17[i]["start_time_diff"].(string) == "" {
			diffI, _ = time.ParseDuration("00m00s")
		} else {
			diffI, _ = time.ParseDuration(fmt.Sprintf("%sm%ss", expBody17[i]["start_time_diff"].(string)[:2], expBody17[i]["start_time_diff"].(string)[3:]))
		}
		if expBody17[j]["start_time_diff"].(string) == "" {
			diffJ, _ = time.ParseDuration("00m00s")
		} else {
			diffJ, _ = time.ParseDuration(fmt.Sprintf("%sm%ss", expBody17[j]["start_time_diff"].(string)[:2], expBody17[j]["start_time_diff"].(string)[3:]))
		}

		if diffI > diffJ {
			return true
		} else if diffI == diffJ {
			if expBody17[i]["attendance_date"].(string) < expBody17[j]["attendance_date"].(string) {
				return true
			}
		}
		return false
	})

	expContent17 := test.NewexportTestContent("attendance_difference", csvHeader, expBody17)

	q17 := xurl.Values{}
	q17.Add("file_type", "json")
	q17.Add("char_code", "utf8")
	q17.Add("order_by", "start_time_diff_min")
	q17.Add("sort", "desc")
	uri17 := fmt.Sprintf("%s&%s", url, q17.Encode())

	case17 := attendanceDifferencesExportTcase{
		desc: "CASE17: order by start_time_diff desc",

		preCompany:        preCompany17,
		preEmployees:      preEmps17,
		preTitles:         preTitles17,
		preContracts:      preContracts17,
		preCustomers:      preCustomers17,
		preDepartments:    preDepartments17,
		preTSheets:        preTSheets17,
		preTSheetRests:    preRests17,
		preMAtts:          preMAtts17,
		preAttDiffReasons: preReasons17,
		preIntegAttDiff:   integAttDiffs,

		reqObj: test.HandlerReq{
			Url:    uri17,
			Method: http.MethodGet,
		},
		resObj: test.ExportHandlerRes{
			Res:    expContent17,
			ErrStr: nil,
		},
		fn: h.Export,
	}

	// CASE18
	// sort: end_time_diff, asc
	days18 := 2
	preCompany18 := at.genCompany(compNo)
	preEmps18 := at.genEmployee(compNo, 1)
	preTitles18 := at.genTitles(compNo, 1)
	preContracts18 := at.genContracts(compNo, 1)
	preCustomers18 := at.genCustomers(compNo, 1)
	preDepartments18 := at.genDepartments(compNo, 1)

	preTSheets18 := at.genTimesheets(preEmps18[0], days18)
	preRests18 := at.genRests(preTSheets18)

	preMAtts18 := at.genMeasuredAttendances(compNo, preEmps18[0], days18)
	preMAtts18[0].EndTime = test.NewPtr(preMAtts18[0].EndTime.Add(time.Minute * 1)).(*time.Time)
	preMAtts18[1].EndTime = test.NewPtr(preMAtts18[1].EndTime.Add(time.Minute * 2)).(*time.Time)

	preReasons18 := at.genReasons(compNo, preEmps18, days18, int(value.DifferenceReasonClassPcLogUnreadable))

	expBody18 := at.genBodies(startTime, endTime, preEmps18, preTSheets18, preMAtts18, preReasons18)
	expBody18[0]["end_time_diff"] = "00:01"
	expBody18[0]["pclog_work_time"] = "08:01"
	expBody18[0]["work_time_diff"] = "00:01"

	expBody18[1]["end_time_diff"] = "00:02"
	expBody18[1]["pclog_work_time"] = "08:02"
	expBody18[1]["work_time_diff"] = "00:02"
	sort.Slice(expBody18, func(i, j int) bool {
		var diffI, diffJ time.Duration
		if expBody18[i]["end_time_diff"].(string) == "" {
			diffI, _ = time.ParseDuration("00m00s")
		} else {
			diffI, _ = time.ParseDuration(fmt.Sprintf("%sm%ss", expBody18[i]["end_time_diff"].(string)[:2], expBody18[i]["end_time_diff"].(string)[3:]))
		}
		if expBody18[j]["end_time_diff"].(string) == "" {
			diffJ, _ = time.ParseDuration("00m00s")
		} else {
			diffJ, _ = time.ParseDuration(fmt.Sprintf("%sm%ss", expBody18[j]["end_time_diff"].(string)[:2], expBody18[j]["end_time_diff"].(string)[3:]))
		}

		if diffI < diffJ {
			return true
		} else if diffI == diffJ {
			if expBody18[i]["attendance_date"].(string) < expBody18[j]["attendance_date"].(string) {
				return true
			}
		}
		return false
	})

	expContent18 := test.NewexportTestContent("attendance_difference", csvHeader, expBody18)

	q18 := xurl.Values{}
	q18.Add("file_type", "json")
	q18.Add("char_code", "utf8")
	q18.Add("order_by", "end_time_diff_min")
	q18.Add("sort", "asc")
	uri18 := fmt.Sprintf("%s&%s", url, q18.Encode())

	case18 := attendanceDifferencesExportTcase{
		desc: "CASE18: order by end_time_diff asc",

		preCompany:        preCompany18,
		preEmployees:      preEmps18,
		preTitles:         preTitles18,
		preContracts:      preContracts18,
		preCustomers:      preCustomers18,
		preDepartments:    preDepartments18,
		preTSheets:        preTSheets18,
		preTSheetRests:    preRests18,
		preMAtts:          preMAtts18,
		preAttDiffReasons: preReasons18,
		preIntegAttDiff:   integAttDiffs,

		reqObj: test.HandlerReq{
			Url:    uri18,
			Method: http.MethodGet,
		},
		resObj: test.ExportHandlerRes{
			Res:    expContent18,
			ErrStr: nil,
		},
		fn: h.Export,
	}

	// CASE19
	// sort: end_time_diff, desc
	days19 := 2
	preCompany19 := at.genCompany(compNo)
	preEmps19 := at.genEmployee(compNo, 1)
	preTitles19 := at.genTitles(compNo, 1)
	preContracts19 := at.genContracts(compNo, 1)
	preCustomers19 := at.genCustomers(compNo, 1)
	preDepartments19 := at.genDepartments(compNo, 1)

	preTSheets19 := at.genTimesheets(preEmps19[0], days19)
	preRests19 := at.genRests(preTSheets19)

	preMAtts19 := at.genMeasuredAttendances(compNo, preEmps19[0], days19)
	preMAtts19[0].EndTime = test.NewPtr(preMAtts19[0].EndTime.Add(time.Minute * 1)).(*time.Time)
	preMAtts19[1].EndTime = test.NewPtr(preMAtts19[1].EndTime.Add(time.Minute * 2)).(*time.Time)

	preReasons19 := at.genReasons(compNo, preEmps19, days19, int(value.DifferenceReasonClassPcLogUnreadable))

	expBody19 := at.genBodies(startTime, endTime, preEmps19, preTSheets19, preMAtts19, preReasons19)
	expBody19[0]["end_time_diff"] = "00:01"
	expBody19[0]["pclog_work_time"] = "08:01"
	expBody19[0]["work_time_diff"] = "00:01"

	expBody19[1]["end_time_diff"] = "00:02"
	expBody19[1]["pclog_work_time"] = "08:02"
	expBody19[1]["work_time_diff"] = "00:02"
	sort.Slice(expBody19, func(i, j int) bool {
		var diffI, diffJ time.Duration
		if expBody19[i]["end_time_diff"].(string) == "" {
			diffI, _ = time.ParseDuration("00m00s")
		} else {
			diffI, _ = time.ParseDuration(fmt.Sprintf("%sm%ss", expBody19[i]["end_time_diff"].(string)[:2], expBody19[i]["end_time_diff"].(string)[3:]))
		}
		if expBody19[j]["end_time_diff"].(string) == "" {
			diffJ, _ = time.ParseDuration("00m00s")
		} else {
			diffJ, _ = time.ParseDuration(fmt.Sprintf("%sm%ss", expBody19[j]["end_time_diff"].(string)[:2], expBody19[j]["end_time_diff"].(string)[3:]))
		}

		if diffI > diffJ {
			return true
		} else if diffI == diffJ {
			if expBody19[i]["attendance_date"].(string) < expBody19[j]["attendance_date"].(string) {
				return true
			}
		}
		return false
	})

	expContent19 := test.NewexportTestContent("attendance_difference", csvHeader, expBody19)

	q19 := xurl.Values{}
	q19.Add("file_type", "json")
	q19.Add("char_code", "utf8")
	q19.Add("order_by", "end_time_diff_min")
	q19.Add("sort", "desc")
	uri19 := fmt.Sprintf("%s&%s", url, q19.Encode())

	case19 := attendanceDifferencesExportTcase{
		desc: "CASE19: order by end_time_diff desc",

		preCompany:        preCompany19,
		preEmployees:      preEmps19,
		preTitles:         preTitles19,
		preContracts:      preContracts19,
		preCustomers:      preCustomers19,
		preDepartments:    preDepartments19,
		preTSheets:        preTSheets19,
		preTSheetRests:    preRests19,
		preMAtts:          preMAtts19,
		preAttDiffReasons: preReasons19,
		preIntegAttDiff:   integAttDiffs,

		reqObj: test.HandlerReq{
			Url:    uri19,
			Method: http.MethodGet,
		},
		resObj: test.ExportHandlerRes{
			Res:    expContent19,
			ErrStr: nil,
		},
		fn: h.Export,
	}

	// CASE20
	// sort: work_time_diff_min, asc
	days20 := 2
	preCompany20 := at.genCompany(compNo)
	preEmps20 := at.genEmployee(compNo, 1)
	preTitles20 := at.genTitles(compNo, 1)
	preContracts20 := at.genContracts(compNo, 1)
	preCustomers20 := at.genCustomers(compNo, 1)
	preDepartments20 := at.genDepartments(compNo, 1)

	preTSheets20 := at.genTimesheets(preEmps20[0], days20)
	preRests20 := at.genRests(preTSheets20)

	preMAtts20 := at.genMeasuredAttendances(compNo, preEmps20[0], days20)
	preMAtts20[0].StartTime = preMAtts20[0].StartTime.Add(time.Minute * 1)
	preMAtts20[1].StartTime = preMAtts20[1].StartTime.Add(time.Minute * 2)

	preReasons20 := at.genReasons(compNo, preEmps20, days20, int(value.DifferenceReasonClassPcLogUnreadable))

	expBody20 := at.genBodies(startTime, endTime, preEmps20, preTSheets20, preMAtts20, preReasons20)
	expBody20[0]["start_time_diff"] = "00:01"
	expBody20[0]["pclog_work_time"] = "07:59"
	expBody20[0]["work_time_diff"] = "00:01"

	expBody20[1]["start_time_diff"] = "00:02"
	expBody20[1]["pclog_work_time"] = "07:58"
	expBody20[1]["work_time_diff"] = "00:02"
	sort.Slice(expBody20, func(i, j int) bool {
		var diffI, diffJ time.Duration
		if expBody20[i]["work_time_diff"].(string) == "" {
			diffI, _ = time.ParseDuration("00m00s")
		} else {
			diffI, _ = time.ParseDuration(fmt.Sprintf("%sm%ss", expBody20[i]["work_time_diff"].(string)[:2], expBody20[i]["work_time_diff"].(string)[3:]))
		}
		if expBody20[j]["work_time_diff"].(string) == "" {
			diffJ, _ = time.ParseDuration("00m00s")
		} else {
			diffJ, _ = time.ParseDuration(fmt.Sprintf("%sm%ss", expBody20[j]["work_time_diff"].(string)[:2], expBody20[j]["work_time_diff"].(string)[3:]))
		}

		if diffI < diffJ {
			return true
		} else if diffI == diffJ {
			if expBody20[i]["attendance_date"].(string) < expBody20[j]["attendance_date"].(string) {
				return true
			}
		}
		return false
	})

	expContent20 := test.NewexportTestContent("attendance_difference", csvHeader, expBody20)

	q20 := xurl.Values{}
	q20.Add("file_type", "json")
	q20.Add("char_code", "utf8")
	q20.Add("order_by", "work_time_diff_min")
	q20.Add("sort", "asc")
	uri20 := fmt.Sprintf("%s&%s", url, q20.Encode())

	case20 := attendanceDifferencesExportTcase{
		desc: "CASE20: order by work_time_diff_min asc",

		preCompany:        preCompany20,
		preEmployees:      preEmps20,
		preTitles:         preTitles20,
		preContracts:      preContracts20,
		preCustomers:      preCustomers20,
		preDepartments:    preDepartments20,
		preTSheets:        preTSheets20,
		preTSheetRests:    preRests20,
		preMAtts:          preMAtts20,
		preAttDiffReasons: preReasons20,
		preIntegAttDiff:   integAttDiffs,

		reqObj: test.HandlerReq{
			Url:    uri20,
			Method: http.MethodGet,
		},
		resObj: test.ExportHandlerRes{
			Res:    expContent20,
			ErrStr: nil,
		},
		fn: h.Export,
	}

	// CASE21
	// sort: work_time_diff_min, desc
	days21 := 2
	preCompany21 := at.genCompany(compNo)
	preEmps21 := at.genEmployee(compNo, 1)
	preTitles21 := at.genTitles(compNo, 1)
	preContracts21 := at.genContracts(compNo, 1)
	preCustomers21 := at.genCustomers(compNo, 1)
	preDepartments21 := at.genDepartments(compNo, 1)

	preTSheets21 := at.genTimesheets(preEmps21[0], days21)
	preRests21 := at.genRests(preTSheets21)

	preMAtts21 := at.genMeasuredAttendances(compNo, preEmps21[0], days21)
	preMAtts21[0].StartTime = preMAtts21[0].StartTime.Add(time.Minute * 1)
	preMAtts21[1].StartTime = preMAtts21[1].StartTime.Add(time.Minute * 2)

	preReasons21 := at.genReasons(compNo, preEmps21, days21, int(value.DifferenceReasonClassPcLogUnreadable))

	expBody21 := at.genBodies(startTime, endTime, preEmps21, preTSheets21, preMAtts21, preReasons21)
	expBody21[0]["start_time_diff"] = "00:01"
	expBody21[0]["pclog_work_time"] = "07:59"
	expBody21[0]["work_time_diff"] = "00:01"

	expBody21[1]["start_time_diff"] = "00:02"
	expBody21[1]["pclog_work_time"] = "07:58"
	expBody21[1]["work_time_diff"] = "00:02"
	sort.Slice(expBody21, func(i, j int) bool {
		var diffI, diffJ time.Duration
		if expBody21[i]["work_time_diff"].(string) == "" {
			diffI, _ = time.ParseDuration("00m00s")
		} else {
			diffI, _ = time.ParseDuration(fmt.Sprintf("%sm%ss", expBody21[i]["work_time_diff"].(string)[:2], expBody21[i]["work_time_diff"].(string)[3:]))
		}
		if expBody21[j]["work_time_diff"].(string) == "" {
			diffJ, _ = time.ParseDuration("00m00s")
		} else {
			diffJ, _ = time.ParseDuration(fmt.Sprintf("%sm%ss", expBody21[j]["work_time_diff"].(string)[:2], expBody21[j]["work_time_diff"].(string)[3:]))
		}

		if diffI > diffJ {
			return true
		} else if diffI == diffJ {
			if expBody21[i]["attendance_date"].(string) < expBody21[j]["attendance_date"].(string) {
				return true
			}
		}
		return false
	})

	expContent21 := test.NewexportTestContent("attendance_difference", csvHeader, expBody21)

	q21 := xurl.Values{}
	q21.Add("file_type", "json")
	q21.Add("char_code", "utf8")
	q21.Add("order_by", "work_time_diff_min")
	q21.Add("sort", "desc")
	uri21 := fmt.Sprintf("%s&%s", url, q21.Encode())

	case21 := attendanceDifferencesExportTcase{
		desc:              "CASE21: order by work_time_diff_min desc",
		preCompany:        preCompany21,
		preEmployees:      preEmps21,
		preTitles:         preTitles21,
		preContracts:      preContracts21,
		preCustomers:      preCustomers21,
		preDepartments:    preDepartments21,
		preTSheets:        preTSheets21,
		preTSheetRests:    preRests21,
		preMAtts:          preMAtts21,
		preAttDiffReasons: preReasons21,
		preIntegAttDiff:   integAttDiffs,
		reqObj: test.HandlerReq{
			Url:    uri21,
			Method: http.MethodGet,
		},
		resObj: test.ExportHandlerRes{
			Res:    expContent21,
			ErrStr: nil,
		},
		fn: h.Export,
	}

	// CASE22
	// internal server error
	mockCtrl22 := gomock.NewController(t)
	defer mockCtrl22.Finish()
	mockObj22 := cmockrepo.NewMockAttendanceDifference(mockCtrl22)
	mockObj22.EXPECT().Export(gomock.Any()).Return(nil, 0, errors.New("AAA"))
	mrr22 := &test.MockRepoRegistry{}
	mrr22.MAttDiff = mockObj22

	case22 := attendanceDifferencesExportTcase{
		desc:            "CASE22: internal server error",
		preIntegAttDiff: integAttDiffs,
		reqObj: test.HandlerReq{
			Url:    url,
			Method: http.MethodGet,
		},
		resObj: test.ExportHandlerRes{
			Res:    nil,
			ErrStr: "*errors.errorString",
		},
		fn:  h.Export,
		mrr: mrr22,
	}

	// CASE23
	// activity log
	preCompany23 := at.genCompany(compNo)
	preEmps23 := at.genEmployee(compNo, 1)
	preTitles23 := at.genTitles(compNo, 1)
	preContracts23 := at.genContracts(compNo, 1)
	preCustomers23 := at.genCustomers(compNo, 1)
	preDepartments23 := at.genDepartments(compNo, 1)
	preTSheets23 := at.genTimesheets(preEmps23[0], 1)
	preRests23 := at.genRests(preTSheets23)
	preMAtts23 := at.genMeasuredAttendances(compNo, preEmps23[0], 1)
	preReasons23 := at.genReasons(compNo, preEmps23, 1, int(value.DifferenceReasonClassPcLogUnreadable))

	expBody23 := at.genBodies(startTime, endTime, preEmps23, preTSheets23, preMAtts23, preReasons23)
	expContent23 := test.NewexportTestContent("attendance_difference", csvHeader, expBody23)

	q23 := xurl.Values{}
	q23.Add("file_type", "json")
	q23.Add("char_code", "utf8")
	uri23 := fmt.Sprintf("%s&%s", url, q23.Encode())

	userSetting23 := test.CreateDefaultUserInfo()
	accessInfo23 := test.CreateDefaultAccessInfo()
	accessInfo23.AccessType = config.WEBAccess
	expActLog23 := at.genActLog(compNo, userID, accessInfo23)

	case23 := attendanceDifferencesExportTcase{
		desc: "CASE23: activity log",

		preCompany:        preCompany23,
		preEmployees:      preEmps23,
		preTitles:         preTitles23,
		preContracts:      preContracts23,
		preCustomers:      preCustomers23,
		preDepartments:    preDepartments23,
		preTSheets:        preTSheets23,
		preTSheetRests:    preRests23,
		preMAtts:          preMAtts23,
		preAttDiffReasons: preReasons23,
		preIntegAttDiff:   integAttDiffs,
		expActLogs:        expActLog23,

		reqObj: test.HandlerReq{
			Url:    uri23,
			Method: http.MethodGet,
		},
		resObj: test.ExportHandlerRes{
			Res:    expContent23,
			ErrStr: nil,
		},
		fn:          h.Export,
		userSetting: &userSetting23,
		accessInfo:  &accessInfo23,
	}

	// CSE26
	// activity log: internal server error
	preCompany26 := at.genCompany(compNo)
	preEmps26 := at.genEmployee(compNo, 1)

	accessInfo26 := test.CreateDefaultAccessInfo()
	accessInfo26.AccessType = config.WEBAccess

	mockCtrl26 := gomock.NewController(t)
	defer mockCtrl26.Finish()
	mockObj26 := mockrepo.NewMockActivityLog(mockCtrl26)
	mockObj26.EXPECT().Create(gomock.Any()).Return(errors.New("AAA"))
	mrr26 := &test.MockRepoRegistry{}
	mrr26.MActivityLog = mockObj26

	q26 := xurl.Values{}
	q26.Add("file_type", "json")
	q26.Add("char_code", "utf8")
	uri26 := fmt.Sprintf("%s&%s", url, q26.Encode())

	case26 := attendanceDifferencesExportTcase{
		desc: "CASE26: activity log: internal server error",

		preCompany:      preCompany26,
		preEmployees:    preEmps26,
		preIntegAttDiff: integAttDiffs,

		reqObj: test.HandlerReq{
			Url:    uri26,
			Method: http.MethodGet,
		},
		resObj: test.ExportHandlerRes{
			Res:    nil,
			ErrStr: "*errors.errorString",
		},
		fn:         h.Export,
		mrr:        mrr26,
		accessInfo: &accessInfo26,
	}

	// CSE27
	// Integration attendance difference FindOne() internal server error
	preCompany27 := at.genCompany(compNo)
	preEmps27 := at.genEmployee(compNo, 1)

	accessInfo27 := test.CreateDefaultAccessInfo()
	accessInfo27.AccessType = config.WEBAccess

	mockCtrl27 := gomock.NewController(t)
	defer mockCtrl27.Finish()
	mockObj27 := cmockrepo.NewMockIntegrationAttendanceDifference(mockCtrl27)
	mockObj27.EXPECT().FindOne(compNo).
		Return(new_model.IntegrationAttendanceDifference{}, errors.New("AAA"))
	mrr27 := &test.MockRepoRegistry{}
	mrr27.MIntegAttDiff = mockObj27

	q27 := xurl.Values{}
	q27.Add("file_type", "json")
	q27.Add("char_code", "utf8")
	uri27 := fmt.Sprintf("%s&%s", url, q27.Encode())

	case27 := attendanceDifferencesExportTcase{
		desc: "CASE27: Integration attendance difference FindOne() internal server error",

		preCompany:      preCompany27,
		preEmployees:    preEmps27,
		preIntegAttDiff: integAttDiffs,

		reqObj: test.HandlerReq{
			Url:    uri27,
			Method: http.MethodGet,
		},
		resObj: test.ExportHandlerRes{
			Res:    nil,
			ErrStr: "*errors.errorString",
		},
		fn:         h.Export,
		mrr:        mrr27,
		accessInfo: &accessInfo27,
	}

	// CASE24
	// Day off
	preCompany24 := at.genCompany(compNo)
	preEmps24 := at.genEmployee(compNo, 1)
	preTitles24 := at.genTitles(compNo, 1)
	preContracts24 := at.genContracts(compNo, 1)
	preCustomers24 := at.genCustomers(compNo, 1)
	preDepartments24 := at.genDepartments(compNo, 1)
	preTSheets24 := at.genTimesheets(preEmps24[0], 1)
	preTSheets24[0].HolidayType = intutil.ToPtr(int(config.TsHldyWholeDay))
	preRests24 := at.genRests(preTSheets24)

	expBody24 := at.genBodies(startTime, endTime, preEmps24, preTSheets24, []tbl.MeasuredAttendance{}, []tbl.AttendanceDifferenceReason{})
	expContent24 := test.NewexportTestContent("attendance_difference", csvHeader, expBody24)
	expContent24.Body()[0]["start_time"] = ""
	expContent24.Body()[0]["end_time"] = ""
	expContent24.Body()[0]["work_time"] = ""
	expContent24.Body()[0]["idle_time"] = ""
	expContent24.Body()[0]["pclog_work_time"] = ""
	expContent24.Body()[0]["start_time_diff"] = ""
	expContent24.Body()[0]["end_time_diff"] = ""
	expContent24.Body()[0]["work_time_diff"] = ""
	expContent24.Body()[0]["start_time_diff_flag"] = false
	expContent24.Body()[0]["end_time_diff_flag"] = false

	q24 := xurl.Values{}
	q24.Add("file_type", "json")
	q24.Add("char_code", "utf8")
	uri24 := fmt.Sprintf("%s&%s", url, q24.Encode())

	case24 := attendanceDifferencesExportTcase{
		desc: "CASE24: Day off",

		preCompany:      preCompany24,
		preEmployees:    preEmps24,
		preTitles:       preTitles24,
		preContracts:    preContracts24,
		preCustomers:    preCustomers24,
		preDepartments:  preDepartments24,
		preTSheets:      preTSheets24,
		preTSheetRests:  preRests24,
		preIntegAttDiff: integAttDiffs,

		reqObj: test.HandlerReq{
			Url:    uri24,
			Method: http.MethodGet,
		},
		resObj: test.ExportHandlerRes{
			Res:    expContent24,
			ErrStr: nil,
		},
		fn: h.Export,
	}

	// CASE25
	// Day off with PCLog
	preCompany25 := at.genCompany(compNo)
	preEmps25 := at.genEmployee(compNo, 1)
	preTitles25 := at.genTitles(compNo, 1)
	preContracts25 := at.genContracts(compNo, 1)
	preCustomers25 := at.genCustomers(compNo, 1)
	preDepartments25 := at.genDepartments(compNo, 1)
	preTSheets25 := at.genTimesheets(preEmps25[0], 1)
	preTSheets25[0].HolidayType = intutil.ToPtr(int(config.TsHldyWholeDay))
	preRests25 := at.genRests(preTSheets25)
	preMAtts25 := at.genMeasuredAttendances(compNo, preEmps25[0], 1)
	preReasons25 := at.genReasons(compNo, preEmps25, 1, int(value.DifferenceReasonClassPcOffOmission))

	expBody25 := at.genBodies(startTime, endTime, preEmps25, preTSheets25, preMAtts25, preReasons25)
	expContent25 := test.NewexportTestContent("attendance_difference", csvHeader, expBody25)
	expContent25.Body()[0]["start_time"] = ""
	expContent25.Body()[0]["end_time"] = ""
	expContent25.Body()[0]["work_time"] = ""
	expContent25.Body()[0]["start_time_diff"] = ""
	expContent25.Body()[0]["end_time_diff"] = ""
	expContent25.Body()[0]["work_time_diff"] = ""
	expContent25.Body()[0]["start_time_diff_flag"] = true
	expContent25.Body()[0]["end_time_diff_flag"] = true
	expContent25.Body()[0]["fixed_status"] = false

	q25 := xurl.Values{}
	q25.Add("file_type", "json")
	q25.Add("char_code", "utf8")
	uri25 := fmt.Sprintf("%s&%s", url, q25.Encode())

	case25 := attendanceDifferencesExportTcase{
		desc: "CASE25: Day off with PCLog",

		preCompany:        preCompany25,
		preEmployees:      preEmps25,
		preTitles:         preTitles25,
		preContracts:      preContracts25,
		preCustomers:      preCustomers25,
		preDepartments:    preDepartments25,
		preTSheets:        preTSheets25,
		preTSheetRests:    preRests25,
		preMAtts:          preMAtts25,
		preAttDiffReasons: preReasons25,
		preIntegAttDiff:   integAttDiffs,

		reqObj: test.HandlerReq{
			Url:    uri25,
			Method: http.MethodGet,
		},
		resObj: test.ExportHandlerRes{
			Res:    expContent25,
			ErrStr: nil,
		},
		fn: h.Export,
	}

	// CASE28
	// system admin user with for_self as false
	preCompany28 := at.genCompany(compNo)
	preEmps28 := at.genEmployee(compNo, 2)
	preTitles28 := at.genTitles(compNo, 2)
	preContracts28 := at.genContracts(compNo, 2)
	preCustomers28 := at.genCustomers(compNo, 2)
	preDepartments28 := at.genDepartments(compNo, 2)
	preTSheets28 := append(at.genTimesheets(preEmps28[0], 1), at.genTimesheets(preEmps28[1], 1)...)
	preRests28 := at.genRests(preTSheets28)
	preMAtts28 := append(at.genMeasuredAttendances(compNo, preEmps28[0], 1), at.genMeasuredAttendances(compNo, preEmps28[1], 1)...)
	preReasons28 := at.genReasons(compNo, preEmps28, 1, int(value.DifferenceReasonClassPcOffOmission))

	expBody28 := at.genBodies(startTime, endTime, preEmps28, preTSheets28, preMAtts28, preReasons28)
	expContent28 := test.NewexportTestContent("attendance_difference", csvHeader, expBody28)

	userSetting28 := test.CreateDefaultUserInfo()

	q28 := xurl.Values{}
	q28.Add("file_type", "json")
	q28.Add("char_code", "utf8")
	q28.Add("for_self", "false")
	uri28 := fmt.Sprintf("%s&%s", url, q28.Encode())

	case28 := attendanceDifferencesExportTcase{
		desc: "CASE28: system admin user with for_self as false",

		preCompany:        preCompany28,
		preEmployees:      preEmps28,
		preTitles:         preTitles28,
		preContracts:      preContracts28,
		preCustomers:      preCustomers28,
		preDepartments:    preDepartments28,
		preTSheets:        preTSheets28,
		preTSheetRests:    preRests28,
		preMAtts:          preMAtts28,
		preAttDiffReasons: preReasons28,
		preIntegAttDiff:   integAttDiffs,

		reqObj: test.HandlerReq{
			Url:    uri28,
			Method: http.MethodGet,
		},
		resObj: test.ExportHandlerRes{
			Res:    expContent28,
			ErrStr: nil,
		},
		fn:          h.Export,
		userSetting: &userSetting28,
	}

	// CASE29
	// system admin user with for_self as true
	preCompany29 := at.genCompany(compNo)
	preEmps29 := at.genEmployee(compNo, 2)
	preTitles29 := at.genTitles(compNo, 2)
	preContracts29 := at.genContracts(compNo, 2)
	preCustomers29 := at.genCustomers(compNo, 2)
	preDepartments29 := at.genDepartments(compNo, 2)
	preTSheets29 := append(at.genTimesheets(preEmps29[0], 1), at.genTimesheets(preEmps29[1], 1)...)
	preRests29 := at.genRests(preTSheets29)
	preMAtts29 := append(at.genMeasuredAttendances(compNo, preEmps29[0], 1), at.genMeasuredAttendances(compNo, preEmps29[1], 1)...)
	preReasons29 := at.genReasons(compNo, preEmps29, 1, int(value.DifferenceReasonClassPcOffOmission))

	expBody29 := at.genBodies(startTime, endTime, []tbl.Employee{preEmps29[0]}, []tbl.Timesheet{preTSheets29[0]}, []tbl.MeasuredAttendance{preMAtts29[0]}, []tbl.AttendanceDifferenceReason{preReasons29[0]})
	expContent29 := test.NewexportTestContent("attendance_difference", csvHeader, expBody29)

	userSetting29 := test.CreateDefaultUserInfo()

	q29 := xurl.Values{}
	q29.Add("file_type", "json")
	q29.Add("char_code", "utf8")
	q29.Add("for_self", "true")
	uri29 := fmt.Sprintf("%s&%s", url, q29.Encode())

	case29 := attendanceDifferencesExportTcase{
		desc: "CASE29: system admin user with for_self as true",

		preCompany:        preCompany29,
		preEmployees:      preEmps29,
		preTitles:         preTitles29,
		preContracts:      preContracts29,
		preCustomers:      preCustomers29,
		preDepartments:    preDepartments29,
		preTSheets:        preTSheets29,
		preTSheetRests:    preRests29,
		preMAtts:          preMAtts29,
		preAttDiffReasons: preReasons29,
		preIntegAttDiff:   integAttDiffs,

		reqObj: test.HandlerReq{
			Url:    uri29,
			Method: http.MethodGet,
		},
		resObj: test.ExportHandlerRes{
			Res:    expContent29,
			ErrStr: nil,
		},
		fn:          h.Export,
		userSetting: &userSetting29,
	}

	// CASE30
	// regular member without query param for_self
	preCompany30 := at.genCompany(compNo)
	preEmps30 := at.genEmployee(compNo, 2)
	preTitles30 := at.genTitles(compNo, 2)
	preContracts30 := at.genContracts(compNo, 2)
	preCustomers30 := at.genCustomers(compNo, 2)
	preDepartments30 := at.genDepartments(compNo, 2)
	preTSheets30 := append(at.genTimesheets(preEmps30[0], 1), at.genTimesheets(preEmps30[1], 1)...)
	preRests30 := at.genRests(preTSheets30)
	preMAtts30 := append(at.genMeasuredAttendances(compNo, preEmps30[0], 1), at.genMeasuredAttendances(compNo, preEmps30[1], 1)...)
	preReasons30 := at.genReasons(compNo, preEmps30, 1, int(value.DifferenceReasonClassPcOffOmission))

	expBody30 := at.genBodies(startTime, endTime, []tbl.Employee{preEmps30[0]}, []tbl.Timesheet{preTSheets30[0]}, []tbl.MeasuredAttendance{preMAtts30[0]}, []tbl.AttendanceDifferenceReason{preReasons30[0]})
	expContent30 := test.NewexportTestContent("attendance_difference", csvHeader, expBody30)

	userSetting30 := test.CreateDefaultUserInfo()
	userSetting30.SysAdminFlag = false

	q30 := xurl.Values{}
	q30.Add("file_type", "json")
	q30.Add("char_code", "utf8")
	uri30 := fmt.Sprintf("%s&%s", url, q30.Encode())

	case30 := attendanceDifferencesExportTcase{
		desc: "CASE30: regular member without query param for_self",

		preCompany:        preCompany30,
		preEmployees:      preEmps30,
		preTitles:         preTitles30,
		preContracts:      preContracts30,
		preCustomers:      preCustomers30,
		preDepartments:    preDepartments30,
		preTSheets:        preTSheets30,
		preTSheetRests:    preRests30,
		preMAtts:          preMAtts30,
		preAttDiffReasons: preReasons30,
		preIntegAttDiff:   integAttDiffs,

		reqObj: test.HandlerReq{
			Url:    uri30,
			Method: http.MethodGet,
		},
		resObj: test.ExportHandlerRes{
			Res:    expContent30,
			ErrStr: nil,
		},
		fn:          h.Export,
		userSetting: &userSetting30,
	}

	// CASE31
	// regular member with for_self as false
	preCompany31 := at.genCompany(compNo)
	preEmps31 := at.genEmployee(compNo, 2)
	preTitles31 := at.genTitles(compNo, 2)
	preContracts31 := at.genContracts(compNo, 2)
	preCustomers31 := at.genCustomers(compNo, 2)
	preDepartments31 := at.genDepartments(compNo, 2)
	preTSheets31 := append(at.genTimesheets(preEmps31[0], 1), at.genTimesheets(preEmps31[1], 1)...)
	preRests31 := at.genRests(preTSheets31)
	preMAtts31 := append(at.genMeasuredAttendances(compNo, preEmps31[0], 1), at.genMeasuredAttendances(compNo, preEmps31[1], 1)...)
	preReasons31 := at.genReasons(compNo, preEmps31, 1, int(value.DifferenceReasonClassPcOffOmission))

	expBody31 := at.genBodies(startTime, endTime, []tbl.Employee{preEmps31[0]}, []tbl.Timesheet{preTSheets31[0]}, []tbl.MeasuredAttendance{preMAtts31[0]}, []tbl.AttendanceDifferenceReason{preReasons31[0]})
	expContent31 := test.NewexportTestContent("attendance_difference", csvHeader, expBody31)

	userSetting31 := test.CreateDefaultUserInfo()
	userSetting31.SysAdminFlag = false

	q31 := xurl.Values{}
	q31.Add("file_type", "json")
	q31.Add("char_code", "utf8")
	q31.Add("for_self", "false")
	uri31 := fmt.Sprintf("%s&%s", url, q31.Encode())

	case31 := attendanceDifferencesExportTcase{
		desc: "CASE31: regular member with for_self as false",

		preCompany:        preCompany31,
		preEmployees:      preEmps31,
		preTitles:         preTitles31,
		preContracts:      preContracts31,
		preCustomers:      preCustomers31,
		preDepartments:    preDepartments31,
		preTSheets:        preTSheets31,
		preTSheetRests:    preRests31,
		preMAtts:          preMAtts31,
		preAttDiffReasons: preReasons31,
		preIntegAttDiff:   integAttDiffs,

		reqObj: test.HandlerReq{
			Url:    uri31,
			Method: http.MethodGet,
		},
		resObj: test.ExportHandlerRes{
			Res:    expContent31,
			ErrStr: nil,
		},
		fn:          h.Export,
		userSetting: &userSetting31,
	}

	// CASE32
	// regular member with for_self as true
	preCompany32 := at.genCompany(compNo)
	preEmps32 := at.genEmployee(compNo, 2)
	preTitles32 := at.genTitles(compNo, 2)
	preContracts32 := at.genContracts(compNo, 2)
	preCustomers32 := at.genCustomers(compNo, 2)
	preDepartments32 := at.genDepartments(compNo, 2)
	preTSheets32 := append(at.genTimesheets(preEmps32[0], 1), at.genTimesheets(preEmps32[1], 1)...)
	preRests32 := at.genRests(preTSheets32)
	preMAtts32 := append(at.genMeasuredAttendances(compNo, preEmps32[0], 1), at.genMeasuredAttendances(compNo, preEmps32[1], 1)...)
	preReasons32 := at.genReasons(compNo, preEmps32, 1, int(value.DifferenceReasonClassPcOffOmission))

	expBody32 := at.genBodies(startTime, endTime, []tbl.Employee{preEmps32[0]}, []tbl.Timesheet{preTSheets32[0]}, []tbl.MeasuredAttendance{preMAtts32[0]}, []tbl.AttendanceDifferenceReason{preReasons32[0]})
	expContent32 := test.NewexportTestContent("attendance_difference", csvHeader, expBody32)

	userSetting32 := test.CreateDefaultUserInfo()
	userSetting32.SysAdminFlag = false

	q32 := xurl.Values{}
	q32.Add("file_type", "json")
	q32.Add("char_code", "utf8")
	q32.Add("for_self", "true")
	uri32 := fmt.Sprintf("%s&%s", url, q32.Encode())

	case32 := attendanceDifferencesExportTcase{
		desc: "CASE32: regular member with for_self as true",

		preCompany:        preCompany32,
		preEmployees:      preEmps32,
		preTitles:         preTitles32,
		preContracts:      preContracts32,
		preCustomers:      preCustomers32,
		preDepartments:    preDepartments32,
		preTSheets:        preTSheets32,
		preTSheetRests:    preRests32,
		preMAtts:          preMAtts32,
		preAttDiffReasons: preReasons32,
		preIntegAttDiff:   integAttDiffs,

		reqObj: test.HandlerReq{
			Url:    uri32,
			Method: http.MethodGet,
		},
		resObj: test.ExportHandlerRes{
			Res:    expContent32,
			ErrStr: nil,
		},
		fn:          h.Export,
		userSetting: &userSetting32,
	}

	// CASE33
	// should export reason class content, when the difference has a reason record with any class except for 0, but its reason is empty
	days33 := 2
	preCompany33 := at.genCompany(compNo)
	preEmps33 := at.genEmployee(compNo, 1)
	preTitles33 := at.genTitles(compNo, 1)
	preContracts33 := at.genContracts(compNo, 1)
	preCustomers33 := at.genCustomers(compNo, 1)
	preDepartments33 := at.genDepartments(compNo, 1)

	preTSheets33 := at.genTimesheets(preEmps33[0], days33)
	preRests33 := at.genRests(preTSheets33)

	preMAtts33 := at.genMeasuredAttendances(compNo, preEmps33[0], days33)
	preMAtts33[0].StartTime = preMAtts33[0].StartTime.Add(time.Minute * 30)

	preReasons33 := at.genReasons(compNo, preEmps33, days33, int(value.DifferenceReasonClassPcOffOmission))
	preReasons33[0].Reason = test.NewPtr("").(*string)

	expBody33 := at.genBodies(startTime, endTime, preEmps33, preTSheets33, preMAtts33, preReasons33)
	expBody33[0]["start_time_diff"] = "00:30"
	expBody33[0]["start_time_diff_flag"] = true
	expBody33[0]["pclog_work_time"] = "07:30"
	expBody33[0]["work_time_diff"] = "00:30"
	expContent33 := test.NewexportTestContent("attendance_difference", csvHeader, expBody33)

	q33 := xurl.Values{}
	q33.Add("file_type", "json")
	q33.Add("char_code", "utf8")
	uri33 := fmt.Sprintf("%s&%s", url, q33.Encode())

	case33 := attendanceDifferencesExportTcase{
		desc: "CASE33: should export reason class content, when the difference has a reason record with any class except for 0, but its reason is empty",

		preCompany:        preCompany33,
		preEmployees:      preEmps33,
		preTitles:         preTitles33,
		preContracts:      preContracts33,
		preCustomers:      preCustomers33,
		preDepartments:    preDepartments33,
		preTSheets:        preTSheets33,
		preTSheetRests:    preRests33,
		preMAtts:          preMAtts33,
		preAttDiffReasons: preReasons33,
		preIntegAttDiff:   integAttDiffs,

		reqObj: test.HandlerReq{
			Url:    uri33,
			Method: http.MethodGet,
		},
		resObj: test.ExportHandlerRes{
			Res:    expContent33,
			ErrStr: nil,
		},
		fn: h.Export,
	}

	// CASE34
	// 34 No timesheet
	preCompany34 := at.genCompany(compNo)
	preEmps34 := at.genEmployee(compNo, 1)
	preTitles34 := at.genTitles(compNo, 1)
	preContracts34 := at.genContracts(compNo, 1)
	preCustomers34 := at.genCustomers(compNo, 1)
	preDepartments34 := at.genDepartments(compNo, 1)
	preTSheets34 := at.genTimesheets(preEmps34[0], 1)
	preMAtts34 := at.genMeasuredAttendances(compNo, preEmps34[0], 1)
	preReasons34 := at.genReasons(compNo, preEmps34, 1, int(value.DifferenceReasonClassOthers))

	expBody34 := at.genBodies(startTime, endTime, preEmps34, preTSheets34, preMAtts34, preReasons34)
	expBody34[0]["start_time"] = ""
	expBody34[0]["start_time_diff"] = ""
	expBody34[0]["start_time_diff_flag"] = true
	expBody34[0]["end_time"] = ""
	expBody34[0]["end_time_diff"] = ""
	expBody34[0]["end_time_diff_flag"] = true
	expBody34[0]["work_time"] = ""
	expBody34[0]["work_time_diff"] = ""
	expBody34[0]["fixed_status"] = false
	expContent34 := test.NewexportTestContent("attendance_difference", csvHeader, expBody34)
	userSetting34 := test.CreateDefaultUserInfo()

	q34 := xurl.Values{}
	q34.Add("file_type", "json")
	q34.Add("char_code", "utf8")
	uri34 := fmt.Sprintf("%s&%s", url, q34.Encode())

	case34 := attendanceDifferencesExportTcase{
		desc: "CASE34: No timesheet",

		preCompany:        preCompany34,
		preEmployees:      preEmps34,
		preTitles:         preTitles34,
		preContracts:      preContracts34,
		preCustomers:      preCustomers34,
		preDepartments:    preDepartments34,
		preMAtts:          preMAtts34,
		preAttDiffReasons: preReasons34,
		preIntegAttDiff:   integAttDiffs,

		reqObj: test.HandlerReq{
			Url:    uri34,
			Method: http.MethodGet,
		},
		resObj: test.ExportHandlerRes{
			Res:    expContent34,
			ErrStr: nil,
		},
		fn:          h.Export,
		userSetting: &userSetting34,
	}

	// CASE35
	// 35 No PCLog
	preCompany35 := at.genCompany(compNo)
	preEmps35 := at.genEmployee(compNo, 1)
	preTitles35 := at.genTitles(compNo, 1)
	preContracts35 := at.genContracts(compNo, 1)
	preCustomers35 := at.genCustomers(compNo, 1)
	preDepartments35 := at.genDepartments(compNo, 1)
	preTSheets35 := at.genTimesheets(preEmps35[0], 1)
	preRests35 := at.genRests(preTSheets35)
	preMAtts35 := at.genMeasuredAttendances(compNo, preEmps35[0], 1)
	preReasons35 := at.genReasons(compNo, preEmps35, 1, int(value.DifferenceReasonClassOthers))

	expBody35 := at.genBodies(startTime, endTime, preEmps35, preTSheets35, preMAtts35, preReasons35)
	expBody35[0]["pclog_start_time"] = ""
	expBody35[0]["start_time_diff"] = ""
	expBody35[0]["start_time_diff_flag"] = true
	expBody35[0]["pclog_end_time"] = ""
	expBody35[0]["end_time_diff"] = ""
	expBody35[0]["end_time_diff_flag"] = true
	expBody35[0]["pclog_work_time"] = ""
	expBody35[0]["work_time_diff"] = ""
	expBody35[0]["idle_time"] = ""
	expBody35[0]["fixed_status"] = false
	expContent35 := test.NewexportTestContent("attendance_difference", csvHeader, expBody35)
	userSetting35 := test.CreateDefaultUserInfo()

	q35 := xurl.Values{}
	q35.Add("file_type", "json")
	q35.Add("char_code", "utf8")
	uri35 := fmt.Sprintf("%s&%s", url, q35.Encode())

	case35 := attendanceDifferencesExportTcase{
		desc: "CASE35: No PCLog",

		preCompany:        preCompany35,
		preEmployees:      preEmps35,
		preTitles:         preTitles35,
		preContracts:      preContracts35,
		preCustomers:      preCustomers35,
		preDepartments:    preDepartments35,
		preTSheets:        preTSheets35,
		preTSheetRests:    preRests35,
		preAttDiffReasons: preReasons35,
		preIntegAttDiff:   integAttDiffs,

		reqObj: test.HandlerReq{
			Url:    uri35,
			Method: http.MethodGet,
		},
		resObj: test.ExportHandlerRes{
			Res:    expContent35,
			ErrStr: nil,
		},
		fn:          h.Export,
		userSetting: &userSetting35,
	}

	// CASE36
	// 36 Timesheet is not fixed
	preCompany36 := at.genCompany(compNo)
	preEmps36 := at.genEmployee(compNo, 1)
	preTitles36 := at.genTitles(compNo, 1)
	preContracts36 := at.genContracts(compNo, 1)
	preCustomers36 := at.genCustomers(compNo, 1)
	preDepartments36 := at.genDepartments(compNo, 1)
	preTSheets36 := at.genTimesheets(preEmps36[0], 1)
	preTSheets36[0].EndTime = nil
	preRests36 := at.genRests(preTSheets36)
	preMAtts36 := at.genMeasuredAttendances(compNo, preEmps36[0], 1)
	preReasons36 := at.genReasons(compNo, preEmps36, 1, int(value.DifferenceReasonClassOthers))

	expBody36 := at.genBodies(startTime, endTime, preEmps36, preTSheets36, preMAtts36, preReasons36)
	expBody36[0]["start_time"] = "08:00"
	expBody36[0]["start_time_diff"] = "00:00"
	expBody36[0]["start_time_diff_flag"] = false
	expBody36[0]["end_time"] = ""
	expBody36[0]["end_time_diff"] = ""
	expBody36[0]["end_time_diff_flag"] = true
	expBody36[0]["work_time"] = ""
	expBody36[0]["work_time_diff"] = ""
	expBody36[0]["fixed_status"] = false
	expContent36 := test.NewexportTestContent("attendance_difference", csvHeader, expBody36)
	userSetting36 := test.CreateDefaultUserInfo()

	q36 := xurl.Values{}
	q36.Add("file_type", "json")
	q36.Add("char_code", "utf8")
	uri36 := fmt.Sprintf("%s&%s", url, q36.Encode())

	case36 := attendanceDifferencesExportTcase{
		desc: "CASE36: Timesheet is not fixed",

		preCompany:        preCompany36,
		preEmployees:      preEmps36,
		preTitles:         preTitles36,
		preContracts:      preContracts36,
		preCustomers:      preCustomers36,
		preDepartments:    preDepartments36,
		preTSheets:        preTSheets36,
		preTSheetRests:    preRests36,
		preMAtts:          preMAtts36,
		preAttDiffReasons: preReasons36,
		preIntegAttDiff:   integAttDiffs,

		reqObj: test.HandlerReq{
			Url:    uri36,
			Method: http.MethodGet,
		},
		resObj: test.ExportHandlerRes{
			Res:    expContent36,
			ErrStr: nil,
		},
		fn:          h.Export,
		userSetting: &userSetting36,
	}

	// CASE37
	// 37 PCLog is not fixed
	preCompany37 := at.genCompany(compNo)
	preEmps37 := at.genEmployee(compNo, 1)
	preTitles37 := at.genTitles(compNo, 1)
	preContracts37 := at.genContracts(compNo, 1)
	preCustomers37 := at.genCustomers(compNo, 1)
	preDepartments37 := at.genDepartments(compNo, 1)
	preTSheets37 := at.genTimesheets(preEmps37[0], 1)
	preRests37 := at.genRests(preTSheets37)
	preMAtts37 := at.genMeasuredAttendances(compNo, preEmps37[0], 1)
	preMAtts37[0].EndTime = nil
	preMAtts37[0].IdleMin = nil
	preReasons37 := at.genReasons(compNo, preEmps37, 1, int(value.DifferenceReasonClassOthers))

	expBody37 := at.genBodies(startTime, endTime, preEmps37, preTSheets37, preMAtts37, preReasons37)
	expBody37[0]["pclog_start_time"] = "08:00"
	expBody37[0]["start_time_diff"] = "00:00"
	expBody37[0]["start_time_diff_flag"] = false
	expBody37[0]["pclog_end_time"] = ""
	expBody37[0]["end_time_diff"] = ""
	expBody37[0]["end_time_diff_flag"] = true
	expBody37[0]["pclog_work_time"] = ""
	expBody37[0]["work_time_diff"] = ""
	expBody37[0]["idle_time"] = ""
	expBody37[0]["fixed_status"] = false
	expContent37 := test.NewexportTestContent("attendance_difference", csvHeader, expBody37)
	userSetting37 := test.CreateDefaultUserInfo()

	q37 := xurl.Values{}
	q37.Add("file_type", "json")
	q37.Add("char_code", "utf8")
	uri37 := fmt.Sprintf("%s&%s", url, q37.Encode())

	case37 := attendanceDifferencesExportTcase{
		desc: "CASE37: PCLog is not fixed",

		preCompany:        preCompany37,
		preEmployees:      preEmps37,
		preTitles:         preTitles37,
		preContracts:      preContracts37,
		preCustomers:      preCustomers37,
		preDepartments:    preDepartments37,
		preTSheets:        preTSheets37,
		preTSheetRests:    preRests37,
		preMAtts:          preMAtts37,
		preAttDiffReasons: preReasons37,
		preIntegAttDiff:   integAttDiffs,

		reqObj: test.HandlerReq{
			Url:    uri37,
			Method: http.MethodGet,
		},
		resObj: test.ExportHandlerRes{
			Res:    expContent37,
			ErrStr: nil,
		},
		fn:          h.Export,
		userSetting: &userSetting37,
	}

	cases := []attendanceDifferencesExportTcase{
		case1, case2, case3, case4, case5,
		case6, case7, case8 /*case9,*/, case10,
		case11, case12, case13, case14, case15,
		case16, case17, case18, case19, case20,
		case21, case22, case23, case24, case25,
		case26, case27, case28, case29, case30,
		case31, case32, case33, case34, case35,
		case36, case37,
	}

	at.ExecExportTest(t, compNo, userID, cases)
}
