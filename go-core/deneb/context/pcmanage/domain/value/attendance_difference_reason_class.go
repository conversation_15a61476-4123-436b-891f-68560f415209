package value

type DifferenceReasonClass int

const (
	DifferenceReasonClassOthers           = DifferenceReasonClass(0)
	DifferenceReasonClassPcUsedNotForWork = DifferenceReasonClass(1)
	DifferenceReasonClassPcUnnecessarily  = DifferenceReasonClass(2)
	DifferenceReasonClassPcLogUnreadable  = DifferenceReasonClass(3)
	DifferenceReasonClassPcOffOmission    = DifferenceReasonClass(4)
)

type DifferenceReasonClassVal string

var DifferenceReasonClassConv = map[DifferenceReasonClass]DifferenceReasonClassVal{
	DifferenceReasonClassOthers:           DifferenceReasonClassVal("attendance_difference.reason_class.others"),
	DifferenceReasonClassPcUsedNotForWork: DifferenceReasonClassVal("attendance_difference.reason_class.pc_used_not_for_work"),
	DifferenceReasonClassPcUnnecessarily:  DifferenceReasonClassVal("attendance_difference.reason_class.pc_unnecessarily"),
	DifferenceReasonClassPcLogUnreadable:  DifferenceReasonClassVal("attendance_difference.reason_class.pc_log_unreadable"),
	DifferenceReasonClassPcOffOmission:    DifferenceReasonClassVal("attendance_difference.reason_class.pc_off_omission"),
}
