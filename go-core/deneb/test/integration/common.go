package integration

import (
	"net/http/httptest"
	"time"

	"github.com/DATA-DOG/go-sqlmock"
	"github.com/gin-gonic/gin"
	"gitlab.com/innopm/deneb/common/ictx"
	"gitlab.com/innopm/deneb/common/ilog"
	"gitlab.com/innopm/deneb/config"
	"gitlab.com/innopm/deneb/domain/model"
	"gorm.io/driver/mysql"
	"gorm.io/gorm"
)

func GetDBMock() (*gorm.DB, sqlmock.Sqlmock, error) {
	db, mock, err := sqlmock.New()
	if err != nil {
		return nil, nil, err
	}

	gdb, err := gorm.Open(mysql.New(mysql.Config{
		Conn:                      db,
		SkipInitializeWithVersion: true, // skip mysql version
	}), &gorm.Config{})
	if err != nil {
		return nil, nil, err
	}
	return gdb, mock, nil
}

func GenGinIctx(compID, empID int) ictx.Context {
	lp := ilog.LogParams{
		Address:   "***************",
		RequestID: "XXXXXXX",
		Route:     "/test/",
		EmpNo:     "GUEST",
		CompNo:    "GUEST",
	}
	logger := ilog.InitInpmLogger(lp)
	ginContext, _ := gin.CreateTestContext(httptest.NewRecorder())
	ginContext.Keys = map[string]interface{}{}
	ginContext.Keys["company_id"] = compID
	ginContext.Keys["user_id"] = empID
	ginContext.Keys["logger"] = logger
	ginContext.Keys["user_info"] = model.UserInfo{}
	ginContext.Keys["access_info"] = model.AccessInfo{
		IPAddress:  "*********",
		AccessType: "web",
	}
	ictx := ictx.NewCtx(ginContext)
	return ictx
}

func GenGinIctxWithUserInfo(compID, empID int, userInfo model.UserInfo) ictx.Context {
	lp := ilog.LogParams{
		Address:   "***************",
		RequestID: "XXXXXXX",
		Route:     "/test/",
		EmpNo:     "GUEST",
		CompNo:    "GUEST",
	}
	logger := ilog.InitInpmLogger(lp)
	ginContext, _ := gin.CreateTestContext(httptest.NewRecorder())
	ginContext.Keys = map[string]interface{}{}
	ginContext.Keys["company_id"] = compID
	ginContext.Keys["user_id"] = empID
	ginContext.Keys["logger"] = logger
	ginContext.Keys["user_info"] = userInfo
	ginContext.Keys["access_info"] = model.AccessInfo{
		IPAddress:  "*********",
		AccessType: "web",
	}
	ictx := ictx.NewCtx(ginContext)
	return ictx
}

func SetIntegConfigHCTimeout(timeout int) {
	if config.Integ == nil {
		config.Integ = &config.Integration{}
	}
	config.Integ.HttpClient.Timeout = timeout
}

func SetIntegConfigJTDuration(duration int) {
	if config.Integ == nil {
		config.Integ = &config.Integration{}
	}
	config.Integ.Jobcan.TokenDuration = duration
}

func SetIntegConfigSummaryCount(count int) {
	if config.Integ == nil {
		config.Integ = &config.Integration{}
	}
	config.Integ.Jobcan.SummaryCount = count
}

func SetIntegConfigTAurl(url string) {
	if config.Integ == nil {
		config.Integ = &config.Integration{}
	}
	config.Integ.Jobcan.TokenApiUrl = url
}

func SetIntegConfigSAurl(url string) {
	if config.Integ == nil {
		config.Integ = &config.Integration{}
	}
	config.Integ.Jobcan.SummaryApiUrl = url
}

func SetIntegConfigEAUrl(url string) {
	if config.Integ == nil {
		config.Integ = &config.Integration{}
	}
	config.Integ.Jobcan.EmployeeApiUrl = url
}

func SetIntegConfigECount(count int) {
	if config.Integ == nil {
		config.Integ = &config.Integration{}
	}
	config.Integ.Jobcan.EmployeeCount = count
}

func CreateTestContext(userID, compID int, userInfo *model.UserInfo, plan *string) ictx.Context {
	w := httptest.NewRecorder()
	c, _ := gin.CreateTestContext(w)
	ictx.SetUserID(c, userID)
	ictx.SetCompID(c, compID)

	uInfo := CreateDefaultUserInfo()
	if userInfo != nil {
		uInfo = *userInfo
	}
	ictx.SetUserInfo(c, uInfo)
	ictx.SetAccessInfo(c, CreateDefaultAccessInfo())

	logParams := ilog.LogParams{}
	ilogger := ilog.InitInpmLogger(logParams)

	ictx.SetLogger(c, ilogger)
	if plan == nil {
		defaultPlan := "TRL"
		plan = &defaultPlan
	}
	logger := ictx.GetLogger(c)
	logger.SetUserInfo(compID, userID, *plan)

	return ictx.NewCtx(c)
}

func CreateDefaultAccessInfo() model.AccessInfo {
	return model.AccessInfo{
		IPAddress:  "*********",
		AccessType: "api",
	}
}

// CreateDefaultUserInfo default Userstting for Testing
func CreateDefaultUserInfo() model.UserInfo {
	tz, _ := time.LoadLocation("Asia/Tokyo")
	return model.UserInfo{
		CurrencyDigit:                0, // JPY
		CostMax:                      1.0,
		HoursInMonth:                 160,
		HoursInDay:                   8,
		JobtimeProcessCount:          3,
		Location:                     tz,
		CompanyGanttFlag:             true,
		UseTimesheetApproval:         true,
		CompanyWorktimeStep:          15,
		SysAdminFlag:                 true,
		DepartmentNo:                 1,
		Language:                     config.DefaultLanguage,
		AttendanceManageUseType:      false,
		UseTimesheetAttendance:       true,
		FutureFlag:                   true,
		UseDayOff:                    true,
		CompanyLocation:              tz,
		CompanyLanguage:              "en",
		FamilyName:                   "Crowdlog",
		FirstName:                    "Devs",
		AttendanceWorkMatchThreshold: 15,
		WorktimeTimeFormula:          "duration",
		UseWorkReportSummary:         true,
	}
}
