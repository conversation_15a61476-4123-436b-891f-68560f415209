package usecase

import (
	"errors"
	"fmt"
	"strconv"
	"time"

	"gitlab.com/innopm/deneb/common/ictx"
	"gitlab.com/innopm/deneb/config"
	"gitlab.com/innopm/deneb/domain/context"
	"gitlab.com/innopm/deneb/domain/model"
	"gitlab.com/innopm/deneb/domain/model/req"
	"gitlab.com/innopm/deneb/domain/model/tbl"
	"gitlab.com/innopm/deneb/domain/repo"
	infra "gitlab.com/innopm/deneb/infra/integration"
	"gitlab.com/innopm/deneb/registry"
	"gitlab.com/innopm/deneb/util"
	"gitlab.com/innopm/deneb/util/boolutil"
	"gitlab.com/innopm/deneb/util/dateutil"
	"gitlab.com/innopm/deneb/util/intutil"
	"gorm.io/gorm"
)

var ymdFmt = "2006-01-02"
var timeFmt = "2006-01-02 15:04:05"

type IntegAttendanceStore interface {
	StoreAtts(actx context.Context, attdata infra.AttData) error
	StoreByEmployees(context.Context, []model.IntegAttByEmployee)
}

type StoreReq struct {
	repoRegi            registry.RepoRegistry
	integAttendanceRepo repo.IntegrationAttendance
	integAttRepo        repo.IntegratedAttendance
	tsRestRepo          repo.TimesheetRest
}

func NewStoreAtts(r registry.RepoRegistry) StoreReq {
	return StoreReq{
		repoRegi:            r,
		integAttendanceRepo: r.NewIntegrationAttendanceRepo(),
		integAttRepo:        r.NewIntegratedAttendanceRepo(),
		tsRestRepo:          r.NewTimesheetRestRepo(),
	}
}

func (s StoreReq) StoreAtts(actx context.Context, attdata infra.AttData) error {
	for _, adByID := range attdata {
		err := s.storeAttsByEmp(actx, adByID)
		if err != nil {
			return err
		}
	}
	return nil
}

func (s StoreReq) storeAttsByEmp(actx context.Context, ad infra.AttDataByID) error {
	reg := s.repoRegi

	for _, att := range ad.AttDataByDay {
		if err := s.upsertIntegratedAtt(actx, *att.IntegratedAtt); err != nil {
			actx.GetNotification().AppendNotification(
				context.NotifCannotSaveAttendance, ad.Id, att.Day, "save integrated attendance")
			continue
		}

		if att.TimeSheet == nil {
			continue
		}

		svdAtt, err := NewAttendance(reg).Save(
			*actx.GetIContext(), ad.Id,
			s.convertAttendance(actx, *att.TimeSheet))
		if err != nil {
			actx.GetNotification().AppendNotification(
				context.NotifCannotSaveAttendance, ad.Id, att.Day, "save timesheet")
			continue
		}

		if err := s.deleteTimesheetRest(actx, svdAtt.UserID,
			svdAtt.TimesheetID); err != nil {
			if !errors.Is(err, gorm.ErrRecordNotFound) {
				actx.GetNotification().AppendNotification(
					context.NotifCannotSaveAttendance, ad.Id, att.Day, "delete timesheet_rest")
				continue
			}
		}

		if att.TimeSheetRest != nil {
			_, err = NewTimesheetRest(reg).Create(
				*actx.GetIContext(), ad.Id,
				s.convertRest(*att.TimeSheetRest))
			if err != nil {
				actx.GetNotification().AppendNotification(
					context.NotifCannotSaveAttendance, ad.Id, att.Day, "save timesheet_rest")
			}
		}
	}
	return nil
}

func (s StoreReq) deleteTimesheetRest(actx context.Context, empID, tsID int) error {
	reg := s.repoRegi
	ctx := *actx.GetIContext()

	tsrs, err := (reg).NewTimesheetRestRepo().FindByTimesheetID(tsID)
	if err != nil {
		return err
	}
	for _, tsr := range tsrs {
		err := NewTimesheetRest(reg).Delete(ctx, empID, tsr.RestNo)
		if err != nil {
			return err
		}
	}
	return nil
}

func (s StoreReq) convertAttendance(actx context.Context, ts tbl.Timesheet) req.AttendanceOfSave {
	if intutil.ToZeroIfNil(ts.HolidayType) == int(config.TsHldyWholeDay) {
		st := ts.TimesheetDate.Format(ymdFmt) + " " + (*actx.GetIContext()).GetUserInfo().StartTime
		et := ts.TimesheetDate.Format(ymdFmt) + " " + (*actx.GetIContext()).GetUserInfo().EndTime
		sdt, _ := time.Parse(timeFmt, st)
		edt, _ := time.Parse(timeFmt, et)
		return req.AttendanceOfSave{
			StartDateTime:             &req.ISODateTime{Time: sdt},
			EndDateTime:               &req.ISODateTime{Time: edt},
			HolidayType:               ts.HolidayType,
			AutoCalculationAttendance: boolutil.ToPtr(false),
		}
	}

	tsd := ts.TimesheetDate
	hour := s.getHour(ts.EndTime)
	if hour >= 24 {
		tsd = ts.TimesheetDate.AddDate(0, 0, 1)
		hour = hour - 24
	}

	st := ts.TimesheetDate.Format(ymdFmt) + " " + *ts.StartTime
	et := tsd.Format(ymdFmt) + " " + fmt.Sprintf("%02d", hour) + ":" + (*ts.EndTime)[3:8]
	sdt, _ := time.Parse(timeFmt, st)
	edt, _ := time.Parse(timeFmt, et)
	return req.AttendanceOfSave{
		StartDateTime:             &req.ISODateTime{Time: sdt},
		EndDateTime:               &req.ISODateTime{Time: edt},
		HolidayType:               ts.HolidayType,
		AutoCalculationAttendance: boolutil.ToPtr(false),
	}
}

func (s StoreReq) convertRest(tsr tbl.TimesheetRest) req.BreakOfSave {
	st := tsr.StartDate.Add(s.calcAddDur(tsr.StartTime, tsr.StartTime))
	et := tsr.EndDate.Add(s.calcAddDur(tsr.EndTime, tsr.EndTime))

	return req.BreakOfSave{
		StartDateTime: &req.ISODateTime{Time: st},
		EndDateTime:   &req.ISODateTime{Time: et}}
}

func (s StoreReq) getHour(time *string) int {
	h, _ := strconv.Atoi((*time)[0:2])
	return h
}

func (s StoreReq) getMin(time *string) int {
	m, _ := strconv.Atoi((*time)[3:5])
	return m
}

func (s StoreReq) calcAddDur(hour, min *string) time.Duration {
	return time.Hour*time.Duration(s.getHour(hour)) +
		time.Minute*time.Duration(s.getMin(min))
}

func (s StoreReq) upsertIntegratedAtt(actx context.Context,
	integAtt tbl.IntegratedAttendance) (err error) {
	if s.integAttRepo.Exist(integAtt.CompanyNo, integAtt.EmployeeNo, integAtt.TimesheetDate) {
		integAtt.CreateUser = 0
		err = s.integAttRepo.Update(&integAtt)
		return
	}

	err = s.integAttRepo.Create(&integAtt)
	return
}

func (s StoreReq) StoreByEmployees(integCtx context.Context,
	integAttsByEmps []model.IntegAttByEmployee) {
	defer func() {
		s.flashLog(integCtx)
	}()
	for _, integAttsByEmp := range integAttsByEmps {
		s.storeByEmployee(integCtx, integAttsByEmp)
	}
}

func (s StoreReq) flashLog(integCtx context.Context) {
	integCtx.GetNotification().FlashLog(context.NotifCannotConvertAttendance)
	integCtx.GetNotification().FlashLog(context.NotifAttendanceSynchronizedByAnotherConditionExisted)
	integCtx.GetNotification().FlashLog(context.NotifCannotSaveAttendance)
}

func (s StoreReq) storeByEmployee(integCtx context.Context,
	integAttsByEmps model.IntegAttByEmployee) {
	for _, integAtt := range integAttsByEmps.Attendances {
		if err := s.validateAttendanceInfo(integAtt); err != nil {
			integCtx.GetNotification().AppendNotification(context.NotifCannotSaveAttendance,
				integAtt.EmpID, integAtt.Date, err.Error())
			continue
		}
		s.storeByEmployeeAndDay(integCtx, integAttsByEmps.Employee, integAtt)
	}
}

func (s StoreReq) validateAttendanceInfo(att model.IntegAttInfo) error {

	if att.IntegrationSettingID == 0 {
		return fmt.Errorf("invalid integration setting ID: %d", att.IntegrationSettingID)
	}

	if att.Date.IsZero() {
		return fmt.Errorf("timesheet date is zero value")
	}

	if att.EmpID == 0 {
		return fmt.Errorf("invalid emp ID: %d", att.EmpID)
	}

	if att.HolidayType < int(config.TsHldyNone) || att.HolidayType > int(config.TsHldyPM) {
		return fmt.Errorf("invalid holiday type: %d",
			att.HolidayType)
	}

	if att.StartDateTime.IsZero() || att.EndDateTime.IsZero() {
		return fmt.Errorf("start or end time is zero time: start:%v, end:%v",
			att.StartDateTime, att.EndDateTime)
	}

	for _, rest := range att.Rests {
		if rest.StartDateTime.IsZero() || rest.EndDateTime.IsZero() {
			return fmt.Errorf("rest start or end time is zero time: start:%v, end:%v",
				rest.StartDateTime, rest.EndDateTime)
		}

		if rest.EndDateTime.Before(rest.StartDateTime) {
			return fmt.Errorf("rest end time is before start time: start:%v, end:%v",
				rest.StartDateTime, rest.EndDateTime)
		}

		if rest.StartDateTime.Before(att.StartDateTime) {
			return fmt.Errorf("rest start time is before attendance start time: rest start:%v, attendance start:%v",
				rest.StartDateTime, att.StartDateTime)
		}

		if rest.EndDateTime.After(att.EndDateTime) {
			return fmt.Errorf("rest end time is after attendance end time: rest end:%v, attendance end:%v",
				rest.EndDateTime, att.EndDateTime)
		}
	}

	zeroTsDate := dateutil.HourClockTotalDurToTime(att.Date, "00:00:00")
	if att.HolidayType == int(config.TsHldyWholeDay) &&
		!(zeroTsDate.Equal(att.StartDateTime) && zeroTsDate.Equal(att.EndDateTime)) {
		return fmt.Errorf("attendance of whole day off has start time or end time(not zero time): start:%v, end:%v",
			att.StartDateTime, att.EndDateTime)
	}

	if att.HolidayType == int(config.TsHldyWholeDay) && att.RestTime > 0 {
		return fmt.Errorf("attendance of whole day off has rest time: rest time mins:%v", att.RestTime)
	}

	if att.HolidayType == int(config.TsHldyWholeDay) && att.WorkTime > 0 {
		return fmt.Errorf("attendance of whole day off has work time: work time mins:%v", att.WorkTime)
	}

	if att.HolidayType != int(config.TsHldyWholeDay) &&
		(att.EndDateTime.Before(att.StartDateTime) || att.EndDateTime.Equal(att.StartDateTime)) {
		return fmt.Errorf("start time is before end time: start:%v, end:%v",
			att.StartDateTime, att.EndDateTime)
	}

	return nil
}

func (s StoreReq) storeByEmployeeAndDay(integCtx context.Context,
	empInfo model.IntegAttEmployeeInfo, attInfo model.IntegAttInfo) {
	ictx := *integCtx.GetIContext()
	usrInfo := ictx.GetUserInfo()

	// save integrated_attendance
	err := s.saveIntegratedAtt(integCtx, attInfo, integCtx.GetIntegrationAttendanceID())
	if err != nil {
		integCtx.GetNotification().AppendNotification(context.NotifCannotSaveAttendance,
			attInfo.EmpID, attInfo.Date, "cannot save integrated attendance")
		return
	}

	// generate timesheet attendance request
	attReq, isErr := s.genAttendanceReq(usrInfo.StartTime, usrInfo.EndTime, empInfo.DayEndTime, attInfo)
	if isErr {
		integCtx.GetNotification().AppendNotification(context.NotifCannotConvertAttendance,
			attInfo.EmpID, attInfo.Date, "cannot convert timesheet attendance time")
		return
	}

	// save timesheet attendance
	svdAtt, err := NewAttendance(s.repoRegi).Save(ictx, empInfo.ID, attReq)
	if err != nil {
		integCtx.GetNotification().AppendNotification(context.NotifCannotSaveAttendance,
			attInfo.EmpID, attInfo.Date, "cannot save timesheet attendance")
		return
	}

	// generate breaks request
	breakReqs, breakDelIds, isErr := s.genBreakReqs(attInfo.RestTime, svdAtt.TimesheetID,
		util.DateToTime(*svdAtt.StartDateTime), util.DateToTime(*svdAtt.EndDateTime))
	if isErr {
		integCtx.GetNotification().AppendNotification(context.NotifCannotConvertAttendance,
			attInfo.EmpID, attInfo.Date, "cannot convert timesheet rests")
		return
	}

	// create manually stamped rests
	if len(attInfo.Rests) > 0 {
		breaksReq := make([]req.BreakOfSave, 0, len(attInfo.Rests))
		for _, r := range attInfo.Rests {
			breaksReq = append(breaksReq,
				req.BreakOfSave{
					StartDateTime: &req.ISODateTime{Time: r.StartDateTime},
					EndDateTime:   &req.ISODateTime{Time: r.EndDateTime},
				},
			)
		}
		err := s.createTimesheetRests(ictx, svdAtt.UserID, breaksReq)
		if err != nil {
			integCtx.GetNotification().AppendNotification(
				context.NotifCannotSaveAttendance,
				attInfo.EmpID, attInfo.Date, "cannot save timesheet rests which are from manually stamps at integration system")
		}
	}

	// delete old timesheet rests
	if len(breakDelIds) > 0 {
		err := s.deleteTimesheetRests(ictx, svdAtt.UserID, breakDelIds)
		if err != nil {
			integCtx.GetNotification().AppendNotification(
				context.NotifCannotSaveAttendance,
				attInfo.EmpID, attInfo.Date, "cannot delete timesheet rest")
			return
		}
	}

	// create timesheet rests
	if len(breakReqs) > 0 {
		err := s.createTimesheetRests(ictx, svdAtt.UserID, breakReqs)
		if err != nil {
			integCtx.GetNotification().AppendNotification(
				context.NotifCannotSaveAttendance,
				attInfo.EmpID, attInfo.Date, "cannot save timesheet rest")
		}
	}
}

func (s StoreReq) genAttendanceReq(compStartTime, compEndTime, dayEndTime string,
	attInfo model.IntegAttInfo) (attReq req.AttendanceOfSave, isErr bool) {
	// start and end time
	att, isErr := s.convertAttendanceTime(compStartTime, compEndTime, dayEndTime, attInfo)
	if isErr {
		return
	}
	// attendance request
	attReq.StartDateTime = &req.ISODateTime{Time: att.StartDateTime}
	attReq.EndDateTime = &req.ISODateTime{Time: att.EndDateTime}
	attReq.HolidayType = &attInfo.HolidayType
	attReq.AutoCalculationAttendance = boolutil.ToPtr(false)

	return
}

func (s StoreReq) genBreakReqs(attRestTime, tsID int,
	tsStartDateTime, tsEndDateTime time.Time) (breaksReq []req.BreakOfSave,
	tsRestDelIds []int, isErr bool) {
	// convert rest time
	rests, tsRestDelIds, isErr := s.convertRestTime(attRestTime,
		tsID, tsStartDateTime, tsEndDateTime)
	if isErr {
		return
	}

	for _, rest := range rests {
		breaksReq = append(breaksReq,
			req.BreakOfSave{
				StartDateTime: &req.ISODateTime{Time: rest.StartDateTime},
				EndDateTime:   &req.ISODateTime{Time: rest.EndDateTime},
			},
		)
	}

	return
}

func (s StoreReq) convertAttendanceTime(compStartTime, compEndTime, dayEndTime string,
	attInfo model.IntegAttInfo) (att model.IntegAttDateTime, isErr bool) {
	// day off
	if attInfo.HolidayType == int(config.TsHldyWholeDay) {
		att = model.NewIntegAttDateTime(
			dateutil.HourClockTotalDurToTime(attInfo.Date, compStartTime),
			dateutil.HourClockTotalDurToTime(attInfo.Date, compEndTime),
		)
		return
	}

	// work_time
	startDateTime := attInfo.StartDateTime.Truncate(time.Minute)
	endDateTime := attInfo.EndDateTime.Truncate(time.Minute)

	// error if more than one day(24 hours)
	if (endDateTime.Sub(startDateTime)) > 24*time.Hour {
		isErr = true
		return
	}

	// day_end_time
	minDayEndTime := dateutil.HourClockTotalDurToTime(attInfo.Date, dayEndTime)
	maxDayEndTime := minDayEndTime.AddDate(0, 0, 1)

	if startDateTime.Before(minDayEndTime) {
		endDateTime = endDateTime.Add(minDayEndTime.Sub(startDateTime))
		startDateTime = minDayEndTime
	}

	if endDateTime.After(maxDayEndTime) {
		startDateTime = startDateTime.Add(maxDayEndTime.Sub(endDateTime))
		endDateTime = maxDayEndTime
	}

	att = model.NewIntegAttDateTime(startDateTime, endDateTime)
	return
}

func (s StoreReq) convertRestTime(attRestTime, tsID int, tsStartDateTime,
	tsEndDateTime time.Time) (rests []model.IntegRestDateTime, tsRestDelIds []int, isErr bool) {
	// rest inside work time
	tsRestsInWorkTime, tsRestDelIds, isErr := s.findTimesheetRestInWorktime(tsID, tsStartDateTime, tsEndDateTime)
	if isErr {
		return
	}

	// no rest time
	if attRestTime <= 0 {
		return
	}

	// workTime < restTime
	workTime := int(tsEndDateTime.Sub(tsStartDateTime).Minutes())
	if workTime < attRestTime {
		isErr = true
		return
	}

	// no timesheet rest
	if len(tsRestsInWorkTime) == 0 {
		rests = append(rests, s.calcMidRestTime(tsStartDateTime, tsEndDateTime, attRestTime))
		return
	}

	rests = s.distributeRestTime(tsStartDateTime, tsEndDateTime, attRestTime, tsRestsInWorkTime)
	return
}

func (s StoreReq) calcMidRestTime(tsStartDateTime, tsEndDateTime time.Time,
	attRestTime int) model.IntegRestDateTime {
	// get mid of work time
	midWorkTime := tsStartDateTime.Add(tsEndDateTime.Sub(tsStartDateTime) / 2)
	// round down mid time to hour
	roundedMidWorkTime := time.Date(midWorkTime.Year(), midWorkTime.Month(), midWorkTime.Day(),
		midWorkTime.Hour(), 0, 0, 0, midWorkTime.Location())

	// roundedMidWorkTime < tsStartDateTime
	if roundedMidWorkTime.Before(tsStartDateTime) {
		return model.NewIntegRestDateTime(
			tsStartDateTime,
			tsStartDateTime.Add(time.Duration(attRestTime)*time.Minute),
		)
	}

	// minus one(1) hour
	restStart := roundedMidWorkTime.Add(time.Duration(-1) * time.Hour)
	restEnd := restStart.Add(time.Duration(attRestTime) * time.Minute)
	// restStart >= tsStartDateTime && restEnd <= tsEndDateTime
	if !tsStartDateTime.After(restStart) && !tsEndDateTime.Before(restEnd) {
		return model.NewIntegRestDateTime(restStart, restEnd)
	}

	restStart = roundedMidWorkTime
	restEnd = restStart.Add(time.Duration(attRestTime) * time.Minute)
	// restStart >= tsStartDateTime && restEnd <= tsEndDateTime
	if !tsStartDateTime.After(restStart) && !tsEndDateTime.Before(restEnd) {
		return model.NewIntegRestDateTime(restStart, restEnd)
	}

	return model.NewIntegRestDateTime(
		tsStartDateTime,
		tsStartDateTime.Add(time.Duration(attRestTime)*time.Minute),
	)
}

func (s StoreReq) calRestDuration(tsStartDateTime, tsEndDateTime time.Time,
	tsRest tbl.TimesheetRest) (restStartDateTime, restEndDateTime time.Time, restDurationMin int) {
	restStartDateTime = util.DateToTime(dateutil.ToDateFormat(*tsRest.StartDate) + " " + *tsRest.StartTime)
	restEndDateTime = util.DateToTime(dateutil.ToDateFormat(*tsRest.EndDate) + " " + *tsRest.EndTime)

	// restStartDateTime < tsStartDateTime
	if restStartDateTime.Before(tsStartDateTime) {
		restStartDateTime = tsStartDateTime
	}
	// restEndDateTime > tsEndDateTime
	if restEndDateTime.After(tsEndDateTime) {
		restEndDateTime = tsEndDateTime
	}

	restDurationMin = int(restEndDateTime.Sub(restStartDateTime).Minutes())
	return
}

func (s StoreReq) distributeRestTime(tsStartDateTime, tsEndDateTime time.Time,
	attRestTime int, tsRestsInWorkTime []tbl.TimesheetRest) (rests []model.IntegRestDateTime) {

	// offset rest time by existing timesheet rest
	offsetAttRestTime := 0
	offsetRestsInWorkTime := make([]tbl.TimesheetRest, 0, len(tsRestsInWorkTime))
	for _, tsRest := range tsRestsInWorkTime {
		if offsetAttRestTime >= attRestTime {
			break
		}

		_, _, restDurationMin := s.calRestDuration(tsStartDateTime, tsEndDateTime, tsRest)
		offsetAttRestTime += restDurationMin
		offsetRestsInWorkTime = append(offsetRestsInWorkTime, tsRest)
	}

	// distribute rest to existing timesheet rest from earliest to latest
	excessRestTime, rests := s.distRestTimeToExistingTsRest(tsStartDateTime,
		tsEndDateTime, attRestTime, offsetRestsInWorkTime)

	if excessRestTime <= 0 {
		return
	}

	// distribute remaining rest from latest to earliest
	return s.distRmngRestTimeToExistingTsRest(tsEndDateTime, excessRestTime, rests)
}

// distribute rest time to existing timesheet rest
func (s StoreReq) distRestTimeToExistingTsRest(tsStartDateTime, tsEndDateTime time.Time,
	attRestTime int, tsRestsInWorkTime []tbl.TimesheetRest) (excessRestTime int, rests []model.IntegRestDateTime) {
	excessRestTime = attRestTime
	for _, tsRest := range tsRestsInWorkTime {
		if excessRestTime <= 0 {
			break
		}

		restStartDateTime, restEndDateTime, restDurationMin := s.calRestDuration(tsStartDateTime, tsEndDateTime, tsRest)

		if excessRestTime < restDurationMin {
			restEndDateTime = restStartDateTime.Add(time.Duration(excessRestTime) * time.Minute)
		}

		excessRestTime -= restDurationMin
		rests = append(rests, model.NewIntegRestDateTime(restStartDateTime, restEndDateTime))
	}
	return
}

// distribute remaining rest time to existing timesheet rest
func (s StoreReq) distRmngRestTimeToExistingTsRest(restDateTimeLimit time.Time,
	excessRestTime int, rests []model.IntegRestDateTime) []model.IntegRestDateTime {
	// reverse loop
	for i := len(rests) - 1; i >= 0 && excessRestTime > 0; i-- {
		restEndDateTime := rests[i].EndDateTime
		restDuration := restDateTimeLimit.Sub(restEndDateTime)

		if excessRestTime < int(restDuration.Minutes()) {
			restDuration = time.Duration(excessRestTime) * time.Minute
		}

		restEndDateTime = restEndDateTime.Add(restDuration)
		excessRestTime -= int(restDuration.Minutes())

		// if earliest rest change start time
		restStartDateTime := rests[i].StartDateTime
		if i == 0 && excessRestTime > 0 {
			restStartDateTime = restStartDateTime.Add(-time.Duration(excessRestTime) * time.Minute)
			excessRestTime = 0
		}

		// set limit for next rest
		restDateTimeLimit = restStartDateTime

		rests[i].StartDateTime = restStartDateTime
		rests[i].EndDateTime = restEndDateTime
	}
	return rests
}

func (s StoreReq) findTimesheetRestInWorktime(tsID int, tsStartDateTime,
	tsEndDateTime time.Time) (tsRestsInWorkTime []tbl.TimesheetRest, tsRestDelIds []int, isErr bool) {
	// timesheet rests
	tsRests, err := s.tsRestRepo.FindByTimesheetID(tsID)
	if err != nil {
		isErr = true
		return
	}

	for _, tsRest := range tsRests {
		// incomplete rest
		if tsRest.StartDate == nil || tsRest.StartTime == nil ||
			tsRest.EndDate == nil || tsRest.EndTime == nil {
			tsRestDelIds = append(tsRestDelIds, tsRest.RestNo) // delete incomplete
			continue
		}

		tsRestStartDateTime := util.DateToTime(dateutil.ToDateFormat(*tsRest.StartDate) + " " + *tsRest.StartTime)
		tsRestEndDateTime := util.DateToTime(dateutil.ToDateFormat(*tsRest.EndDate) + " " + *tsRest.EndTime)

		// tsStartDateTime <= tsRestEndDateTime && tsEndDateTime >= tsRestStartDateTime
		if tsRestEndDateTime.After(tsStartDateTime) && tsRestStartDateTime.Before(tsEndDateTime) {
			tsRestsInWorkTime = append(tsRestsInWorkTime, tsRest)
			tsRestDelIds = append(tsRestDelIds, tsRest.RestNo) // delete inside worktime
		}
	}
	return
}

func (s StoreReq) saveIntegratedAtt(integCtx context.Context, attInfo model.IntegAttInfo, integAttId int) error {
	ctx := *integCtx.GetIContext()
	compID := ctx.GetCompID()
	userID := ctx.GetUserID()
	// holiday use
	holidayUse := false
	if attInfo.HolidayType > int(config.TsHldyNone) {
		holidayUse = true
	}
	integAtt := tbl.IntegratedAttendance{
		CompanyNo:               compID,
		EmployeeNo:              attInfo.EmpID,
		TimesheetDate:           attInfo.Date,
		TargetSystem:            attInfo.System,
		IntegrationAttendanceId: integAttId,
		StartTime:               dateutil.TimeDiffToHourClock(attInfo.Date, attInfo.StartDateTime),
		EndTime:                 dateutil.TimeDiffToHourClock(attInfo.Date, attInfo.EndDateTime),
		HolidayUse:              holidayUse,
		RestTime:                attInfo.RestTime,
		CreateUser:              userID,
		UpdateUser:              userID,
	}

	tbl, err := s.integAttRepo.FindSpecific(integAtt.CompanyNo, integAtt.EmployeeNo, integAtt.TimesheetDate)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return s.integAttRepo.Create(&integAtt)
		}
		return err
	}

	err = s.checkSameSyncCondition(integCtx, tbl, attInfo)
	if err != nil {
		return err
	}

	return s.integAttRepo.Update(&integAtt)
}

func (s StoreReq) createTimesheetRests(c ictx.Context, empID int, breaksReq []req.BreakOfSave) (err error) {
	for _, breakReq := range breaksReq {
		_, err = NewTimesheetRest(s.repoRegi).Create(c, empID, breakReq)
		if err != nil {
			return
		}
	}
	return
}

func (s StoreReq) deleteTimesheetRests(c ictx.Context, empID int, ids []int) (err error) {
	for _, id := range ids {
		err = NewTimesheetRest(s.repoRegi).Delete(c, empID, id)
		if err != nil {
			return
		}
	}
	return
}

func (s StoreReq) checkSameSyncCondition(
	ctx context.Context,
	integratedAttTbl tbl.IntegratedAttendance,
	attInfo model.IntegAttInfo) error {

	if integratedAttTbl.IntegrationAttendanceId <= 0 {
		// can not get integration_attendance
		info := fmt.Sprintf("%d(0)", integratedAttTbl.IntegrationAttendanceId)
		ctx.GetNotification().AppendNotification(
			context.NotifAttendanceSynchronizedByAnotherConditionExisted,
			attInfo.EmpID, attInfo.Date, info)
		return nil
	}

	// Get IntegrationAttendance
	tbl, err := s.integAttendanceRepo.FindSpecific(
		ctx.GetCompID(), integratedAttTbl.IntegrationAttendanceId, nil)
	if err != nil {
		return err
	}

	// Output log in case of different setting
	if integratedAttTbl.TargetSystem != string(ctx.GetTargetSystem()) ||
		tbl.IntegrationAttendance.IntegrationSettingID != attInfo.IntegrationSettingID {
		info := fmt.Sprintf("%d(%d)",
			integratedAttTbl.IntegrationAttendanceId, tbl.IntegrationSettingID)
		ctx.GetNotification().AppendNotification(
			context.NotifAttendanceSynchronizedByAnotherConditionExisted,
			attInfo.EmpID, attInfo.Date, info)
	}
	return nil
}
