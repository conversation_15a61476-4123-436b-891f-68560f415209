-- Test script to demonstrate the department history overlap issue and verify the fix

-- Create test data
INSERT INTO company (company_no, company_name) VALUES (1, 'Test Company') ON DUPLICATE KEY UPDATE company_name = 'Test Company';

INSERT INTO department (department_no, company_no, management_code, department_name, order_no, parent_no, active_flag) VALUES 
(1, 1, 'DEPT001', 'Department A', 1, 0, 1),
(2, 1, 'DEPT002', 'Department B', 2, 0, 1),
(3, 1, 'DEPT003', 'Department C', 3, 0, 1)
ON DUPLICATE KEY UPDATE department_name = VALUES(department_name);

-- Test Employee 1: Has department history (A -> B -> C)
INSERT INTO employee (employee_no, company_no, department_no, first_name, family_name, start_date) VALUES 
(1, 1, 3, 'John', 'Doe', '2023-01-01')
ON DUPLICATE KEY UPDATE department_no = 3, start_date = '2023-01-01';

-- Employee 1's department history
INSERT INTO employee_department_history (company_no, employee_no, department_no, end_date) VALUES 
(1, 1, 1, '2023-03-31'),  -- Department A: 2023-01-01 to 2023-03-31
(1, 1, 2, '2023-06-30')   -- Department B: 2023-04-01 to 2023-06-30
ON DUPLICATE KEY UPDATE end_date = VALUES(end_date);
-- Current department C: 2023-07-01 to 9999-12-31

-- Test Employee 2: No department history (always in Department A)
INSERT INTO employee (employee_no, company_no, department_no, first_name, family_name, start_date) VALUES 
(2, 1, 1, 'Jane', 'Smith', '2023-01-01')
ON DUPLICATE KEY UPDATE department_no = 1, start_date = '2023-01-01';

-- Test Employee 3: Has department history but current department same as last historical
INSERT INTO employee (employee_no, company_no, department_no, first_name, family_name, start_date) VALUES 
(3, 1, 2, 'Bob', 'Johnson', '2023-01-01')
ON DUPLICATE KEY UPDATE department_no = 2, start_date = '2023-01-01';

-- Employee 3's department history (this should cause overlap in the original code)
INSERT INTO employee_department_history (company_no, employee_no, department_no, end_date) VALUES 
(1, 3, 1, '2023-03-31'),  -- Department A: 2023-01-01 to 2023-03-31
(1, 3, 2, '2023-06-30')   -- Department B: 2023-04-01 to 2023-06-30
ON DUPLICATE KEY UPDATE end_date = VALUES(end_date);
-- Current department B: 2023-07-01 to 9999-12-31 (same as last historical department)

-- Query to show the overlap issue with original logic
SELECT 
    'ORIGINAL_LOGIC_SIMULATION' as test_type,
    employee_no,
    department_no,
    department_start_date,
    department_end_date,
    CASE 
        WHEN department_end_date = '9999-12-31' THEN 'CURRENT'
        ELSE 'HISTORICAL'
    END as record_type
FROM (
    -- Original current department query (simplified)
    SELECT 
        e.employee_no,
        e.department_no,
        IFNULL(
            DATE_ADD((
                SELECT MAX(end_date) 
                FROM employee_department_history edh 
                WHERE edh.employee_no = e.employee_no AND edh.company_no = e.company_no
            ), INTERVAL 1 DAY),
            e.start_date
        ) as department_start_date,
        '9999-12-31' as department_end_date
    FROM employee e
    WHERE e.company_no = 1
    
    UNION ALL
    
    -- Original historical department query (simplified)
    SELECT 
        edh.employee_no,
        edh.department_no,
        IFNULL(
            DATE_ADD((
                SELECT MAX(end_date) 
                FROM employee_department_history edh2 
                WHERE edh2.employee_no = edh.employee_no 
                AND edh2.company_no = edh.company_no
                AND edh2.end_date < edh.end_date
            ), INTERVAL 1 DAY),
            (SELECT start_date FROM employee e WHERE e.employee_no = edh.employee_no)
        ) as department_start_date,
        edh.end_date as department_end_date
    FROM employee_department_history edh
    WHERE edh.company_no = 1
) combined
ORDER BY employee_no, department_start_date;

-- Query to show the fixed logic
SELECT 
    'FIXED_LOGIC' as test_type,
    employee_no,
    department_no,
    department_start_date,
    department_end_date,
    CASE 
        WHEN department_end_date = '9999-12-31' THEN 'CURRENT'
        ELSE 'HISTORICAL'
    END as record_type
FROM (
    -- Fixed current department query
    SELECT 
        e.employee_no,
        e.department_no,
        IFNULL(
            DATE_ADD((
                SELECT MAX(end_date) 
                FROM employee_department_history edh 
                WHERE edh.employee_no = e.employee_no AND edh.company_no = e.company_no
            ), INTERVAL 1 DAY),
            e.start_date
        ) as department_start_date,
        '9999-12-31' as department_end_date
    FROM employee e
    WHERE e.company_no = 1
    -- Only include if no history exists OR current department is different from last historical department
    AND (
        NOT EXISTS (
            SELECT 1 FROM employee_department_history edh 
            WHERE edh.employee_no = e.employee_no AND edh.company_no = e.company_no
        )
        OR NOT EXISTS (
            SELECT 1 FROM employee_department_history edh2 
            WHERE edh2.employee_no = e.employee_no AND edh2.company_no = e.company_no 
            AND edh2.department_no = e.department_no 
            AND edh2.end_date = (
                SELECT MAX(end_date) FROM employee_department_history edh3 
                WHERE edh3.employee_no = e.employee_no AND edh3.company_no = e.company_no
            )
        )
    )
    
    UNION ALL
    
    -- Historical department query (unchanged)
    SELECT 
        edh.employee_no,
        edh.department_no,
        IFNULL(
            DATE_ADD((
                SELECT MAX(end_date) 
                FROM employee_department_history edh2 
                WHERE edh2.employee_no = edh.employee_no 
                AND edh2.company_no = edh.company_no
                AND edh2.end_date < edh.end_date
            ), INTERVAL 1 DAY),
            (SELECT start_date FROM employee e WHERE e.employee_no = edh.employee_no)
        ) as department_start_date,
        edh.end_date as department_end_date
    FROM employee_department_history edh
    WHERE edh.company_no = 1
) combined
ORDER BY employee_no, department_start_date;

-- Analysis query to identify overlaps
SELECT 
    employee_no,
    COUNT(*) as record_count,
    GROUP_CONCAT(
        CONCAT(department_no, ':', department_start_date, '-', department_end_date) 
        ORDER BY department_start_date 
        SEPARATOR ' | '
    ) as department_timeline
FROM (
    -- Use the original logic to show overlaps
    SELECT 
        e.employee_no,
        e.department_no,
        IFNULL(
            DATE_ADD((
                SELECT MAX(end_date) 
                FROM employee_department_history edh 
                WHERE edh.employee_no = e.employee_no AND edh.company_no = e.company_no
            ), INTERVAL 1 DAY),
            e.start_date
        ) as department_start_date,
        '9999-12-31' as department_end_date
    FROM employee e
    WHERE e.company_no = 1
    
    UNION ALL
    
    SELECT 
        edh.employee_no,
        edh.department_no,
        IFNULL(
            DATE_ADD((
                SELECT MAX(end_date) 
                FROM employee_department_history edh2 
                WHERE edh2.employee_no = edh.employee_no 
                AND edh2.company_no = edh.company_no
                AND edh2.end_date < edh.end_date
            ), INTERVAL 1 DAY),
            (SELECT start_date FROM employee e WHERE e.employee_no = edh.employee_no)
        ) as department_start_date,
        edh.end_date as department_end_date
    FROM employee_department_history edh
    WHERE edh.company_no = 1
) combined
GROUP BY employee_no
ORDER BY employee_no;
