package model

import "time"

type IntegAttByEmployee struct {
	Employee    IntegAttEmployeeInfo
	Attendances []IntegAttInfo
}

func NewIntegAttByEmployee(
	employee IntegAttEmployeeInfo,
	attendances []IntegAttInfo,
) IntegAttByEmployee {
	return IntegAttByEmployee{
		Employee:    employee,
		Attendances: attendances,
	}
}

type IntegAttEmployeeInfo struct {
	ID         int
	Code       string
	DayEndTime string
}

func NewIntegAttEmployeeInfo(
	id int,
	code,
	dayEndTime string,
) IntegAttEmployeeInfo {
	return IntegAttEmployeeInfo{
		ID:         id,
		Code:       code,
		DayEndTime: dayEndTime,
	}
}

type IntegAttInfo struct {
	IntegrationSettingID int
	System               string
	EmpID                int
	Date                 time.Time
	StartDateTime        time.Time
	EndDateTime          time.Time
	WorkTime             int
	RestTime             int
	HolidayType          int
	Rests                []IntegRestDateTime
}

func NewIntegAttInfo(
	integrationSettingID int,
	system string,
	empID int,
	date,
	startDateTime,
	endDateTime time.Time,
	workTime,
	restTime,
	holidayType int,
	Rests []IntegRestDateTime,
) IntegAttInfo {
	return IntegAttInfo{
		IntegrationSettingID: integrationSettingID,
		System:               system,
		EmpID:                empID,
		Date:                 date,
		StartDateTime:        startDateTime,
		EndDateTime:          endDateTime,
		WorkTime:             workTime,
		RestTime:             restTime,
		HolidayType:          holidayType,
		Rests:                Rests,
	}
}

type IntegAttDateTime struct {
	StartDateTime time.Time
	EndDateTime   time.Time
}

func NewIntegAttDateTime(
	startDateTime,
	endDatetTime time.Time,
) IntegAttDateTime {
	return IntegAttDateTime{
		StartDateTime: startDateTime,
		EndDateTime:   endDatetTime,
	}
}

type IntegRestDateTime struct {
	StartDateTime time.Time
	EndDateTime   time.Time
}

func NewIntegRestDateTime(
	startDateTime,
	endDatetTime time.Time,
) IntegRestDateTime {
	return IntegRestDateTime{
		StartDateTime: startDateTime,
		EndDateTime:   endDatetTime,
	}
}
