# Department History Overlap Issue Fix

## Problem Description

The `buildEmpDeptHistoryLogQuery` function in `employee_department_history.go` was generating overlapping time intervals for employee department history records. This occurred when the UNION of `empCurDeptSubQuery` and `empDeptHistSubQuery` results created duplicate or overlapping department assignments for the same time periods.

## Root Cause Analysis

### Original Logic Issues

1. **Current Department Query (`empCurDeptSubQuery`)**:
   - Included ALL employees regardless of their department history
   - Set start date as: last department history end_date + 1 day OR employee start_date
   - Set end date as: '9999-12-31'

2. **Historical Department Query (`empDeptHistSubQuery`)**:
   - Included ALL department history records
   - Set start date as: previous department history end_date + 1 day OR employee start_date
   - Set end date as: current history record's end_date

### Overlap Scenarios

#### Scenario 1: Employee with Same Current and Last Historical Department
```
Employee: <PERSON>
- Historical: Dept A (2023-01-01 to 2023-03-31)
- Historical: Dept B (2023-04-01 to 2023-06-30)  
- Current: Dept B (employee.department_no = B)

Original Result:
- Historical Record: Dept B (2023-04-01 to 2023-06-30)
- Current Record: Dept B (2023-07-01 to 9999-12-31) ✓ Correct

But if employee never changed from Dept B:
- Historical Record: Dept B (2023-04-01 to 2023-06-30)
- Current Record: Dept B (2023-07-01 to 9999-12-31) ✗ Duplicate department!
```

#### Scenario 2: Employee with No Department Changes
```
Employee: Jane Smith
- Current: Dept A (employee.department_no = A, no history records)

Original Result:
- Current Record: Dept A (2023-01-01 to 9999-12-31) ✓ Correct

This scenario works correctly.
```

## Solution Implementation

### Modified Current Department Query Logic

Added a WHERE condition to exclude current department records when:
1. The employee has department history AND
2. The current department is the same as the last historical department

```sql
WHERE "edh.employee_no IS NULL OR NOT EXISTS (" +
    "SELECT 1 FROM employee_department_history edh2 " +
    "WHERE edh2.employee_no = e.employee_no AND edh2.company_no = e.company_no " +
    "AND edh2.department_no = e.department_no " +
    "AND edh2.end_date = (SELECT MAX(end_date) FROM employee_department_history edh3 " +
    "WHERE edh3.employee_no = e.employee_no AND edh3.company_no = e.company_no))"
```

### Logic Breakdown

1. **`edh.employee_no IS NULL`**: Include employees with no department history
2. **`NOT EXISTS (...)`**: Exclude employees where current department matches last historical department

The NOT EXISTS condition checks:
- Same employee (`edh2.employee_no = e.employee_no`)
- Same company (`edh2.company_no = e.company_no`)
- Same department (`edh2.department_no = e.department_no`)
- Is the most recent history record (`edh2.end_date = MAX(end_date)`)

## Expected Results After Fix

### Employee 1: Multiple Department Changes
```
- Historical: Dept A (2023-01-01 to 2023-03-31)
- Historical: Dept B (2023-04-01 to 2023-06-30)
- Current: Dept C (2023-07-01 to 9999-12-31)
```
✓ No overlap - current department C is different from last historical department B

### Employee 2: No Department History
```
- Current: Dept A (2023-01-01 to 9999-12-31)
```
✓ No overlap - no historical records exist

### Employee 3: Current Same as Last Historical
```
- Historical: Dept A (2023-01-01 to 2023-03-31)
- Historical: Dept B (2023-04-01 to 2023-06-30)
- (No current record generated - would be duplicate of last historical)
```
✓ No overlap - current department record excluded because it would duplicate historical

## Testing

Use the provided `test_department_history_overlap.sql` script to:
1. Create test data with various scenarios
2. Compare original vs fixed logic results
3. Identify any remaining overlaps

## Files Modified

- `go-core/deneb/infra/dao/employee_department_history.go`
  - `buildEmpDeptHistoryLogQuery()` function
  - `buildRecEmpDeptHist()` function

## Impact Assessment

### Positive Impacts
- Eliminates duplicate department assignments for same time periods
- Ensures clean, non-overlapping department history timeline
- Maintains data integrity for reporting and analytics

### Potential Considerations
- Employees whose current department matches their last historical department will only have historical records (no current record)
- This is actually correct behavior - the historical record already covers their current assignment
- Applications relying on always having a "current" record may need adjustment

## Verification Steps

1. Run the test SQL script to verify fix works correctly
2. Check existing data for any employees affected by this change
3. Verify that reports and analytics still function correctly
4. Monitor for any application errors related to missing "current" department records

## Alternative Approaches Considered

1. **Modify end_date of historical records**: Would require complex logic to update existing data
2. **Use DISTINCT in UNION**: Wouldn't solve the fundamental overlap issue
3. **Post-process to remove duplicates**: Less efficient and doesn't address root cause

The chosen solution addresses the root cause by preventing duplicate records from being generated in the first place.
