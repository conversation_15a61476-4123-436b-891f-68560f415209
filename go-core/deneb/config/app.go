package config

import "net/http"

// Project rout path
const PJROOT = "/go/src/gitlab.com/innopm/deneb/"

// CommitFilePath commit path
const CommitFilePath = PJROOT + "resource/build/commit-id"

// SecretPath is a path for confidential env files
const SecretPath = "resource/secret/"

// ConfigFile is a file that the configs will be read
const ConfigFile = "config.yaml"

// ConfigTestFile is a file for the test configs
const ConfigTestFile = "config-test.yaml"

// MaxLimitPerPage is a maximum limit of fetched data
const MaxLimitPerPage = 500

// MaxLimitPerPageForExcludedUsers is a maximum limit of fetched data for excluded users
const MaxLimitPerPageForExcludedUsers = 2000

// DefaultLimitPerPage is a default for limiting the result fetched per page
const DefaultLimitPerPage = 100

// DefaultPerPage is a default values for perpage
const DefaultPerPage = 20

// DefaultLanguage is a language use when no other language is available
const DefaultLanguage = "en"

// LimitTargetExport is a default for limiting the result in export
const LimitTargetExport = 1000

// MaxLimitTargetExport is maximum limit of export data
const MaxLimitTargetExport = 5000

// MaxPasswdLength is maximum length of password that can be set
const MaxPasswdLength = 16

// IgnoreFields are fields not needed to be modify before validation
var IgnoreFields = []string{}

// CountedHTTPCodes are status codes that are counted when accessing API
var CountedHTTPCodes = []int{
	http.StatusOK,
	http.StatusNoContent,
}

// APIVersion is the version of app
const APIVersion = "v1.85.0"

const ServiceName = "CrowdLog"

// CommitID is set at staring by commit-id file
var CommitID = ""

// DaysInAWeek is days in a week(7 days)
const DaysInAWeek = 7

// MaxDaysInMonth is max days in a month(31 days)
const MaxDaysInAMonth = 31

// MaxDaily is the max of daily date range (days)
const MaxDaily = 100

// DefaultMaxMonthly is the default max of monthly date range (months)
const DefaultMaxMonthly = 12

// MaxMonthly is the max of monthly date range (months)
const MaxMonthly = 60

// MaxInterval365Days is the max holiday date range (days)
const MaxInterval365Days = 365

// BaseDay is the default base_day
const BaseDay = 1

// IntervalDaily is default of daily (in days)
const IntervalDaily = 5

// IntervalMonthly is default of monthly (in months)
const IntervalMonthly = 3

// Interval90Days is default interval of holiday (in days)
const Interval90Days = 90

// ProjectTypeGantt is gantt project type
const ProjectTypeGantt = 1

// ProjectTypeBoth is gantt and process project type
const ProjectTypeBoth = 2

// TsUnapplied is unapplied status for timesheet approval status
const TsUnapplied = 0

// TsPending is pending status for timesheet approval status
const TsPending = 1

// BcryptPwCost is the number of of hashing operation in bcrypt encryption
const BcryptPwCost = 10

// MinDatetime is minimum datatime value
const MinDatetime = "1000-01-01 00:00:00"

// Default company start time
const DefaultCompanyStartTime = "09:00:00"

// Default company end time
const DefaultCompanyEndTime = "18:00:00"

// MaxDate is maximum date value
const MaxDate = "9999-12-31"

// SyncCodeStartEndDelim sync code leading and trailing delimiters
const SyncCodeStartEndDelim = "@@"

// SyncTaskCodeStartEndDelim sync code leading and trailing delimiters
const SyncTaskCodeStartEndDelim = "@@@"

// SyncCodeDelim sync code delimiters
const SyncCodeDelim = "::"

// SyncCodeDelim sync code delimiters
const SyncNameDelim = " > "

// MaxCalendarSyncDays is the max of calendar sync date range (days)
const MaxCalendarSyncDays = 35

// ### Permission Type ###
// PermType is Type of Permission
const (
	ADM = PermType("ADM")
	PJI = PermType("PJI")
	PJP = PermType("PJP")
	PJW = PermType("PJW")
	TST = PermType("TST")

	// timesheet_input project auth
	TSTinProj = PermType("TSTinProj")
)

// PermType is Type of Permission
type PermType string

// ### Permission Access ###
// PermAcc is Access level of Permission
const (
	View = PermAccs(1)
	Edit = PermAccs(2)
)

// PermAcc is Access level of Permission
type PermAccs int

// ### Company Closing Type ###
// ClosingType is company's closing type
const (
	ClsngTpWork       = ClosingType(0)
	ClsngTpSales      = ClosingType(1)
	ClsngTpCost       = ClosingType(2)
	ClsngTpExpense    = ClosingType(3)
	ClsngTpAttendance = ClosingType(4)
)

// ClosingType is company's closing type
type ClosingType int

// ### Approval Status ###
// Approval Status is timesheet's approval status
const (
	AprvlStsUnapplied = ApprovalStatus(0)
	AprvlStsPending   = ApprovalStatus(1)
	AprvlStsApproved  = ApprovalStatus(2)
	AprvlStsRejected  = ApprovalStatus(3)
)

// Approval Status is timesheet's approval status
type ApprovalStatus int

// ### Request Status ###
// Request Status is attendance's approval status
const (
	RqstStsUnapplied = RequestStatus(0)
	RqstStsPending   = RequestStatus(1)
	RqstStsApproved  = RequestStatus(2)
	RqstStsRejected  = RequestStatus(3)
)

// Request Status is attendance's approval status
type RequestStatus int

// ### Ts Holiday Type ###
// Ts Holiday Type is timesheet's holiday type
const (
	TsHldyNone     = TsHolidayType(0)
	TsHldyWholeDay = TsHolidayType(1)
	TsHldyAM       = TsHolidayType(2)
	TsHldyPM       = TsHolidayType(3)
)

// Ts Holiday Type is timesheet's holiday type
type TsHolidayType int

var TsHolidayConv = map[TsHolidayType]string{
	TsHldyNone:     "none",
	TsHldyWholeDay: "day_off",
	TsHldyAM:       "morning_off",
	TsHldyPM:       "afternoon_off",
}

// ### Att Workday Class ###
// Att Workday Class is employee's work form workday class
const (
	AttWrkDayClassWkDay        = AttWorkDayClass(1)
	AttWrkDayClassLglHldy      = AttWorkDayClass(2)
	AttWrkDayClassOutsdLglHldy = AttWorkDayClass(3)
)

// Att Workday Class is employee's work form workday class
type AttWorkDayClass int

const (
	WorktimeUnitManMonth  = WorktimeUnit(0)
	WorktimeUnitManDay    = WorktimeUnit(1)
	WorktimeUnitManHour   = WorktimeUnit(2)
	WorktimeUnitManMinute = WorktimeUnit(3)
)

// WorktimeUnit is worktime unit type in company settings
type WorktimeUnit int

// TsStatus is timesheet status
const (
	TsNotOpen    = TsStatus(0)
	TsToday      = TsStatus(1)
	TsStartStamp = TsStatus(2)
	TsBreak      = TsStatus(3)
	TsEndStamp   = TsStatus(4)
	TsFixed      = TsStatus(5)
	TsTemp       = TsStatus(6)
)

// TsStatus is timesheet status
type TsStatus int

const (
	StampStart = StampType(0)
	StampEnd   = StampType(1)
)

// StampType is stamp for attendance and rest
type StampType int

// ProjectType is project's type
const (
	PrjProcess = ProjectType(0)
	PrjGantt   = ProjectType(1)
	PrjBoth    = ProjectType(2)
)

// ProjectType is project's type
type ProjectType int

// ProjectTaskProcessType is projects' task_process_flag
const (
	ProjTskProcNotUsed = ProjectTaskProcessType(0)
	ProjTskProcList    = ProjectTaskProcessType(1)
	ProjTskProcGroup   = ProjectTaskProcessType(2)
)

type ProjectTaskProcessType int

// FileFormat is a combination of supported file_type and char_code
var FileFormat = map[string][]string{
	"xlsx": {"utf8"},
	"csv":  {"utf8", "sjis", "eucjp", "gbk"},
}

// SQS const
const (
	// Commands
	SQSExportAll                            = "export/all"
	SQSExportWork                           = "export/work"
	SQSIntegrationAttendance                = "integration/attendance"
	SQSNotificationWorktimeInputReminder    = "notif/reminder/worktime_input"
	SQSWorkReportPartitionCreationInvoke    = "reports/work/partition/invoke"
	SQSWorkReportPartitionCreationExecution = "reports/work/partition/exec"
	SQSWorkReportDeletionInvoke             = "reports/work/deletion/invoke"
	SQSWorkReportDeletionExecution          = "reports/work/deletion/exec"
	SQSAttDiffCalcInvocation                = "att/diff/calc/invocation"
	SQSAttDiffCalcExecution                 = "att/diff/calc/execution"
	SQSIntegJobcanRefreshInvoke             = "integ/jobcan/refresh/invoke"
	SQSIntegJobcanRefreshExecution          = "integ/jobcan/refresh/exec"
)

// Job status range values
const (
	JobRunningMin = 0
	JobRunningMax = 19
	JobSuccessMin = 20
	JobSuccessMax = 29
	JobFailedMin  = 80
	JobFailedMax  = 99
)

// SQSMsgGrpID groups messages in sqs
const (
	SQSMsgGrpID      = "atlas"
	DenebSQSMsgGrpID = "twist"
)

// Response Header Const
const (
	ResHDRCacheControl = "no-store"
	ResHDRExpires      = "-1"
	ResHDRPragma       = "no-cache"
)

// Notification types
const (
	Webhook = NotifType("WH")
	Email   = NotifType("EM")
	Slack   = NotifType("SL")
	MsTeams = NotifType("MT")
)

// NotifType groups notification type
type NotifType string

// NotifTypeConv are readable values of notif types
var NotifTypeConv = map[NotifType]string{
	Webhook: "webhook",
	Slack:   "slack",
	MsTeams: "ms_teams",
}

// Type of action in notifcation item
const (
	TSL = NotifAction("TSL")
	SMP = NotifAction("SMP")
)

// NotifAction groups notification actions
type NotifAction string

// Type of fields in form
const (
	FieldOneLineText    = FieldFormType(0)
	FieldMultiLinesText = FieldFormType(1)
	FieldSelectMenu     = FieldFormType(2)
	FieldRadio          = FieldFormType(3)
	FieldCheckbox       = FieldFormType(4)
)

// FieldFormType groups form types
type FieldFormType int

// FieldFormTypeConv are readable values of form types
var FieldFormTypeConv = map[FieldFormType]string{
	FieldOneLineText:    "one_line_text",
	FieldMultiLinesText: "multi_lines_text",
	FieldSelectMenu:     "select_menu",
	FieldRadio:          "radio",
	FieldCheckbox:       "checkbox",
}

// Format in OneLineText form type
const (
	FieldCharacter = FieldFormat(0)
	FieldNumber    = FieldFormat(1)
	FieldDate      = FieldFormat(2)
	FieldEmail     = FieldFormat(3)
)

// FieldFormat groups format
type FieldFormat int

// FieldFormatConv are readable values of format
var FieldFormatConv = map[FieldFormat]string{
	FieldCharacter: "character",
	FieldNumber:    "number",
	FieldDate:      "date",
	FieldEmail:     "email",
}

// Type of list in SelectMenu form type
const (
	FieldOriginalList = FieldListType(0)
	FieldMember       = FieldListType(1)
	FieldCustomer     = FieldListType(2)
	FieldDepartment   = FieldListType(3)
	FieldBusiness     = FieldListType(4)
	FieldProject      = FieldListType(5)
)

// FieldListType groups list type
type FieldListType int

// FieldListTypeConv are readable values of list type
var FieldListTypeConv = map[FieldListType]string{
	FieldOriginalList: "original_list",
	FieldMember:       "member",
	FieldCustomer:     "customer",
	FieldDepartment:   "department",
	FieldBusiness:     "business",
	FieldProject:      "project",
}

// UserAccessedFunc - group functions that user accessed
type UserAccessedFunc string

// UserAccessedFunc list of function
const (
	AllDownload                   = UserAccessedFunc("all_download")
	WorkDownload                  = UserAccessedFunc("work_download")
	Project                       = UserAccessedFunc("project")
	Contract                      = UserAccessedFunc("employ_type_master")
	ProjectMember                 = UserAccessedFunc("project_member")
	MemberDepartment              = UserAccessedFunc("member_department")
	ProjectProcess                = UserAccessedFunc("project_process")
	ProjectProcessGroup           = UserAccessedFunc("project_process_group")
	ProjectTaskProcess            = UserAccessedFunc("project_task_process")
	ProjectTaskProcessGroup       = UserAccessedFunc("project_task_process_group")
	WorkBudget                    = UserAccessedFunc("work_budget")
	WorkBudgetProject             = UserAccessedFunc("work_budget_project")
	WorkBudgetProjectProcess      = UserAccessedFunc("work_budget_project_process")
	GanttChart                    = UserAccessedFunc("ganttchart_project")
	Timesheet                     = UserAccessedFunc("timesheet")
	DailyWorkActual               = UserAccessedFunc("daily_work_actual")
	MonthlyWorkActual             = UserAccessedFunc("monthly_work_actual")
	Title                         = UserAccessedFunc("title_master")
	IntegrationsSettings          = UserAccessedFunc("integrations_settings")
	IntegrationsAttendanceJobs    = UserAccessedFunc("integrations_attendance_jobs")
	Business                      = UserAccessedFunc("business_master")
	Customer                      = UserAccessedFunc("customer_master")
	ProcessGroup                  = UserAccessedFunc("process_group")
	Process                       = UserAccessedFunc("process_master")
	WorktimeInputRemindNotif      = UserAccessedFunc("worktime_input_remind_notif")
	WorkflowTimesheetExcludedUser = UserAccessedFunc("workflow_timesheet_excluded_user")
	WorkflowTimesheetSettings     = UserAccessedFunc("workflow_timesheet_settings")
	AttendanceDifferenceCalc      = UserAccessedFunc("attendance_difference_calc")
	AttendanceDifferenceExport    = UserAccessedFunc("attendance_difference_export")
	WorkReportSummary             = UserAccessedFunc("work_report_summary")
	ProjectManagement             = UserAccessedFunc("project_management")
)

// UserAction - group of user's action
type UserAction string

// User Action list
const (
	Create   = UserAction("create")
	Update   = UserAction("update")
	Delete   = UserAction("delete")
	Export   = UserAction("export")
	Generate = UserAction("generate")
)

// AppAccessType - group application access type
type AppAccessType string

// AppAccessType - list of access
const (
	WEBAccess   = AppAccessType("web")
	APIAccess   = AppAccessType("api")
	BatchAccess = AppAccessType("batch")
)

// ### Task Type ###
// TaskType is task type
const (
	TaskTypeTask      = TaskType(0)
	TaskTypeGroup     = TaskType(1)
	TaskTypeMilestone = TaskType(2)
)

// ClosingType is company's closing type
type TaskType int

// ReportType group of report type
type ReportType string

// ReportType - list of report type
const (
	ReportTypeDaily   = ReportType("daily")
	ReportTypeMonthly = ReportType("monthly")
)

// ReportTypes - list of available report types
var ReportTypes = []ReportType{
	ReportTypeDaily,
	ReportTypeMonthly,
}

// WrkRptDimenMin Work report dimension max length
const WrkRptDimenMin = 1

// WrkRptDimenMax Work report dimension max length
const WrkRptDimenMax = 4

// WrkRptDimen Work report dimension type
type WrkRptDimen string

// List of work report dimension key
const (
	WrkRptDimenProj        = WrkRptDimen("project")
	WrkRptDimenProjBusi    = WrkRptDimen("project_business")
	WrkRptDimenProjBusiLvl = WrkRptDimen("project_business_level_%v")
	WrkRptDimenProjCust    = WrkRptDimen("project_customer")
	WrkRptDimenProjDept    = WrkRptDimen("project_department")
	WrkRptDimenProjDeptLvl = WrkRptDimen("project_department_level_%v")
	WrkRptDimenProjOwner   = WrkRptDimen("project_owner")
	WrkRptDimenUser        = WrkRptDimen("user")
	WrkRptDimenUserCont    = WrkRptDimen("user_contract")
	WrkRptDimenUserCust    = WrkRptDimen("user_customer")
	WrkRptDimenUserDept    = WrkRptDimen("user_department")
	WrkRptDimenUserDeptLvl = WrkRptDimen("user_department_level_%v")
	WrkRptDimenUserTitle   = WrkRptDimen("user_title")
	WrkRptDimenProc1       = WrkRptDimen("process_1")
	WrkRptDimenProc2       = WrkRptDimen("process_2")
	WrkRptDimenProc3       = WrkRptDimen("process_3")
)

// WrkRptDimens List of available work report dimension
var WrkRptDimens = []WrkRptDimen{
	WrkRptDimenProj,
	WrkRptDimenProjBusi,
	WrkRptDimenProjCust,
	WrkRptDimenProjDept,
	WrkRptDimenProjOwner,
	WrkRptDimenUser,
	WrkRptDimenUserCont,
	WrkRptDimenUserCust,
	WrkRptDimenUserDept,
	WrkRptDimenUserTitle,
	WrkRptDimenProc1,
	WrkRptDimenProc2,
	WrkRptDimenProc3,
}

// WrkBgtRptDimens List of available work budget dimension
var WrkBgtRptDimens = []WrkRptDimen{
	WrkRptDimenProj,
	WrkRptDimenProjBusi,
	WrkRptDimenProjCust,
	WrkRptDimenProjDept,
	WrkRptDimenProjOwner,
	WrkRptDimenUser,
	WrkRptDimenUserCont,
	WrkRptDimenUserCust,
	WrkRptDimenUserDept,
	WrkRptDimenUserTitle,
}

// ChartType Work report chart type
type ChartType string

// List of work report chart type key
const (
	ChartTypeTotalBar   = ChartType("total_bar_chart")
	ChartTypeStackedBar = ChartType("stacked_bar_chart")
	ChartTypeBar        = ChartType("bar_chart")
	ChartTypeSumLine    = ChartType("sum_line_chart")
	ChartTypePie        = ChartType("pie_chart")
	ChartTypeCombo      = ChartType("combo_chart")
)

// ChartTypes List of available work report chart type
var ChartTypes = []ChartType{
	ChartTypeTotalBar,
	ChartTypeStackedBar,
	ChartTypeBar,
	ChartTypeSumLine,
	ChartTypePie,
	ChartTypeCombo,
}

// ChartDataLimit Work report chart data limit
type ChartDataLimit int

// ChartDataLimits List of available work report chart data limit
var ChartDataLimits = []ChartDataLimit{
	5, 10, 20, 30, 40, 50, 100,
}

// RepSetKey Work report settings keys
type RepSetKey string

// List of work report settings keys
const (
	RepSetTimeRangeGranKey  = RepSetKey("time_range_granularity")
	RepSetChartTypeKey      = RepSetKey("chart_conditions.type")
	RepSetChartDataLimitKey = RepSetKey("chart_conditions.data_limit")
)

// RepSetWith Work report additional data
type RepSetWith string

// List of work report additional data key
const (
	RepSetWithBudget = RepSetWith("budget")
)

// RepSetWiths List of available work report additional data
var RepSetWiths = []RepSetWith{
	RepSetWithBudget,
}

// CstmWrkRprtLimit is the limit of custom_work_report per company
const CstmWrkRprtLimit = 100

// The max count limit of copy-to-date per request
const MaxCountCopyToDates = 10

// MaxCountCopyDailyStatus is the max of daily date for checking copy status (days)
const MaxCountCopyDailyStatus = 35

const (
	WorktimeUnitManMonthStr  = WorktimeUnitStr("man_month")
	WorktimeUnitManDayStr    = WorktimeUnitStr("man_day")
	WorktimeUnitManHourStr   = WorktimeUnitStr("man_hour")
	WorktimeUnitManMinuteStr = WorktimeUnitStr("man_minute")
)

// WorktimeUnit equivalent string
type WorktimeUnitStr string

// ### Attendance Auth Type ###
// Type of attendance auth
const (
	AttAuthAdmin = AttendanceAuthType(2)
)

// AttendanceAuthType is member's attendance auth
type AttendanceAuthType int

// TimeBdgtUnit Worktime budget unit type
type TimeBdgtUnit string

// List of worktime budget unit type key
const (
	TimeBudgetMonths  = TimeBdgtUnit("time_budget_months")
	TimeBudgetDays    = TimeBdgtUnit("time_budget_days")
	TimeBudgetHours   = TimeBdgtUnit("time_budget_hours")
	TimeBudgetMinutes = TimeBdgtUnit("time_budget_minutes")
)

// Update threshold for session record
const SessionUpdateThresholdSeconds = 30

// ASessionKeyType key of ASession value on sessions table
type ASessionKeyType string

// List of ASessionKey name
const (
	ASessionATimeKey         = ASessionKeyType("_SESSION_ATIME")
	ASessionETimeKey         = ASessionKeyType("_SESSION_ETIME")
	ASessionViolationFlagKey = ASessionKeyType("is_passwd_policy_violation")
)

// List of default value of session expiry duration each authentication type
const (
	ExternalAuthSessionExpiryDurationSec = 7 * 24 * 60 * 60
)

// TimeBdgtUnits List of available time budget units
var TimeBdgtUnits = []TimeBdgtUnit{
	TimeBudgetMonths,
	TimeBudgetDays,
	TimeBudgetHours,
	TimeBudgetMinutes,
}

// Integration Target System Type
type IntegTargetSystem string

// Integration Target System Types
const (
	IntegTargetSystemJobcan    = IntegTargetSystem("jobcan")
	IntegTargetSystemKOT       = IntegTargetSystem("kot")
	IntegTargetSystemKinkakuji = IntegTargetSystem("kinkakuji")
	IntegTargetSystemHrmos     = IntegTargetSystem("hrmos")
)

// Integration Target System token key
type IntegTargetSystemTokenKey string

// Integration Target System tokens
type IntegTargetSystemTokens map[IntegTargetSystemTokenKey]string

// Integration Target System token keys
const (
	IntegTargetSystemToken = IntegTargetSystemTokenKey("token")
)

// Integration Attendance Job Status Type
type IntegAttJobStatus string

// Integration Attendance Job Status Types
const (
	IntegAttJobStatusWaiting = IntegAttJobStatus("waiting")
	IntegAttJobStatusRunning = IntegAttJobStatus("running")
	IntegAttJobStatusSuccess = IntegAttJobStatus("success")
	IntegAttJobStatusFailure = IntegAttJobStatus("failure")
)

type PasswdStrRestrictionLevel int

const (
	PWStrRestrLvlNone                 = PasswdStrRestrictionLevel(0)
	PWStrRestrLvlInclThreeOfCharTypes = PasswdStrRestrictionLevel(1)
	PWStrRestrLvlInclAllCharTypes     = PasswdStrRestrictionLevel(2)
)

type NumberToBeIncludedPasswdStrRestriction int

const (
	InclAtLeast3UpperLowerNumAndSymbol = NumberToBeIncludedPasswdStrRestriction(3)
	InclAllCharTypes                   = NumberToBeIncludedPasswdStrRestriction(4)
)

// CalSyncTargetService Target Service Type
type CalSyncTargetService string

// List of calendar sync service type key
const (
	CalSyncTargetServiceNone    = CalSyncTargetService("none")
	CalSyncTargetServiceGoogle  = CalSyncTargetService("google")
	CalSyncTargetServiceOutlook = CalSyncTargetService("outlook")
)

// Integration  System Title Type
type IntegSystemTitle string

// Integration  System Raw Title
const (
	IntegJobcanTitle    = IntegSystemTitle("ジョブカン")
	IntegKOTTitle       = IntegSystemTitle("KING OF TIME")
	IntegKinkakujiTitle = IntegSystemTitle("勤革時")
	IntegHrmosTitle     = IntegSystemTitle("HRMOS")
)

// Work Budget Report member display type
type WrkBdgtMemDisplay string

// List of work budget report member display type key
const (
	WrkBdgtMemDisplayAll        = WrkBdgtMemDisplay("all")
	WrkBdgtMemDisplayWithBudget = WrkBdgtMemDisplay("with_budget")
	WrkBdgtMemDisplayNoBudget   = WrkBdgtMemDisplay("no_budget")
)

// WrkBdgtMemDisplays List of available work budget report member display type
var WrkBdgtMemDisplays = []WrkBdgtMemDisplay{
	WrkBdgtMemDisplayAll,
	WrkBdgtMemDisplayWithBudget,
	WrkBdgtMemDisplayNoBudget,
}

// WrkBdgtDisplay type
type WrkBdgtDisplay string

// List of work budget display type key
const (
	WrkBdgtDisplayAll        = WrkBdgtDisplay("all")
	WrkBdgtDisplayWithBudget = WrkBdgtDisplay("with_budget")
	WrkBdgtDisplayNoBudget   = WrkBdgtDisplay("no_budget")
)

// WrkBdgtDisplays List of available work budget display type
var WrkBdgtDisplays = []WrkBdgtDisplay{
	WrkBdgtDisplayAll,
	WrkBdgtDisplayWithBudget,
	WrkBdgtDisplayNoBudget,
}

// Integration Invoker Command Type
type IntegrationCommand string

// Integration Invoker Commands
const (
	IntegrationCommandInvoker  = IntegrationCommand("iti")
	IntegrationCommandExecutor = IntegrationCommand("ite")
)

// Integration Execution Mode Type
type IntegrationExecMode string

// Integration Execution Modes
const (
	IntegrationExecModeManu = IntegrationExecMode("manu")
	IntegrationExecModeAuto = IntegrationExecMode("auto")
)

// System Info use for system generated data that has user id (e.g employee_no)
const (
	SystemUserID    = -1
	SystemCompanyID = -1
	SystemLName     = "System"
	SystemFName     = "Generated"
)

// Task status type
type TaskStatus int

// Task status types
const (
	TaskStatusInProgress = TaskStatus(0)
	TaskStatusCompletion = TaskStatus(1)
	TaskStatusClose      = TaskStatus(2)
)

const MaskedString = "********"

// KOT
type KOTHalfdayHolidayTypeName string

const (
	KOTHalfdayHolidayMorningOff   = KOTHalfdayHolidayTypeName("AM半休")
	KOTHalfdayHolidayAfternoonOff = KOTHalfdayHolidayTypeName("PM半休")
)

type KOTAttendanceCode string

const (
	KOTAttendanceStart = KOTAttendanceCode("1")
	KOTAttendanceEnd   = KOTAttendanceCode("2")
	KOTRestStart       = KOTAttendanceCode("3")
	KOTRestEnd         = KOTAttendanceCode("4")
)

// OAuth Provider Type
type OAuthProviderType string

// List of oauth provider type key
const (
	OAuthProviderGoogle    = OAuthProviderType("google")
	OAuthProviderMicrosoft = OAuthProviderType("ms")
	OAuthProviderKOT       = OAuthProviderType("kot")
	OAuthProviderKinkakuji = OAuthProviderType("kinkakuji")
)

// List of Plan
type PlanType string

const (
	PlanTrial   = PlanType("TRL")
	PlanBasic   = PlanType("BSC")
	PlanPremium = PlanType("PRM")
	PlanDemo    = PlanType("DEM")
)

const (
	TimesheetPagePath                = "pages/timesheet"
	OldTimesheetPagePath             = "timesheet"
	SubscriptionConfirmationPagePath = "pages/subscriptions/confirmation"
)

const SubscriptionPath = "/subscriptions"

// WorktimeUnitConv, map of WorktimeUnitStr to WorktimeUnit(int)
var WorktimeUnitConv = map[string]int{
	string(WorktimeUnitManMonthStr):  int(WorktimeUnitManMonth),
	string(WorktimeUnitManDayStr):    int(WorktimeUnitManDay),
	string(WorktimeUnitManHourStr):   int(WorktimeUnitManHour),
	string(WorktimeUnitManMinuteStr): int(WorktimeUnitManMinute),
}

// WorktimeUnitsStr, slice of WorktimeUnitStr
var WorktimeUnitsStr = []WorktimeUnitStr{
	WorktimeUnitManMonthStr,
	WorktimeUnitManDayStr,
	WorktimeUnitManHourStr,
	WorktimeUnitManMinuteStr,
}

// Work Report Aggregation
const (
	WorkReportNestedBusinessLevelMax   = 10
	WorkReportNestedDepartmentLevelMax = 10
)

// Work Report Summary
const (
	WorkReportDataMaxLimit          = 5000
	WorkReportDataTrendRankingLimit = 5
)

// PMTaskCategoryAxis
type PMTaskCategoryAxis string

// List of pmtask category axis key
const (
	PMTaskProjectAxis = PMTaskCategoryAxis("project")
	PMTaskMemberAxis  = PMTaskCategoryAxis("member")
)

// PMTaskType
type PMTaskType string

// List of pmtask type key
const (
	PMTaskTypeTask      = PMTaskType("task")
	PMTaskTypeGroup     = PMTaskType("group")
	PMTaskTypeMilestone = PMTaskType("milestone")
)

// PMTaskStatusGroup type
type PMTaskStatusGroup string

// PMTaskStatusGroup List
const (
	PMTaskStatusGroupTodo       = PMTaskStatusGroup("todo")
	PMTaskStatusGroupInProgress = PMTaskStatusGroup("in-progress")
	PMTaskStatusGroupDone       = PMTaskStatusGroup("done")
)

// PMTaskStatusName type
type PMTaskStatusName string

// PMTaskStatusName List
const (
	PMTaskStatusNameTodo       = PMTaskStatusName("未着手")
	PMTaskStatusNameInProgress = PMTaskStatusName("進行中")
	PMTaskStatusNameDone       = PMTaskStatusName("完了")
)

// PMTaskStatus is a struct for PMTask status
type PMTaskStatus struct {
	Name  PMTaskStatusName
	Group PMTaskStatusGroup
}

// PMTaskDefaultStatuses is a list of default PMTask statuses
var PMTaskDefaultStatuses = []PMTaskStatus{
	{Name: PMTaskStatusNameTodo, Group: PMTaskStatusGroupTodo},
	{Name: PMTaskStatusNameInProgress, Group: PMTaskStatusGroupInProgress},
	{Name: PMTaskStatusNameDone, Group: PMTaskStatusGroupDone},
}

// PMTaskDelayStatus type
type PMTaskDelayStatus string

// PMTaskDelayStatus List
const (
	PMTaskDelayStatusOnTime  = PMTaskDelayStatus("ontime")
	PMTaskDelayStatusOverdue = PMTaskDelayStatus("overdue")
)

// PMTask Max date range (months)
const PMTaskMaxDateRange = 36

// PMTask Group Max Nested Level
const PMTaskGroupMaxNestedLevel = 10

// Work time budget project related
type ProjectBudgetInputType string

const (
	ProjectBudgetInputTypeDirect     = ProjectBudgetInputType("direct")
	ProjectBudgetInputTypeCumulative = ProjectBudgetInputType("cumulative")
)

const (
	ProjectAbbr    string = "proj"
	MemberAbbr     string = "mem"
	DependencyAbbr string = "dep"
)
