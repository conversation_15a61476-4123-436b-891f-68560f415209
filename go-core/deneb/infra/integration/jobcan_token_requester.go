package integration

import (
	"errors"
	"net/http"
	"time"

	"gitlab.com/innopm/deneb/common/ictx"
	"gitlab.com/innopm/deneb/config"
)

type jobcanRes struct {
	AccessToken  string `json:"access_token"`
	TokenType    string `json:"token_type"`
	ExpiresIn    int    `json:"expires_in"`
	Scope        string `json:"scope"`
	RefreshToken string `json:"refresh_token"`
}

type jobcanErrRes struct {
	Err string `json:"error"`
}

type JobcanErrReason string

const grantType = "client_credentials"
const jobcanTokenScope = "adits.read employees.read summaries.read"

const (
	JobcanInvalidRequest       = JobcanErrReason("invalid_request")
	JobcanInvalidClient        = JobcanErrReason("invalid_client")
	JobcanInvalidGrant         = JobcanErrReason("invalid_grant")
	JobcanUnauthorizedClient   = JobcanErrReason("unauthorized_client")
	JobcanUnsupportedGrantType = JobcanErrReason("unsupported_grant_type")
	JobcanInvalidScope         = JobcanErrReason("invalid_scope")
)

var (
	ErrSystem           = errors.New("system error")
	ErrAccTokenPubFaild = errors.New("access token publish is failed")
)

func NewJobcanTokenRequester(
	ictx *ictx.Context, sys config.IntegTargetSystem, is interface{}) *tokenRequester {
	return &tokenRequester{ictx: ictx, integTargetSystem: sys, integSetting: is}
}

func (t *tokenRequester) GetToken() (*TokenInfo, bool, error) {
	if t == nil {
		return nil, false, ErrSystem
	}

	rs := jobcanRes{}
	er := jobcanErrRes{}
	formData := genJobcanTokenFormData()
	ret, err := GetHttpClient().SetUrl(config.Integ.Jobcan.TokenApiUrl).
		SetBasicAuth(t.integSetting.(JobcanSetting).ClientID,
			t.integSetting.(JobcanSetting).Secret).
		SetHeaders(setJobcanHeader()).
		SetFormData(&formData).
		SetResult(&rs).
		SetError(&er).
		Post()

	if err != nil {
		return nil, false, JobcanAPIAccessError(ret, err)
	}

	if ret != nil && (*ret).statusCode == http.StatusOK {
		return getJobcanToken(ret, rs)
	}

	if err, isInvalidCredential := handleJobcanAPIResponseError(ret, &er, err); isInvalidCredential {
		return nil, true, err
	} else {
		return nil, false, err
	}
}

func getJobcanToken(ret *httpResponse, rs jobcanRes) (*TokenInfo, bool, error) {
	if rs.AccessToken == "" {
		return nil, false, ErrAccTokenPubFaild
	}
	ti := TokenInfo{tokens: config.IntegTargetSystemTokens{}}
	ti.tokens[config.IntegTargetSystemToken] = rs.AccessToken
	ti.expiration = calcJobcanExpiration()
	return &ti, false, nil
}

func genJobcanTokenFormData() map[string]string {
	return map[string]string{
		"grant_type": grantType,
		"scope":      jobcanTokenScope,
	}
}

func setJobcanHeader() *map[string]string {
	headers := map[string]string{}
	headers["Content-Type"] = "application/x-www-form-urlencoded"
	return &headers
}

func handleJobcanAPIResponseError(ret *httpResponse, er *jobcanErrRes, err error) (error, bool) {
	if ret == nil || (*ret).statusCode == http.StatusInternalServerError {
		return JobcanAPIAccessError(ret, err), false
	}

	eReason := JobcanErrReason((*er).Err)
	switch eReason {
	case JobcanInvalidClient, JobcanInvalidGrant, JobcanUnauthorizedClient, JobcanInvalidScope, JobcanUnsupportedGrantType:
		return JobcanInvalidAPIClientIDOrSecretError(ret, err), true
	case JobcanInvalidRequest:
	default:
	}
	return JobcanAPIAccessError(ret, err), false
}

func calcJobcanExpiration() time.Time {
	n := time.Now()
	return n.Add(time.Duration(
		time.Duration(config.Integ.Jobcan.TokenDuration) * time.Minute))
}
