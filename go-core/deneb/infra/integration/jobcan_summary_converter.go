package integration

import (
	"errors"
	"fmt"
	"strconv"
	"time"

	"gitlab.com/innopm/deneb/config"
	"gitlab.com/innopm/deneb/domain/context"
	"gitlab.com/innopm/deneb/domain/model"
	"gitlab.com/innopm/deneb/domain/model/tbl"
	"gitlab.com/innopm/deneb/util"
	"gitlab.com/innopm/deneb/util/dateutil"
	"gitlab.com/innopm/deneb/util/strutil"
)

var (
	ErrInvalidJobcanAttendanceData = errors.New("invalid jobcan attendance data was found")
)

var ymdLayout = "2006-01-02"
var emptyTime = "00:00:00"
var noHoliday = "none"
var noon int = 12 * 60 //minutes
var day int = 24 * 60  //minutes

type JobcanConvReq struct {
	Ctx         *context.Context
	EmpIdByCode *map[string]int
	Src         interface{}
	SettingId   int
}

func NewJobcanSummaryConverter(cr *ConvReq) *JobcanConvReq {
	ctx := (*cr)
	return &JobcanConvReq{Ctx: ctx.Ctx, EmpIdByCode: ctx.EmpIdByCode, Src: ctx.Src, SettingId: cr.IntegSettingId}
}

func (jcr *JobcanConvReq) Convert() AttData {
	at := make(AttData, 0)
	empIdByCode := *(*jcr).EmpIdByCode
	sumRes := ((*jcr).Src).(SumRes)
	for _, sr := range sumRes {
		id := empIdByCode[sr.Code]
		ad := *(jcr.convertSummaryByUser(id, sr.Code, sr.DayEndTime, sr.Summaries))
		if len(ad.AttDataByDay) > 0 {
			at = append(at, ad)
		}
	}
	return at
}

func (jcr *JobcanConvReq) convertSummaryByUser(
	id int, code string, dayEndTime time.Time, ss Summaries) *AttDataByID {
	ad := AttDataByID{Id: id, Code: code, AttDataByDay: []AttDataByDay{}}

	for _, summary := range ss.Summaries {
		// Get Date
		d := util.DateToTime(summary.Date)
		adbd := AttDataByDay{Day: d}

		// Convert External attendance
		its, err := jcr.convertToIntegratedAttendance(id, d, summary)
		if err != nil {
			(*(*jcr).Ctx).GetNotification().AppendNotification(
				context.NotifCannotConvertAttendance,
				id, d, "can not convert external attendance")
			continue
		}
		adbd.IntegratedAtt = &its
		ad.AttDataByDay = append(ad.AttDataByDay, adbd)

		// Convert Timesheet
		ts, err := jcr.convertTimeSheet(id, dayEndTime, summary)
		if err != nil {
			continue
		}
		l := len(ad.AttDataByDay) - 1
		ad.AttDataByDay[l].TimeSheet = ts

		// Convert Timesheet Rest
		if summary.Rest > 0 {
			skip, tsr := jcr.convertTimeSheetRest(*ts, summary.Rest)
			if skip {
				(*(*jcr).Ctx).GetNotification().AppendNotification(
					context.NotifCannotConvertAttendance,
					id, (*ts).TimesheetDate, "can not convert timesheet rest")
			}
			ad.AttDataByDay[l].TimeSheetRest = tsr
		}
	}

	return &ad
}

func (jcr *JobcanConvReq) convertTimeSheet(id int,
	dayEndTime time.Time, sum Summary) (*tbl.Timesheet, error) {
	if !strutil.IsDateFormat(&sum.Date) {
		return nil, ErrInvalidJobcanAttendanceData
	}
	d, _ := time.Parse(ymdLayout, sum.Date)

	// Day off
	if jcr.isWholeDayOff(sum) {
		h := int(config.TsHldyWholeDay)
		return genTimesheet(id, d, emptyTime, emptyTime, h), nil
	}

	// Not Day off
	if !(strutil.IsTimeFormat(&sum.WorkStart) && strutil.IsTimeFormat(&sum.WorkEnd)) {
		return nil, ErrInvalidJobcanAttendanceData
	}

	skip, modified := cutByDayEndTime(&sum, dayEndTime)
	if skip {
		(*(*jcr).Ctx).GetNotification().AppendNotification(
			context.NotifCannotConvertAttendance, id, d, "can not convert timesheet")
		return nil, ErrInvalidJobcanAttendanceData
	}
	if modified {
		(*(*jcr).Ctx).GetNotification().AppendNotification(
			context.NotifAttendanceModified, id, d, "timesheet was modified because of exceeding DayEndTime")
	}

	h := int(getHolidayType(sum.VacationType, sum.Work, sum.WorkStart))
	return genTimesheet(id, d, *(convertTime(sum.WorkStart)), *(convertTime(sum.WorkEnd)), h), nil
}

func (jcr *JobcanConvReq) convertTimeSheetRest(
	ts tbl.Timesheet, rest uint) (bool, *tbl.TimesheetRest) {
	skip, sd, s, ed, e := calcRest(ts.TimesheetDate, *ts.StartTime, *ts.EndTime, rest)
	if skip {
		return skip, nil
	}
	return skip, &tbl.TimesheetRest{
		StartDate: &sd,
		StartTime: convertTime(s),
		EndDate:   &ed,
		EndTime:   convertTime(e),
	}
}

// Calculate rest start, end time
//
//	return:
//	- rest start day
//	- rest start time (00:00)
//	- rest end day
//	- rest end time (00:00)
func calcRest(sd time.Time, start, end string, rest uint) (bool, time.Time, string, time.Time, string) {
	ed := sd
	sm := timeToMinutes(start)
	em := timeToMinutes(end)
	rm := int(rest)

	if (em - sm) <= rm {
		return true, time.Time{}, "", time.Time{}, ""
	}

	st, et := calcRestStartEnd(sm, em, rm)

	s := fmt.Sprintf("%02d:%02d", st/60, st%60)
	e := fmt.Sprintf("%02d:%02d", et/60, et%60)
	if day <= et {
		sd, s, ed, e = reCalcRest(sd, s, e)
	}

	return false, sd, s, ed, e
}

// Calculate rest start, end time
//
//	return:
//	- start rest time (hour)
//	- end rest time (hour)
func calcRestStartEnd(start, end, rest int) (int, int) {
	mid := start + (end-start)/2
	fmid := (mid / 60) * 60 // mid fitted to hour

	// if fitted mid < start, then rest start = work start
	if fmid < start {
		return start, start + rest
	}

	// if fitted mid - 60 >= start and fitted mid - 60  + rest < end
	restStart := fmid - 60
	if (start <= restStart) && ((restStart + rest) < end) {
		return restStart, restStart + rest
	}

	// if fitted mid >= start and fitted mid + rest < end
	restStart = fmid
	if (start <= restStart) && ((restStart + rest) < end) {
		return fmid, fmid + rest
	}

	return start, start + rest
}

func reCalcRest(sDay time.Time, start, end string) (time.Time, string, time.Time, string) {
	var sD, eD time.Time
	var sT, eT string

	startm := timeToMinutes(start)
	if startm < day {
		sD = sDay
		sT = start
	} else if startm == day {
		sD = sDay.AddDate(0, 0, 1)
		sT = fmt.Sprintf("%02d:%02d", 0, 0)
	} else {
		sD = sDay.AddDate(0, 0, 1)
		sT = fmt.Sprintf("%02d:%02d", (startm-day)/60, startm%60)
	}

	endm := timeToMinutes(end)
	if endm == day {
		eT = fmt.Sprintf("%02d:%02d", 0, 0)
	} else {
		eT = fmt.Sprintf("%02d:%02d", (endm-day)/60, endm%60)
	}
	eD = sDay.AddDate(0, 0, 1)

	return sD, sT, eD, eT
}

// 00:00 -> 00:00:00
func convertTime(time string) *string {
	t := time + ":00"
	return &t
}

func getHolidayType(vacationType string, work uint, workStart string) config.TsHolidayType {
	if vacationType == noHoliday {
		if work > 0 {
			return config.TsHldyNone
		}
	} else {
		return classifyHoliday(workStart)
	}
	return config.TsHldyNone
}

func classifyHoliday(workStart string) config.TsHolidayType {
	if strutil.IsEmpty(&workStart) {
		return config.TsHldyWholeDay
	} else {
		if timeToMinutes(workStart) >= noon {
			return config.TsHldyAM
		} else {
			return config.TsHldyPM
		}
	}
}

// hh:mm(string) -> minutes
func timeToMinutes(hm string) int {
	h, _ := strconv.Atoi(hm[:2])
	m, _ := strconv.Atoi(hm[3:5])
	return (h * 60) + m
}

func genTimesheet(id int, date time.Time, start, end string, ht int) *tbl.Timesheet {
	return &tbl.Timesheet{
		EmployeeNo:    id,
		TimesheetDate: date,
		StartTime:     &start,
		EndTime:       &end,
		Status:        int(config.TsFixed),
		HolidayType:   &ht,
		CreateTime:    time.Now(),
		UpdateTime:    time.Now(),
	}
}

// modify WorkStart/End if WorkTime is out of range from StartDayEndTime to EndDayEndTime
// return:
// - invalid: unable to modify
// - modified: was modified
func cutByDayEndTime(summary *Summary, dayEndTime time.Time) (invalid, modified bool) {
	sdet := (dayEndTime.Hour() * 60) + dayEndTime.Minute()
	edet := sdet + 24*60
	st := timeToMinutes((*summary).WorkStart)
	et := timeToMinutes((*summary).WorkEnd)

	mStart := fmt.Sprintf("%02d:%02d", sdet/60, sdet%60)
	mEnd := fmt.Sprintf("%02d:%02d", edet/60, edet%60)

	// invalid if WorkEnd <= StartDayEndTime OR WorkStart >= EndDayEndTime
	if (et <= sdet) || (edet <= st) {
		invalid = true
		return
	}
	if st < sdet {
		(*summary).WorkStart = mStart
		modified = true
	}
	if et > edet {
		(*summary).WorkEnd = mEnd
		modified = true
	}

	return
}

func (jcr *JobcanConvReq) convertToIntegratedAttendance(empID int, date time.Time,
	sum Summary) (integAtt tbl.IntegratedAttendance, err error) {
	integAtt.CompanyNo = (*jcr.Ctx).GetCompID()
	integAtt.EmployeeNo = empID
	integAtt.TimesheetDate = date

	integAtt.TargetSystem = string(config.IntegTargetSystemJobcan)
	if sum.Work == 0 || jcr.isWholeDayOff(sum) {
		integAtt.StartTime = "00:00:00"
		integAtt.EndTime = "00:00:00"
	} else {
		if !(strutil.IsTimeFormat(&sum.WorkStart) && strutil.IsTimeFormat(&sum.WorkEnd)) {
			err = ErrInvalidJobcanAttendanceData
			return
		}
		integAtt.StartTime = sum.WorkStart + ":00"
		integAtt.EndTime = sum.WorkEnd + ":00"
	}
	integAtt.RestTime = int(sum.Rest)
	if sum.VacationType != "none" {
		integAtt.HolidayUse = true
	}
	integAtt.CreateUser = (*jcr.Ctx).GetUserID()
	integAtt.UpdateUser = (*jcr.Ctx).GetUserID()
	return
}

func (jcr *JobcanConvReq) ToIntegAttsByEmployees() (integAtts []model.IntegAttByEmployee) {
	empIdByCode := *(*jcr).EmpIdByCode
	summaries := ((*jcr).Src).(SumRes)
	for _, summary := range summaries {
		empID := empIdByCode[summary.Code]
		attendances := jcr.toIntegAttsByEmployee(empID, summary.Summaries)
		if len(attendances) > 0 {
			employeeInfo := model.NewIntegAttEmployeeInfo(
				empID,
				summary.Code,
				dateutil.ToTimeFormat(summary.DayEndTime),
			)
			integAtt := model.NewIntegAttByEmployee(employeeInfo, attendances)
			integAtts = append(integAtts, integAtt)
		}
	}
	return
}

func (jcr *JobcanConvReq) toIntegAttsByEmployee(empID int, summaries Summaries) (atts []model.IntegAttInfo) {
	for _, summary := range summaries.Summaries {
		att, err := jcr.toIntegAttInfo(empID, summary)
		if err != nil {
			continue
		}
		atts = append(atts, att)
	}
	return
}

func (jcr *JobcanConvReq) toIntegAttInfo(empID int,
	summary Summary) (att model.IntegAttInfo, err error) {
	// invalid date format
	if !strutil.IsDateFormat(&summary.Date) {
		return att, ErrInvalidJobcanAttendanceData
	}
	date := util.DateToTime(summary.Date)

	// day off(check day off first, start and end is null)
	if jcr.isWholeDayOff(summary) {
		return model.NewIntegAttInfo(
			jcr.SettingId,
			string(config.IntegTargetSystemJobcan),
			empID,
			date,
			dateutil.HourClockTotalDurToTime(date, emptyTime),
			dateutil.HourClockTotalDurToTime(date, emptyTime),
			int(summary.Work),
			int(summary.Rest),
			int(config.TsHldyWholeDay),
			[]model.IntegRestDateTime{},
		), nil
	}

	// no attendance(null) or invalid work start
	if !(strutil.IsTimeFormat(&summary.WorkStart) && strutil.IsTimeFormat(&summary.WorkEnd)) {
		return att, ErrInvalidJobcanAttendanceData
	}

	return model.NewIntegAttInfo(
		jcr.SettingId,
		string(config.IntegTargetSystemJobcan),
		empID,
		date,
		dateutil.HourClockTotalDurToTime(date, summary.WorkStart+":00"),
		dateutil.HourClockTotalDurToTime(date, summary.WorkEnd+":00"),
		int(summary.Work),
		int(summary.Rest),
		int(getHolidayType(summary.VacationType, summary.Work, summary.WorkStart)),
		jcr.getRests(summary.Breaks),
	), nil
}

func (jcr *JobcanConvReq) isWholeDayOff(summary Summary) bool {
	return getHolidayType(summary.VacationType,
		summary.Work, summary.WorkStart) == config.TsHldyWholeDay
}

func (jcr *JobcanConvReq) getRests(breaks []AditsBreak) []model.IntegRestDateTime {
	rests := make([]model.IntegRestDateTime, 0, len(breaks))
	for _, b := range breaks {
		rests = append(rests, model.NewIntegRestDateTime(b.BreakStart, b.BreakEnd))
	}
	return rests
}
