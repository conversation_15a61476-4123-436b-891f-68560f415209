package integration

import (
	"encoding/json"
	"fmt"
	"sort"
	"strings"
	"time"

	"gitlab.com/innopm/deneb/common/ierror"
	"gitlab.com/innopm/deneb/common/ilog"
	"gitlab.com/innopm/deneb/common/imsg"
	"gitlab.com/innopm/deneb/config"
	"gitlab.com/innopm/deneb/domain/context"
	"gitlab.com/innopm/deneb/util"
)

type kotReqester struct {
	actx         *context.Context
	integSetting interface{}
	usersInfo    UsersInfo
	days         []time.Time
	client       KOTClient
	targetSystem config.IntegTargetSystem
}

func NewKOTRequester(c *context.Context, is interface{},
	usersInfo UsersInfo, days []time.Time) AttDataRequester {
	var logger *ilog.InpmLogger
	if *c != nil {
		ctx := (*c).GetIContext()
		if ctx != nil {
			logger = (*ctx).GetLogger()
		}
	}
	return &kotReqester{
		actx:         c,
		integSetting: is,
		usersInfo:    usersInfo,
		days:         days,
		client:       &kotClient{logger: logger, targetSystem: config.IntegTargetSystemKOT},
		targetSystem: config.IntegTargetSystemKOT,
	}
}

func (r *kotReqester) GetAttData() (interface{}, error) {
	ctx := (*r).actx
	ictx := (*ctx).GetIContext()
	logSystemName := strings.ToUpper(string(r.targetSystem))
	logger := (*ictx).GetLogger()

	logger.Info(ilog.CdIVIntegAttAPI, fmt.Sprintf("%s-integSetting start", logSystemName))
	setting, ok := r.integSetting.(KOTSetting)
	if !ok {
		return nil, ierror.NewConsistencyErr(imsg.CannotGetAccessToken)
	}
	loyout := "2006-01-02"
	end := r.days[len(r.days)-1].Format(loyout)
	logger.Info(ilog.CdIVIntegAttAPI, fmt.Sprintf("%s-integSetting end", logSystemName))

	/*
		Employees Related
	*/
	logger.Info(ilog.CdIVIntegAttAPI, fmt.Sprintf("%s-GetEmployees start", logSystemName))
	_, employees, err := r.client.GetEmployees(setting.AccessToken, end)
	if err != nil {
		if !IsIgnorableError(err) {
			(*r.actx).GetNotification().AppendOnlyTypeNotification(context.NotifAPIAccessError)
		}
		return r.handleError(err, logger)
	}

	r.checkEmployees(employees)
	// Suppress log output, but leave code for future bug investigation
	// logger.Info(ilog.CdIVIntegAttAPI, fmt.Sprintf("[%s_RESEARCH_LOG:GetEmployees_API_Response]  status:%v, body:%+v \n", logSystemName, empResStatus, employees))

	employeesCodeMap := map[string]UserInfo{}
	for _, u := range r.usersInfo {
		employeesCodeMap[u.Code] = u
	}
	filteredKeyEmpMap := r.genFilteredEmployeesKeyMap(employees, employeesCodeMap)
	logger.Info(ilog.CdIVIntegAttAPI, fmt.Sprintf("%s-GetEmployees end. Number of employees: %d", logSystemName, len(employees)))
	/*
		Workings(attedance) Related
	*/
	logger.Info(ilog.CdIVIntegAttAPI, fmt.Sprintf("%s-GetDailyWorkings start", logSystemName))
	logger.Info(ilog.CdIVIntegAttAPI, "[%s_RESEARCH_LOG]: days: %v ", logSystemName, r.days)
	_, workings, err := chunkCallAPIByDates(r.days, func(dates []time.Time) dailyWorkingsParams {
		start := dates[0].Format(loyout)
		end := dates[len(dates)-1].Format(loyout)
		return dailyWorkingsParams{
			token: setting.AccessToken,
			start: start,
			end:   end,
		}
	}, r.client.GetDailyWorkings)
	if err != nil {
		(*r.actx).GetNotification().AppendOnlyTypeNotification(context.NotifAPIAccessError)
		return r.handleError(err, logger)
	}
	// Suppress log output, but leave code for future bug investigation
	// logger.Info(ilog.CdIVIntegAttAPI, fmt.Sprintf("[%s_RESEARCH_LOG:GetDailyWorkings_API_Response]  status:%v, body:%+v \n", logSystemName, workResStatus, workings))

	filteredWorkings := r.filterWorkings(workings, filteredKeyEmpMap)
	logger.Info(ilog.CdIVIntegAttAPI, fmt.Sprintf("%s-GetDailyWorkings end. Number of workings: %d", logSystemName, len(workings)))
	/*
		Timerecords(stamps) Related
	*/
	logger.Info(ilog.CdIVIntegAttAPI, fmt.Sprintf("%s-GetTimerecords start", logSystemName))
	_, timerecords, err := chunkCallAPIByDates(r.days, func(dates []time.Time) timerecordsParams {
		start := dates[0].Format(loyout)
		end := dates[len(dates)-1].Format(loyout)
		return timerecordsParams{
			token: setting.AccessToken,
			start: start,
			end:   end,
		}
	}, r.client.GetTimerecords)
	if err != nil {
		(*r.actx).GetNotification().AppendOnlyTypeNotification(context.NotifAPIAccessError)
		return r.handleError(err, logger)
	}
	// Suppress log output, but leave code for future bug investigation
	// logger.Info(ilog.CdIVIntegAttAPI, fmt.Sprintf("[%s_RESEARCH_LOG:GetTimerecords_API_Response]  status:%v, body:%+v \n", logSystemName, recordResSTatus, timerecords))

	filteredTimerecord := r.filterTimerecord(timerecords, filteredKeyEmpMap)
	logger.Info(ilog.CdIVIntegAttAPI, fmt.Sprintf("%s-GetTimerecords end. Number of timerecords: %d", logSystemName, len(timerecords)))
	// build return value
	logger.Info(ilog.CdIVIntegAttAPI, fmt.Sprintf("%s-mergeDailyWorkingsAndTimerecords start", logSystemName))
	dailyWorkingsWithTimerecords := r.mergeDailyWorkingsAndTimerecords(filteredWorkings, filteredTimerecord)
	ret := []KOTData{}
	empSlice := []KOTEmployeeWithDayEndTime{}
	for _, emp := range filteredKeyEmpMap {
		empSlice = append(empSlice, emp)
	}
	sort.Slice(empSlice, func(i, j int) bool {
		return empSlice[i].Code < empSlice[j].KOTEmployee.Code
	})
	for _, emp := range empSlice {
		attendances := []KOTDailyWorkingWithTimerecords{}
		for _, attendance := range dailyWorkingsWithTimerecords {
			if attendance.EmployeeKey == emp.Key && r.isValidTimerecords(attendance.TimeRecords) {
				attendances = append(attendances, attendance)
			}
		}
		kotData := KOTData{
			EmployeeID:   emp.Key,
			EmployeeCode: emp.Code,
			DayEndTime:   emp.DayEndTime,
			Attendances:  attendances,
		}
		ret = append(ret, kotData)
	}
	logger.Info(ilog.CdIVIntegAttAPI, fmt.Sprintf("%s-mergeDailyWorkingsAndTimerecords end. Number of dailyWorkingsWithTimerecords: %d", logSystemName, len(dailyWorkingsWithTimerecords)))

	return ret, nil
}

var ignorableErrMsgs = []string{"company is invalid", "認証に失敗しました"}

func IsIgnorableError(err error) bool {
	if err == nil {
		return true
	}
	_, ok := err.(*KOTErrorResponse)
	if ok {
		msg := err.(*KOTErrorResponse).APIError[0].Message
		for _, igMsg := range ignorableErrMsgs {
			if msg == igMsg {
				return true
			}
		}
	}
	return false
}

func (r *kotReqester) handleError(err error, logger *ilog.InpmLogger) (interface{}, error) {
	if kotErr, ok := err.(*KOTErrorResponse); ok {
		return nil, kotErr.WithSystemError(ErrAPIAccess)
	}
	if kotErr, ok := err.(*KOTError); ok {
		return nil, kotErr.WithSystemError(ErrAPIAccess)
	}
	if logger != nil {
		logger.Error(ilog.CdIntegError, fmt.Sprintf("[%s_RESEARCH_LOG]: %v", strings.ToUpper(string(r.targetSystem)), err))
	}
	return nil, ErrAPIAccess
}

func (r *kotReqester) genFilteredEmployeesKeyMap(employees KOTEmployeesResponse, employeesCodeMap map[string]UserInfo) map[string]KOTEmployeeWithDayEndTime {
	ret := map[string]KOTEmployeeWithDayEndTime{}
	for _, emp := range employees {
		userInfo, ok := employeesCodeMap[emp.Code]
		if !ok {
			continue
		}
		ret[emp.Key] = KOTEmployeeWithDayEndTime{
			KOTEmployee: emp,
			DayEndTime:  userInfo.DayEndTime,
		}
	}
	return ret
}

func (r *kotReqester) filterWorkings(workRes KOTDailyWorkingsResponse, filteredEmpsMap map[string]KOTEmployeeWithDayEndTime) []KOTDailyWorking {
	ret := []KOTDailyWorking{}
	for _, w := range workRes {
		for _, dw := range w.DailyWorkings {
			if dw.IsError {
				continue
			}
			if _, ok := filteredEmpsMap[dw.EmployeeKey]; !ok {
				continue
			}
			ret = append(ret, dw)
		}
	}
	return ret
}

func (r *kotReqester) filterTimerecord(timerecordRes KOTTimerecordResponse, filteredEmpsMap map[string]KOTEmployeeWithDayEndTime) []KOTTimerecordDailyWorking {
	ret := []KOTTimerecordDailyWorking{}
	for _, t := range timerecordRes {
		for _, dw := range t.DailyWorkings {
			if _, ok := filteredEmpsMap[dw.EmployeeKey]; !ok {
				continue
			}
			ret = append(ret, dw)
		}
	}
	return ret
}

func (r *kotReqester) isValidTimerecords(timerecords []KOTTimeRecord) bool {
	trLen := len(timerecords)
	if trLen == 0 {
		return true
	}
	if trLen == 1 {
		return false
	}
	dest := make([]KOTTimeRecord, len(timerecords))
	copy(dest, timerecords)
	sort.Slice(dest, func(i, j int) bool {
		return dest[i].Time.Before(dest[j].Time)
	})
	lastIndex := len(dest) - 1
	lastItem := dest[lastIndex]
	if lastItem.Code != string(config.KOTAttendanceEnd) {
		for i, item := range dest {
			if dest[i].Code == string(config.KOTAttendanceEnd) && lastItem.Time.Equal(dest[i].Time) {
				dest[i] = lastItem
				dest[lastIndex] = item
			}
		}
	}
	// if first timerecord is Start
	first := dest[0]
	if first.Code != string(config.KOTAttendanceStart) {
		return false
	}
	// if last timerecord is End
	last := dest[len(dest)-1]
	if last.Code != string(config.KOTAttendanceEnd) {
		return false
	}

	// is timerecords are paired
	startCnt, endCnt, restStartCnt, restEndCnt := r.getTimerecordCounts(dest)
	if startCnt != endCnt {
		return false
	}
	if restStartCnt != restEndCnt {
		return false
	}
	// if there is a break between start and end
	for i, tr := range dest {
		if tr.Code == string(config.KOTRestStart) {
			prev := dest[0:i]
			startCnt, endCnt, restStartCnt, restEndCnt := r.getTimerecordCounts(prev)
			if startCnt <= endCnt || restStartCnt != restEndCnt {
				return false
			}
		}
	}
	return true
}

func (r *kotReqester) getTimerecordCounts(timerecords []KOTTimeRecord) (startCnt int, endCnt int, restStartCnt int, restEndCnt int) {
	for _, tr := range timerecords {
		if tr.Code == string(config.KOTAttendanceStart) {
			startCnt++
		}
		if tr.Code == string(config.KOTAttendanceEnd) {
			endCnt++
		}
		if tr.Code == string(config.KOTRestStart) {
			restStartCnt++
		}
		if tr.Code == string(config.KOTRestEnd) {
			restEndCnt++
		}
	}
	return
}

func (r *kotReqester) mergeDailyWorkingsAndTimerecords(dailyWorkings []KOTDailyWorking, timerecords []KOTTimerecordDailyWorking) []KOTDailyWorkingWithTimerecords {
	ret := []KOTDailyWorkingWithTimerecords{}
	for _, dw := range dailyWorkings {
		workWithTimerecords := KOTDailyWorkingWithTimerecords{
			KOTDailyWorking: dw,
		}
		for _, t := range timerecords {
			if t.Date == dw.Date && t.EmployeeKey == dw.EmployeeKey {
				workWithTimerecords.TimeRecords = t.TimeRecord
			}
		}
		ret = append(ret, workWithTimerecords)
	}
	return ret
}

// Check no exist CrowdLog employees
func (r *kotReqester) checkEmployees(employees []KOTEmployee) {
	if len(employees) == 0 {
		return
	}
	contains := func(code string) bool {
		for _, e := range employees {
			if e.Code == code {
				return true
			}
		}
		return false
	}
	defer func() {
		(*r.actx).GetNotification().FlashLog(context.NotifCannotGetEmployee)
	}()
	for _, cw := range r.usersInfo {
		if !contains(cw.Code) {
			(*r.actx).GetNotification().AppendNotification(
				context.NotifCannotGetEmployee, cw.Id,
				time.Time{}, "code-"+cw.Code)
		}
	}
}

type KOTClient interface {
	GetEmployees(token string, date string) (int, []KOTEmployee, error)
	GetDailyWorkings(p dailyWorkingsParams) (int, []KOTDailyWorkings, error)
	GetTimerecords(p timerecordsParams) (int, []KOTTimerecord, error)
}

const errorStatusBoundary = 399

type kotClient struct {
	logger       *ilog.InpmLogger
	targetSystem config.IntegTargetSystem
}

func (k *kotClient) GetEmployees(token string, date string) (int, []KOTEmployee, error) {
	ret := KOTEmployeesResponse{}
	params := map[string]string{
		"date": date,
		// whether leaved employee is included.
		// Not current employee but also leaved employee need to be synced.
		"includeResigner": "true",
	}
	hc := GetHttpClient()
	res, err := hc.SetUrl(config.Integ.KOT.EmployeeApiUrl).
		SetAuthToken(token).
		SetQueryParams(&params).
		SetResult(&ret).
		Get()
	// Suppress log output, but leave code for future bug investigation
	// if k.logger != nil {
	// 	logSystemName := strings.ToUpper(string(k.targetSystem))
	// 	k.logger.Info(ilog.CdIVIntegAttAPI, fmt.Sprintf("[%s_RESEARCH_LOG:GetEmployees_API_Params]  params:%v, url:%v \n",
	// 		logSystemName, params, config.Integ.KOT.DailyWorkingsApiUrl))
	// }
	if err != nil {
		return 0, KOTEmployeesResponse{}, err
	}
	if res.statusCode > errorStatusBoundary {
		return handleError[KOTEmployee](res)
	}
	return res.statusCode, ret, err
}

type dailyWorkingsParams struct {
	token string
	start string
	end   string
}

func (k *kotClient) GetDailyWorkings(p dailyWorkingsParams) (int, []KOTDailyWorkings, error) {
	ret := KOTDailyWorkingsResponse{}
	params := map[string]string{"start": p.start, "end": p.end}
	hc := GetHttpClient()
	res, err := hc.SetUrl(config.Integ.KOT.DailyWorkingsApiUrl).
		SetAuthToken(p.token).
		SetQueryParams(&params).
		SetResult(&ret).
		Get()
	// Suppress log output, but leave code for future bug investigation
	// if k.logger != nil {
	// 	logSystemName := strings.ToUpper(string(k.targetSystem))
	// 	k.logger.Info(ilog.CdIVIntegAttAPI, fmt.Sprintf("[%s_RESEARCH_LOG:GetDailyWorkings_API_Params]  params:%v, url:%v \n",
	// 		logSystemName, params, config.Integ.KOT.DailyWorkingsApiUrl))
	// }
	if err != nil {
		return 0, KOTDailyWorkingsResponse{}, err
	}
	if res.statusCode > errorStatusBoundary {
		return handleError[KOTDailyWorkings](res)
	}
	return res.statusCode, ret, err
}

type timerecordsParams struct {
	token string
	start string
	end   string
}

func (k *kotClient) GetTimerecords(p timerecordsParams) (int, []KOTTimerecord, error) {
	ret := KOTTimerecordResponse{}
	params := map[string]string{"start": p.start, "end": p.end}
	hc := GetHttpClient()
	res, err := hc.SetUrl(config.Integ.KOT.DailyWorkingsTimerecordApiUrl).
		SetAuthToken(p.token).
		SetQueryParams(&params).
		SetResult(&ret).
		Get()
	// Suppress log output, but leave code for future bug investigation
	// if k.logger != nil {
	// 	logSystemName := strings.ToUpper(string(k.targetSystem))
	// 	k.logger.Info(ilog.CdIVIntegAttAPI, fmt.Sprintf("[%s_RESEARCH_LOG:GetTimerecords_API_Params]  params:%v, url:%v \n",
	// 		logSystemName, params, config.Integ.KOT.DailyWorkingsApiUrl))
	// }
	if err != nil {
		return 0, KOTTimerecordResponse{}, err
	}
	if res.statusCode > errorStatusBoundary {
		return handleError[KOTTimerecord](res)
	}
	return res.statusCode, ret, err
}

func chunkCallAPIByDates[T any, P any](dates []time.Time, genParams func(dates []time.Time) P,
	callAPI func(param P) (int, []T, error)) (status int, data []T, err error) {
	var res []T
	chunkDates := util.ChunkSlice(dates, config.Integ.KOT.MaxDatesDuration)
	for _, chunkDate := range chunkDates {
		params := genParams(chunkDate)
		status, res, err = callAPI(params)
		if err != nil {
			return status, nil, err
		}
		data = append(data, res...)
	}
	return
}

type KOTData struct {
	// employeeKey
	EmployeeID   string
	EmployeeCode string
	DayEndTime   time.Time
	Attendances  []KOTDailyWorkingWithTimerecords
}

type KOTDailyWorkingWithTimerecords struct {
	KOTDailyWorking
	TimeRecords []KOTTimeRecord
}

/*
***************************

	KOT Error

****************************
*/

func handleError[T any](res *httpResponse) (int, []T, error) {
	var singleError *KOTError
	var multiError *KOTErrorResponse

	body := res.responseJson
	err := json.Unmarshal([]byte(body), &singleError)
	if err != nil {
		return res.statusCode, []T{}, err
	}
	if singleError.Code != 0 || singleError.Message != "" {
		return res.statusCode, []T{}, singleError
	}
	err = json.Unmarshal([]byte(body), &multiError)
	if err != nil {
		return res.statusCode, []T{}, err
	}
	return res.statusCode, []T{}, multiError
}

type KOTErrorResponse struct {
	APIError []KOTError `json:"errors"`
	sysError error
}

func (e *KOTErrorResponse) WithSystemError(sysError error) error {
	return &KOTErrorResponse{
		APIError: e.APIError,
		sysError: sysError,
	}
}

func (e *KOTErrorResponse) Error() string {
	b, err := json.Marshal(&e)
	if err != nil {
		panic(err)
	}
	return string(b)
}

func (e *KOTErrorResponse) GetSystemError() error {
	return e.sysError
}

type KOTError struct {
	Message  string `json:"message"`
	Code     int    `json:"code"`
	sysError error
}

func (e *KOTError) Error() string {
	b, err := json.Marshal(&e)
	if err != nil {
		panic(err)
	}
	return string(b)
}

func (e *KOTError) WithSystemError(sysError error) error {
	return &KOTError{
		Message:  e.Message,
		Code:     e.Code,
		sysError: sysError,
	}
}

func (e *KOTError) GetSystemError() error {
	return e.sysError
}

/*
***************************

	KOT Employee

****************************
*/

type KOTEmployeeWithDayEndTime struct {
	KOTEmployee
	DayEndTime time.Time
}

type KOTEmployee struct {
	Code string `json:"code"`
	Key  string `json:"key"`
}

type KOTEmployeesResponse []KOTEmployee

/*
***************************

	KOT Workings

****************************
*/

type KOTDailyWorkingsResponse []KOTDailyWorkings

type KOTDailyWorkings struct {
	Date          string            `json:"date"`
	DailyWorkings []KOTDailyWorking `json:"dailyWorkings"`
}

type KOTDailyWorking struct {
	Date             string              `json:"date"`
	EmployeeKey      string              `json:"employeeKey"`
	IsError          bool                `json:"isError"`
	BreakTime        int                 `json:"breakTime"`
	TotalWork        int                 `json:"totalWork"`
	HolidaysObtained KOTHolidaysObtained `json:"holidaysObtained"`
}

type KOTHolidaysObtained struct {
	FulltimeHoliday KOTFulltimeHoliday  `json:"fulltimeHoliday"`
	HalfdayHolidays []KOTHalfdayHoliday `json:"halfdayHolidays"`
	HourHolidays    []KOTHourHoliday    `json:"hourHolidays"`
}

type KOTFulltimeHoliday struct {
	Code *string `json:"code"`
	Name *string `json:"name"`
}

type KOTHalfdayHoliday struct {
	Code     string `json:"code"`
	Name     string `json:"name"`
	TypeName string `json:"typeName"`
}

type KOTHourHoliday struct {
	Start   string `json:"start"`
	End     string `json:"end"`
	Minutes string `json:"minutes"`
	Code    string `json:"code"`
	Name    string `json:"name"`
}

/*
***************************

	KOT Timerecord

****************************
*/

type KOTTimerecordResponse []KOTTimerecord

type KOTTimerecord struct {
	Date          string                      `json:"date"`
	DailyWorkings []KOTTimerecordDailyWorking `json:"dailyWorkings"`
}

type KOTTimerecordDailyWorking struct {
	Date        string          `json:"date"`
	EmployeeKey string          `json:"employeeKey"`
	TimeRecord  []KOTTimeRecord `json:"timeRecord"`
}

type KOTTimeRecord struct {
	Time time.Time `json:"time"`
	// 1:start, 2:end, 3:break_start, 4:break_end
	Code string `json:"code"`
}
