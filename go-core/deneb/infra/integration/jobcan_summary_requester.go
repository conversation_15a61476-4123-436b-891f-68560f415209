package integration

import (
	"time"

	"gitlab.com/innopm/deneb/common/ilog"
	"gitlab.com/innopm/deneb/config"
	"gitlab.com/innopm/deneb/domain/context"
)

func NewJobcanSummaryRequester(c *context.Context, is interface{},
	usersInfo UsersInfo, days []time.Time) *AttDataReqesterReq {
	return &AttDataReqesterReq{
		actx:              c,
		integTargetSystem: (*c).GetTargetSystem(),
		integSetting:      is,
		usersInfo:         usersInfo,
		days:              days,
	}
}

func (r *AttDataReqesterReq) GetAttData() (interface{}, error) {
	ctx := (*r).actx

	ictx := (*ctx).GetIContext()
	logger := (*ictx).GetLogger()
	logger.Info(ilog.CdIVIntegAttFunc, "GetAttData start")

	logger.Info(ilog.CdIVIntegAttAPI, "GetToken start")
	tokens, err := NewAuthToken(
		(*ctx).GetIContext(),
		r.integTargetSystem,
		r.integSetting.(JobcanSetting)).GetToken()
	if err != nil {
		getErr(r.actx, err)
		_, ok := err.(*JobcanErrInvalidAPIClientIDOrSecret)
		if ok {
			return nil, err
		}
		return nil, err
	}
	token := tokens[config.IntegTargetSystemToken]
	logger.Info(ilog.CdIVIntegAttAPI, "GetToken end")

	logger.Info(ilog.CdIVIntegAttAPI, "GetEmployeeIDs start")
	// convert from employee's codes to ids
	ecis, err := GetEmployeeIDs(ctx, tokens[config.IntegTargetSystemToken], r.usersInfo)
	if err != nil {
		return nil, err
	}
	logger.Info(ilog.CdIVIntegAttAPI, "GetEmployeeIDs end")

	logger.Info(ilog.CdIVIntegAttAPI, "GetSummary start")
	sum, err := NewJobcanSummaryReq().GetSummary(
		*ctx, SummaryConds{Token: token, EmployeesInfo: *ecis, Dates: r.days})
	logger.Info(ilog.CdIVIntegAttAPI, "GetSummary end")

	logger.Info(ilog.CdIVIntegAttFunc, "GetAttData end")
	return sum, err
}

func getErr(itctx *context.Context, err error) {
	switch err {
	case ErrAccTokenPubFaild:
		(*itctx).GetNotification().AppendOnlyTypeNotification(context.NotifCannotGetAccessToken)
	case ErrUnknownIntegTargetSys:
		(*itctx).GetNotification().AppendOnlyTypeNotification(context.NotifUnknownTargetSystem)
	default:
	}
	if _, ok := err.(*JobcanErrAPIAccessError); ok {
		(*itctx).GetNotification().AppendOnlyTypeNotification(context.NotifAPIAccessError)
	}
	if _, ok := err.(*JobcanErrInvalidAccessToken); ok {
		(*itctx).GetNotification().AppendOnlyTypeNotification(context.NotifInvalidAccessToken)
	}
}
