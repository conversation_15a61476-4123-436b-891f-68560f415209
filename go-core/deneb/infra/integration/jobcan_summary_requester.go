package integration

import (
	"time"

	"gitlab.com/innopm/deneb/common/ilog"
	"gitlab.com/innopm/deneb/config"
	"gitlab.com/innopm/deneb/domain/context"
	"gitlab.com/innopm/deneb/util"
)

func NewJobcanSummaryRequester(c *context.Context, is interface{},
	usersInfo UsersInfo, days []time.Time) *AttDataReqesterReq {
	return &AttDataReqesterReq{
		actx:              c,
		integTargetSystem: (*c).GetTargetSystem(),
		integSetting:      is,
		usersInfo:         usersInfo,
		days:              days,
	}
}

func (r *AttDataReqesterReq) GetAttData() (interface{}, error) {
	ctx := (*r).actx

	ictx := (*ctx).GetIContext()
	logger := (*ictx).GetLogger()
	logger.Info(ilog.CdIVIntegAttFunc, "GetAttData start")

	logger.Info(ilog.CdIVIntegAttAPI, "GetToken start")
	tokens, err := NewAuthToken(
		(*ctx).GetIContext(),
		r.integTargetSystem,
		r.integSetting.(JobcanSetting)).GetToken()
	if err != nil {
		getErr(r.actx, err)
		_, ok := err.(*JobcanErrInvalidAPIClientIDOrSecret)
		if ok {
			return nil, err
		}
		return nil, err
	}
	token := tokens[config.IntegTargetSystemToken]
	logger.Info(ilog.CdIVIntegAttAPI, "GetToken end")

	logger.Info(ilog.CdIVIntegAttAPI, "GetEmployeeIDs start")
	// convert from employee's codes to ids
	ecis, err := GetEmployeeIDs(ctx, tokens[config.IntegTargetSystemToken], r.usersInfo)
	if err != nil {
		return nil, err
	}
	logger.Info(ilog.CdIVIntegAttAPI, "GetEmployeeIDs end")

	logger.Info(ilog.CdIVIntegAttAPI, "GetSummary start")
	sum, err := NewJobcanSummaryReq().GetSummary(
		*ctx, SummaryConds{Token: token, EmployeesInfo: *ecis, Dates: r.days})
	logger.Info(ilog.CdIVIntegAttAPI, "GetSummary end")

	if err != nil {
		return sum, err
	}

	logger.Info(ilog.CdIVIntegAttAPI, "GetAdits start")
	adits, err := NewJobcanAditReq().GetAdits(
		*ctx, AditConds{Token: token, EmployeesInfo: *ecis, Dates: r.days})
	logger.Info(ilog.CdIVIntegAttAPI, "GetAdits end")

	if err != nil {
		return sum, err
	}

	// merge adits to summary
	for k, s := range sum {
		for _, a := range adits {
			if s.ID == a.ID {
				for i, summary := range s.Summaries.Summaries {
					adBreaks, ok := a.Breaks[util.DateToTime(summary.Date)]
					if ok {
						adBreaks = r.removeBreaksAfterDayEndTime(adBreaks, summary)
						sum[k].Summaries.Summaries[i].Breaks = adBreaks

						// recalculate default rest time by subtracting total of the stamped break time
						defaultRestMins := int(summary.Rest) - r.calBreakTotalMins(adBreaks)
						if defaultRestMins < 0 {
							defaultRestMins = 0
						}
						sum[k].Summaries.Summaries[i].Rest = uint(defaultRestMins)
					}
				}
				break
			}
		}
	}

	logger.Info(ilog.CdIVIntegAttFunc, "GetAttData end")
	return sum, err
}

func (r *AttDataReqesterReq) removeBreaksAfterDayEndTime(adBreaks []AditBreak, summary Summary) (res []AditBreak) {

	// it is ok that the adit end time is after work end time when working is not finished at the day
	if summary.CurrentStatus != nil && *summary.CurrentStatus == "working" && summary.AditUnmatch {
		res = adBreaks
		return
	}

	// remove abnormal breaks whose end time is after work end time at a finished working day
	for len(adBreaks) > 0 {
		if adBreaks[len(adBreaks)-1].BreakEnd.After(util.DateToTime(summary.Date + " " + summary.WorkEnd)) {
			adBreaks = adBreaks[:len(adBreaks)-1]
		} else {
			break
		}
	}

	res = adBreaks
	return
}

func (r *AttDataReqesterReq) calBreakTotalMins(breaks []AditBreak) int {
	breakMins := 0
	for _, b := range breaks {
		breakMins += int(b.BreakEnd.Sub(b.BreakStart).Minutes())
	}
	return breakMins
}

func getErr(itctx *context.Context, err error) {
	switch err {
	case ErrAccTokenPubFaild:
		(*itctx).GetNotification().AppendOnlyTypeNotification(context.NotifCannotGetAccessToken)
	case ErrUnknownIntegTargetSys:
		(*itctx).GetNotification().AppendOnlyTypeNotification(context.NotifUnknownTargetSystem)
	default:
	}
	if _, ok := err.(*JobcanErrAPIAccessError); ok {
		(*itctx).GetNotification().AppendOnlyTypeNotification(context.NotifAPIAccessError)
	}
	if _, ok := err.(*JobcanErrInvalidAccessToken); ok {
		(*itctx).GetNotification().AppendOnlyTypeNotification(context.NotifInvalidAccessToken)
	}
}
