package integration

import (
	"net/http"
	"strconv"
	"time"

	"gitlab.com/innopm/deneb/config"
	"gitlab.com/innopm/deneb/domain/context"
	"gitlab.com/innopm/deneb/util"
)

type SummaryConds struct {
	Token         string
	EmployeesInfo []EmployeeInfo
	Dates         []time.Time
}

type SumRes []SummaryRes

type SummaryRes struct {
	ID         int
	Code       string
	DayEndTime time.Time
	Summaries  Summaries
}

type Summaries struct {
	Summaries []Summary `json:"daily_summaries"`
}

type Summary struct {
	EmployeeId        uint    `json:"employee_id"`
	Date              string  `json:"date"`
	Work              uint    `json:"work"`
	Rest              uint    `json:"rest"`
	Night             uint    `json:"night"`
	Over              uint    `json:"over"`
	NightOver         uint    `json:"nightover"`
	OriginalWorkStart string  `json:"original_work_start"`
	OriginalWorkEnd   string  `json:"original_work_end"`
	WorkStart         string  `json:"work_start"`
	WorkEnd           string  `json:"work_end"`
	ShiftOver         uint    `json:"shift_over"`
	Training          uint    `json:"training"`
	Late              uint    `json:"late"`
	LeaveEarly        uint    `json:"leave_early"`
	Absence           uint    `json:"absence"`
	Salary            int     `json:"salary"`
	VacationTime      uint    `json:"vacation_time"`
	HolidayType       int     `json:"holiday_type"`
	VacationType      string  `json:"vacation_type"`
	SuspendType       bool    `json:"suspend_type"`
	CurrentStatus     *string `json:"current_status"`
	AditUnmatch       bool    `json:"adit_unmatch"`
	Breaks            []AditBreak
}

type SummaryError struct {
	StatusCode int
	Status     string
	Detail     string
}

type JobcanSummary interface {
	GetSummary(context.Context, SummaryConds) SumRes
}

type JobcanSummaryReq struct {
}

func NewJobcanSummaryReq() JobcanSummaryReq {
	return JobcanSummaryReq{}
}

var dayForm string = "2006-01-02"

func (j JobcanSummaryReq) GetSummary(c context.Context, cond SummaryConds) (SumRes, error) {
	sumByID := map[int64][]Summary{}
	sr := make([]SummaryRes, 0)

	// for support over 31 days
	chunkDates := util.ChunkSlice(cond.Dates, config.Integ.Jobcan.MaxDatesDuration)
	for _, chunkDate := range chunkDates {
		lastID := uint(0)
		for {
			sm, _, _, err := j.getSummary(c, cond.Token,
				config.Integ.Jobcan.SummaryCount, chunkDate, lastID)
			if err != nil {
				return sr, err
			}
			if j.isEnd(sm) {
				break
			}
			lastID = j.getLastID(sm)
			j.collectSummaryByID(cond, sm, &sumByID)
		}
	}

	for _, ic := range cond.EmployeesInfo {
		id, _ := strconv.ParseInt(ic.ID, 10, 64)
		v, ok := sumByID[id]
		if ok {
			sr = append(sr, SummaryRes{ID: int(id), Code: ic.Code,
				DayEndTime: ic.DayEndTime, Summaries: Summaries{Summaries: v}})
		}
	}

	return sr, nil
}

func (j JobcanSummaryReq) collectSummaryByID(cond SummaryConds, sm Summaries, sumByID *map[int64][]Summary) {
	for _, ic := range cond.EmployeesInfo {
		id, _ := strconv.ParseInt(ic.ID, 10, 64)
		j.setSummary(id, sm.Summaries, sumByID)
	}
}

func (j JobcanSummaryReq) setSummary(id int64, sums []Summary, sumByID *map[int64][]Summary) {
	for _, sum := range sums {
		if sum.EmployeeId != uint(id) {
			continue
		}
		sums, ok := (*sumByID)[id]
		if !ok {
			sums = make([]Summary, 0)
		}
		sums = append(sums, sum)
		(*sumByID)[id] = sums
	}
}

func (j JobcanSummaryReq) isEnd(sm Summaries) bool {
	return len(sm.Summaries) == 0
}

func (j JobcanSummaryReq) getLastID(sm Summaries) uint {
	return sm.Summaries[len(sm.Summaries)-1].EmployeeId
}

func (j JobcanSummaryReq) getSummary(c context.Context, token string, num int,
	days []time.Time, lastID uint) (Summaries, SummaryError, *httpResponse, error) {
	params := map[string]string{
		"last_id": strconv.FormatUint(uint64(lastID), 10),
		"count":   strconv.FormatInt(int64(num), 10),
	}
	sm := Summaries{Summaries: []Summary{}}
	se := SummaryError{}
	hc := GetHttpClient()
	res, err := hc.SetUrl(config.Integ.Jobcan.SummaryApiUrl).
		SetAuthToken(token).
		SetQueryKeyVals("date", j.getParams(days)).
		SetQueryParams(&params).
		SetResult(&sm).
		SetError(&se).
		Get()
	if err == nil && res != nil {
		err = j.evalStatus(c, res, err)
	} else {
		c.GetNotification().AppendOnlyTypeNotification(context.NotifAPIAccessError)
		err = JobcanAPIAccessError(res, err)
	}

	return sm, se, res, err
}

func (j JobcanSummaryReq) evalStatus(c context.Context, res *httpResponse, err error) error {
	switch res.statusCode {
	case http.StatusOK:
		return nil
	case http.StatusUnauthorized:
		c.GetNotification().AppendOnlyTypeNotification(context.NotifInvalidAccessToken)
		return JobcanInvalidAccessTokenError(res, err)
	case http.StatusTooManyRequests:
		c.GetNotification().AppendOnlyTypeNotification(context.NotifAPICallLimitExceeded)
		return JobcanAPICallLimitExceededError(res, err)
	case http.StatusBadRequest,
		http.StatusForbidden,
		http.StatusNotFound,
		http.StatusUnsupportedMediaType:
	default:
	}
	c.GetNotification().AppendOnlyTypeNotification(context.NotifAPIAccessError)
	return JobcanAPIAccessError(res, err)
}

func (j JobcanSummaryReq) getParams(days []time.Time) *[]string {
	strDays := make([]string, 0)
	for _, day := range days {
		strDays = append(strDays, day.Format(dayForm))
	}
	return &strDays
}
