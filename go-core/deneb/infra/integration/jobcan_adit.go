package integration

import (
	"net/http"
	"strconv"
	"time"

	"gitlab.com/innopm/deneb/config"
	"gitlab.com/innopm/deneb/domain/context"
	"gitlab.com/innopm/deneb/util"
)

type AditConds struct {
	Token         string
	EmployeesInfo []EmployeeInfo
	Dates         []time.Time
}

type AditsRes []AditRes

type AditRes struct {
	ID     int
	Breaks map[time.Time][]AditsBreak
}

type Adits struct {
	Adits []Adit `json:"adits"`
}

type Adit struct {
	EmployeeID       uint      `json:"employee_id"`
	GroupID          uint      `json:"group_id"`
	Date             string    `json:"date"`
	Time             string    `json:"time"`
	Location         string    `json:"location"`
	WorkType         string    `json:"work_type"`
	Approved         bool      `json:"approved"`
	Canceled         bool      `json:"canceled"`
	AditType         string    `json:"adit_type"`
	AditMethod       string    `json:"adit_method"`
	Address          string    `json:"address"`
	Latitude         *float64  `json:"latitude"`
	Longitude        *float64  `json:"longitude"`
	Notice           string    `json:"notice"`
	LastRecordUpdate time.Time `json:"last_record_update"`
}

type AditsBreak struct {
	BreakStart time.Time
	BreakEnd   time.Time
}

type AditError struct {
	StatusCode int
	Status     string
	Detail     string
}

type JobcanAdit interface {
	GetAdits(context.Context, AditConds) AditsRes
}

type JobcanAditReq struct {
}

func NewJobcanAditReq() JobcanAditReq {
	return JobcanAditReq{}
}

func (j JobcanAditReq) GetAdits(c context.Context, cond AditConds) (AditsRes, error) {
	aditByDateID := make(map[time.Time]map[int64][]Adit, 0)
	ar := make([]AditRes, 0)

	for _, date := range cond.Dates {
		lastID := uint(0)
		for {
			ads, _, _, err := j.getAdit(c, cond.Token,
				config.Integ.Jobcan.AditCount, []time.Time{date}, lastID)
			if err != nil {
				return ar, err
			}
			if j.isEnd(ads) {
				break
			}
			lastID = j.getLastID(ads)
			j.collectAditByDateID(date, cond, ads, &aditByDateID)
		}
	}

	for date, employeeAdits := range aditByDateID {
		for employeeID, adits := range employeeAdits {
			breaks := j.aditsToBreaks(adits)

			// Check if the employeeID already exists in the result
			found := false
			for i, res := range ar {
				if res.ID == int(employeeID) {
					// Merge breaks into the existing AditRes
					for existingDate, existingBreaks := range res.Breaks {
						if existingDate.Equal(date) {
							res.Breaks[existingDate] = append(existingBreaks, breaks...)
							found = true
							break
						}
					}
					if !found {
						res.Breaks[date] = breaks
					}
					ar[i] = res
					found = true
					break
				}
			}

			// If not found, create a new AditRes
			if !found {
				ar = append(ar, AditRes{
					ID:     int(employeeID),
					Breaks: map[time.Time][]AditsBreak{date: breaks},
				})
			}
		}
	}

	return ar, nil
}

func (j JobcanAditReq) aditsToBreaks(adits []Adit) []AditsBreak {
	breakAdits := make([]Adit, 0, len(adits))
	breaks := make([]AditsBreak, 0, len(adits))
	if len(adits)%2 == 1 {
		breakAdits = adits[1:]
	} else if len(adits) > 2 {
		breakAdits = adits[1 : len(adits)-1]
	}

	var bs, be time.Time
	for k, adit := range breakAdits {
		if k%2 == 0 {
			bs = util.DateToTime(adit.Date + " " + adit.Time)
		} else {
			be = util.DateToTime(adit.Date + " " + adit.Time)
			breaks = append(breaks, AditsBreak{BreakStart: bs, BreakEnd: be})
		}
	}
	return breaks
}

func (j JobcanAditReq) collectAditByDateID(date time.Time, cond AditConds, ads Adits, adByDateID *(map[time.Time]map[int64][]Adit)) {
	for _, ic := range cond.EmployeesInfo {
		id, _ := strconv.ParseInt(ic.ID, 10, 64)
		j.setAdit(date, id, ads.Adits, adByDateID)
	}
}

func (j JobcanAditReq) setAdit(date time.Time, id int64, adits []Adit, adByDateID *(map[time.Time]map[int64][]Adit)) {
	if _, ok := (*adByDateID)[date]; !ok {
		(*adByDateID)[date] = make(map[int64][]Adit, 0)
	}

	for _, adit := range adits {
		if adit.EmployeeID != uint(id) {
			continue
		}

		ads, ok := (*adByDateID)[date][id]
		if !ok {
			ads = make([]Adit, 0)
		}
		ads = append(ads, adit)
		(*adByDateID)[date][id] = ads
	}
}

func (j JobcanAditReq) isEnd(ads Adits) bool {
	return len(ads.Adits) == 0
}

func (j JobcanAditReq) getLastID(ads Adits) uint {
	return ads.Adits[len(ads.Adits)-1].EmployeeID
}

func (j JobcanAditReq) getAdit(c context.Context, token string, num int,
	days []time.Time, lastID uint) (Adits, AditError, *httpResponse, error) {
	params := map[string]string{
		"last_id":  strconv.FormatUint(uint64(lastID), 10),
		"count":    strconv.FormatInt(int64(num), 10),
		"canceled": "false",
	}
	ad := Adits{Adits: []Adit{}}
	ae := AditError{}
	hc := GetHttpClient()
	res, err := hc.SetUrl(config.Integ.Jobcan.AditApiUrl).
		SetAuthToken(token).
		SetQueryKeyVals("date", j.getParams(days)).
		SetQueryParams(&params).
		SetResult(&ad).
		SetError(&ae).
		Get()
	if err == nil && res != nil {
		err = j.evalStatus(c, res, err)
	} else {
		c.GetNotification().AppendOnlyTypeNotification(context.NotifAPIAccessError)
		err = JobcanAPIAccessError(res, err)
	}

	return ad, ae, res, err
}

func (j JobcanAditReq) evalStatus(c context.Context, res *httpResponse, err error) error {
	switch res.statusCode {
	case http.StatusOK:
		return nil
	case http.StatusUnauthorized:
		c.GetNotification().AppendOnlyTypeNotification(context.NotifInvalidAccessToken)
		return JobcanInvalidAccessTokenError(res, err)
	case http.StatusTooManyRequests:
		c.GetNotification().AppendOnlyTypeNotification(context.NotifAPICallLimitExceeded)
		return JobcanAPICallLimitExceededError(res, err)
	case http.StatusBadRequest,
		http.StatusForbidden,
		http.StatusNotFound,
		http.StatusUnsupportedMediaType:
	default:
	}
	c.GetNotification().AppendOnlyTypeNotification(context.NotifAPIAccessError)
	return JobcanAPIAccessError(res, err)
}

func (j JobcanAditReq) getParams(days []time.Time) *[]string {
	strDays := make([]string, 0)
	for _, day := range days {
		strDays = append(strDays, day.Format("2006-01-02"))
	}
	return &strDays
}
