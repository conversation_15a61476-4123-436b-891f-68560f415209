package integration

import (
	"encoding/json"
	"fmt"
	"net/http"
	"testing"
	"time"

	"github.com/jarcoal/httpmock"
	"github.com/stretchr/testify/assert"
	"gitlab.com/innopm/deneb/config"
	"gitlab.com/innopm/deneb/domain/context"
	test "gitlab.com/innopm/deneb/test/integration"
	"gitlab.com/innopm/deneb/util"
)

func Test_NewKOTSummaryRequester_GetAttData(t *testing.T) {
	tester := kotRequesterTester{}
	t.Cleanup(tester.setupKOTHTTPMock())

	type want struct {
		res []KOTData
		err error
	}

	type tcase struct {
		name         string
		setupMocks   func(t *testing.T)
		getRequester func() *kotReqester
		getWant      func() want
	}

	// init reused data
	ictx := test.GenGinIctx(1, 1)
	actx := context.NewCtx(&ictx, config.IntegTargetSystemJobcan)
	dayEndTime := util.DateToTime("05:00:00")
	integSetting := KOTSetting{AccessToken: "ACCESS_TOKEN"}
	clinet := kotClient{}
	cases := []tcase{
		// CASE1 ==============================================================================================
		{
			name: `CASE1_[OK]:KOT data associated with employee is returned. 
					- data has attendance start_end, rest start_end.`,
			setupMocks: func(t *testing.T) {
				/*
					Employee API
				*/
				tester.setupKOTEmployeeRequesterMock(t, KOTEmployeesResponse{
					{Code: "0001", Key: "1"},
					{Code: "0002", Key: "2"},
				}, nil)
				/*
					DailyWorkings API
				*/
				tester.setupKOTDailyWorkingsRequesterMock(t, []KOTDailyWorkings{
					{Date: "2023-04-03", DailyWorkings: []KOTDailyWorking{
						tester.genKOTDailyWorkingTest(testKOTDailyWorkingProps{
							Date:        "2023-04-03",
							EmployeeKey: "1",
							BreakTime:   60,
							TotalWork:   480,
						}),
						tester.genKOTDailyWorkingTest(testKOTDailyWorkingProps{
							Date:        "2023-04-03",
							EmployeeKey: "2",
							BreakTime:   120,
							TotalWork:   720,
						}),
					}},
					{Date: "2023-04-04", DailyWorkings: []KOTDailyWorking{
						tester.genKOTDailyWorkingTest(testKOTDailyWorkingProps{
							Date:        "2023-04-04",
							EmployeeKey: "1",
							BreakTime:   90,
							TotalWork:   540,
						}),
						tester.genKOTDailyWorkingTest(testKOTDailyWorkingProps{
							Date:        "2023-04-04",
							EmployeeKey: "2",
							BreakTime:   60,
							TotalWork:   480,
						}),
					}},
				}, nil)
				/*
					Timerecords API
				*/
				tester.setupKOTTimerecordsRequesterMock(t, []KOTTimerecord{
					{Date: "2023-04-03", DailyWorkings: []KOTTimerecordDailyWorking{
						tester.genTimerecordDailyWorking("2023-04-03", "1",
							tester.withAttendanceStartRecordTest(tester.genRecordTime("2023-04-03", "09:00:00")),
							tester.withAttendanceEndRecordTest(tester.genRecordTime("2023-04-03", "18:00:00")),
							tester.withRestStartRecordTest(tester.genRecordTime("2023-04-03", "12:00:00")),
							tester.withRestEndRecordTest(tester.genRecordTime("2023-04-03", "13:00:00")),
						),
						tester.genTimerecordDailyWorking("2023-04-03", "2",
							tester.withAttendanceStartRecordTest(tester.genRecordTime("2023-04-03", "08:00:00")),
							tester.withAttendanceEndRecordTest(tester.genRecordTime("2023-04-03", "22:00:00")),
							tester.withRestStartRecordTest(tester.genRecordTime("2023-04-03", "12:00:00")),
							tester.withRestEndRecordTest(tester.genRecordTime("2023-04-03", "13:00:00")),
							tester.withRestStartRecordTest(tester.genRecordTime("2023-04-03", "17:00:00")),
							tester.withRestEndRecordTest(tester.genRecordTime("2023-04-03", "18:00:00")),
						),
					}},
					{Date: "2023-04-04", DailyWorkings: []KOTTimerecordDailyWorking{
						tester.genTimerecordDailyWorking("2023-04-04", "1",
							tester.withAttendanceStartRecordTest(tester.genRecordTime("2023-04-04", "09:00:00")),
							tester.withAttendanceEndRecordTest(tester.genRecordTime("2023-04-04", "19:30:00")),
							tester.withRestStartRecordTest(tester.genRecordTime("2023-04-04", "12:00:00")),
							tester.withRestEndRecordTest(tester.genRecordTime("2023-04-04", "13:00:00")),
							tester.withRestStartRecordTest(tester.genRecordTime("2023-04-04", "15:00:00")),
							tester.withRestEndRecordTest(tester.genRecordTime("2023-04-04", "15:30:00")),
						),
						tester.genTimerecordDailyWorking("2023-04-04", "2",
							tester.withAttendanceStartRecordTest(tester.genRecordTime("2023-04-04", "09:00:00")),
							tester.withAttendanceEndRecordTest(tester.genRecordTime("2023-04-04", "18:00:00")),
							tester.withRestStartRecordTest(tester.genRecordTime("2023-04-04", "12:00:00")),
							tester.withRestEndRecordTest(tester.genRecordTime("2023-04-04", "13:00:00")),
						),
					}},
				}, nil)
			},
			getRequester: func() *kotReqester {
				return &kotReqester{
					actx:         &actx,
					integSetting: integSetting,
					client:       &clinet,
					days:         tester.genTimeRange("2023-04-01", "2023-04-29"),
					usersInfo: UsersInfo{
						{Id: 1, Code: "0001", DayEndTime: dayEndTime},
						{Id: 2, Code: "0002", DayEndTime: dayEndTime},
					},
					targetSystem: config.IntegTargetSystemKOT,
				}
			},
			getWant: func() want {
				return want{
					res: []KOTData{
						{
							EmployeeID:   "1",
							EmployeeCode: "0001",
							DayEndTime:   dayEndTime,
							Attendances: []KOTDailyWorkingWithTimerecords{
								{
									KOTDailyWorking: tester.genKOTDailyWorkingTest(testKOTDailyWorkingProps{
										Date:        "2023-04-03",
										EmployeeKey: "1",
										BreakTime:   60,
										TotalWork:   480,
									}),
									TimeRecords: func() []KOTTimeRecord {
										return tester.genTimerecordDailyWorking("2023-04-03", "1",
											tester.withAttendanceStartRecordTest(tester.genRecordTime("2023-04-03", "09:00:00")),
											tester.withAttendanceEndRecordTest(tester.genRecordTime("2023-04-03", "18:00:00")),
											tester.withRestStartRecordTest(tester.genRecordTime("2023-04-03", "12:00:00")),
											tester.withRestEndRecordTest(tester.genRecordTime("2023-04-03", "13:00:00")),
										).TimeRecord
									}(),
								},
								{
									KOTDailyWorking: tester.genKOTDailyWorkingTest(testKOTDailyWorkingProps{
										Date:        "2023-04-04",
										EmployeeKey: "1",
										BreakTime:   90,
										TotalWork:   540,
									}),
									TimeRecords: func() []KOTTimeRecord {
										return tester.genTimerecordDailyWorking("2023-04-04", "1",
											tester.withAttendanceStartRecordTest(tester.genRecordTime("2023-04-04", "09:00:00")),
											tester.withAttendanceEndRecordTest(tester.genRecordTime("2023-04-04", "19:30:00")),
											tester.withRestStartRecordTest(tester.genRecordTime("2023-04-04", "12:00:00")),
											tester.withRestEndRecordTest(tester.genRecordTime("2023-04-04", "13:00:00")),
											tester.withRestStartRecordTest(tester.genRecordTime("2023-04-04", "15:00:00")),
											tester.withRestEndRecordTest(tester.genRecordTime("2023-04-04", "15:30:00")),
										).TimeRecord
									}(),
								},
							},
						},
						{
							EmployeeID:   "2",
							EmployeeCode: "0002",
							DayEndTime:   dayEndTime,
							Attendances: []KOTDailyWorkingWithTimerecords{
								{
									KOTDailyWorking: tester.genKOTDailyWorkingTest(testKOTDailyWorkingProps{
										Date:        "2023-04-03",
										EmployeeKey: "2",
										BreakTime:   120,
										TotalWork:   720,
									}),
									TimeRecords: func() []KOTTimeRecord {
										return tester.genTimerecordDailyWorking("2023-04-03", "2",
											tester.withAttendanceStartRecordTest(tester.genRecordTime("2023-04-03", "08:00:00")),
											tester.withAttendanceEndRecordTest(tester.genRecordTime("2023-04-03", "22:00:00")),
											tester.withRestStartRecordTest(tester.genRecordTime("2023-04-03", "12:00:00")),
											tester.withRestEndRecordTest(tester.genRecordTime("2023-04-03", "13:00:00")),
											tester.withRestStartRecordTest(tester.genRecordTime("2023-04-03", "17:00:00")),
											tester.withRestEndRecordTest(tester.genRecordTime("2023-04-03", "18:00:00")),
										).TimeRecord
									}(),
								},
								{
									KOTDailyWorking: tester.genKOTDailyWorkingTest(testKOTDailyWorkingProps{
										Date:        "2023-04-04",
										EmployeeKey: "2",
										BreakTime:   60,
										TotalWork:   480,
									}),
									TimeRecords: func() []KOTTimeRecord {
										return tester.genTimerecordDailyWorking("2023-04-04", "2",
											tester.withAttendanceStartRecordTest(tester.genRecordTime("2023-04-04", "09:00:00")),
											tester.withAttendanceEndRecordTest(tester.genRecordTime("2023-04-04", "18:00:00")),
											tester.withRestStartRecordTest(tester.genRecordTime("2023-04-04", "12:00:00")),
											tester.withRestEndRecordTest(tester.genRecordTime("2023-04-04", "13:00:00")),
										).TimeRecord
									}(),
								},
							},
						},
					},
					err: nil,
				}
			},
		},
		// CASE2 ==============================================================================================
		{
			name: `CASE2_[OK]:KOT data associated with employee is returned. 
					- userInfo includes employee that not registered KOT.
					- data has attendance start_end, rest start_end.`,
			setupMocks: func(t *testing.T) {
				/*
					Employee API
				*/
				tester.setupKOTEmployeeRequesterMock(t, KOTEmployeesResponse{
					{Code: "0001", Key: "1"},
					{Code: "0002", Key: "2"},
				}, nil)
				/*
					DailyWorkings API
				*/
				tester.setupKOTDailyWorkingsRequesterMock(t, []KOTDailyWorkings{
					{Date: "2023-04-03", DailyWorkings: []KOTDailyWorking{
						tester.genKOTDailyWorkingTest(testKOTDailyWorkingProps{
							Date:        "2023-04-03",
							EmployeeKey: "1",
							BreakTime:   60,
							TotalWork:   480,
						}),
						tester.genKOTDailyWorkingTest(testKOTDailyWorkingProps{
							Date:        "2023-04-03",
							EmployeeKey: "2",
							BreakTime:   120,
							TotalWork:   720,
						}),
					}},
					{Date: "2023-04-04", DailyWorkings: []KOTDailyWorking{
						tester.genKOTDailyWorkingTest(testKOTDailyWorkingProps{
							Date:        "2023-04-04",
							EmployeeKey: "1",
							BreakTime:   90,
							TotalWork:   540,
						}),
						tester.genKOTDailyWorkingTest(testKOTDailyWorkingProps{
							Date:        "2023-04-04",
							EmployeeKey: "2",
							BreakTime:   60,
							TotalWork:   480,
						}),
					}},
				}, nil)
				/*
					Timerecords API
				*/
				tester.setupKOTTimerecordsRequesterMock(t, []KOTTimerecord{
					{Date: "2023-04-03", DailyWorkings: []KOTTimerecordDailyWorking{
						tester.genTimerecordDailyWorking("2023-04-03", "1",
							tester.withAttendanceStartRecordTest(tester.genRecordTime("2023-04-03", "09:00:00")),
							tester.withAttendanceEndRecordTest(tester.genRecordTime("2023-04-03", "18:00:00")),
							tester.withRestStartRecordTest(tester.genRecordTime("2023-04-03", "12:00:00")),
							tester.withRestEndRecordTest(tester.genRecordTime("2023-04-03", "13:00:00")),
						),
						tester.genTimerecordDailyWorking("2023-04-03", "2",
							tester.withAttendanceStartRecordTest(tester.genRecordTime("2023-04-03", "08:00:00")),
							tester.withAttendanceEndRecordTest(tester.genRecordTime("2023-04-03", "22:00:00")),
							tester.withRestStartRecordTest(tester.genRecordTime("2023-04-03", "12:00:00")),
							tester.withRestEndRecordTest(tester.genRecordTime("2023-04-03", "13:00:00")),
							tester.withRestStartRecordTest(tester.genRecordTime("2023-04-03", "17:00:00")),
							tester.withRestEndRecordTest(tester.genRecordTime("2023-04-03", "18:00:00")),
						),
					}},
					{Date: "2023-04-04", DailyWorkings: []KOTTimerecordDailyWorking{
						tester.genTimerecordDailyWorking("2023-04-04", "1",
							tester.withAttendanceStartRecordTest(tester.genRecordTime("2023-04-04", "09:00:00")),
							tester.withAttendanceEndRecordTest(tester.genRecordTime("2023-04-04", "19:30:00")),
							tester.withRestStartRecordTest(tester.genRecordTime("2023-04-04", "12:00:00")),
							tester.withRestEndRecordTest(tester.genRecordTime("2023-04-04", "13:00:00")),
							tester.withRestStartRecordTest(tester.genRecordTime("2023-04-04", "15:00:00")),
							tester.withRestEndRecordTest(tester.genRecordTime("2023-04-04", "15:30:00")),
						),
						tester.genTimerecordDailyWorking("2023-04-04", "2",
							tester.withAttendanceStartRecordTest(tester.genRecordTime("2023-04-04", "09:00:00")),
							tester.withAttendanceEndRecordTest(tester.genRecordTime("2023-04-04", "18:00:00")),
							tester.withRestStartRecordTest(tester.genRecordTime("2023-04-04", "12:00:00")),
							tester.withRestEndRecordTest(tester.genRecordTime("2023-04-04", "13:00:00")),
						),
					}},
				}, nil)
			},
			getRequester: func() *kotReqester {
				return &kotReqester{
					actx:         &actx,
					integSetting: integSetting,
					client:       &clinet,
					days:         tester.genTimeRange("2023-04-01", "2023-04-29"),
					usersInfo: UsersInfo{
						{Id: 1, Code: "0001", DayEndTime: dayEndTime},
						{Id: 2, Code: "0002", DayEndTime: dayEndTime},
						{Id: 3, Code: "NOT_REGISTERED_KOT", DayEndTime: dayEndTime},
					},
					targetSystem: config.IntegTargetSystemKOT,
				}
			},
			getWant: func() want {
				return want{
					res: []KOTData{
						{
							EmployeeID:   "1",
							EmployeeCode: "0001",
							DayEndTime:   dayEndTime,
							Attendances: []KOTDailyWorkingWithTimerecords{
								{
									KOTDailyWorking: tester.genKOTDailyWorkingTest(testKOTDailyWorkingProps{
										Date:        "2023-04-03",
										EmployeeKey: "1",
										BreakTime:   60,
										TotalWork:   480,
									}),
									TimeRecords: func() []KOTTimeRecord {
										return tester.genTimerecordDailyWorking("2023-04-03", "1",
											tester.withAttendanceStartRecordTest(tester.genRecordTime("2023-04-03", "09:00:00")),
											tester.withAttendanceEndRecordTest(tester.genRecordTime("2023-04-03", "18:00:00")),
											tester.withRestStartRecordTest(tester.genRecordTime("2023-04-03", "12:00:00")),
											tester.withRestEndRecordTest(tester.genRecordTime("2023-04-03", "13:00:00")),
										).TimeRecord
									}(),
								},
								{
									KOTDailyWorking: tester.genKOTDailyWorkingTest(testKOTDailyWorkingProps{
										Date:        "2023-04-04",
										EmployeeKey: "1",
										BreakTime:   90,
										TotalWork:   540,
									}),
									TimeRecords: func() []KOTTimeRecord {
										return tester.genTimerecordDailyWorking("2023-04-04", "1",
											tester.withAttendanceStartRecordTest(tester.genRecordTime("2023-04-04", "09:00:00")),
											tester.withAttendanceEndRecordTest(tester.genRecordTime("2023-04-04", "19:30:00")),
											tester.withRestStartRecordTest(tester.genRecordTime("2023-04-04", "12:00:00")),
											tester.withRestEndRecordTest(tester.genRecordTime("2023-04-04", "13:00:00")),
											tester.withRestStartRecordTest(tester.genRecordTime("2023-04-04", "15:00:00")),
											tester.withRestEndRecordTest(tester.genRecordTime("2023-04-04", "15:30:00")),
										).TimeRecord
									}(),
								},
							},
						},
						{
							EmployeeID:   "2",
							EmployeeCode: "0002",
							DayEndTime:   dayEndTime,
							Attendances: []KOTDailyWorkingWithTimerecords{
								{
									KOTDailyWorking: tester.genKOTDailyWorkingTest(testKOTDailyWorkingProps{
										Date:        "2023-04-03",
										EmployeeKey: "2",
										BreakTime:   120,
										TotalWork:   720,
									}),
									TimeRecords: func() []KOTTimeRecord {
										return tester.genTimerecordDailyWorking("2023-04-03", "2",
											tester.withAttendanceStartRecordTest(tester.genRecordTime("2023-04-03", "08:00:00")),
											tester.withAttendanceEndRecordTest(tester.genRecordTime("2023-04-03", "22:00:00")),
											tester.withRestStartRecordTest(tester.genRecordTime("2023-04-03", "12:00:00")),
											tester.withRestEndRecordTest(tester.genRecordTime("2023-04-03", "13:00:00")),
											tester.withRestStartRecordTest(tester.genRecordTime("2023-04-03", "17:00:00")),
											tester.withRestEndRecordTest(tester.genRecordTime("2023-04-03", "18:00:00")),
										).TimeRecord
									}(),
								},
								{
									KOTDailyWorking: tester.genKOTDailyWorkingTest(testKOTDailyWorkingProps{
										Date:        "2023-04-04",
										EmployeeKey: "2",
										BreakTime:   60,
										TotalWork:   480,
									}),
									TimeRecords: func() []KOTTimeRecord {
										return tester.genTimerecordDailyWorking("2023-04-04", "2",
											tester.withAttendanceStartRecordTest(tester.genRecordTime("2023-04-04", "09:00:00")),
											tester.withAttendanceEndRecordTest(tester.genRecordTime("2023-04-04", "18:00:00")),
											tester.withRestStartRecordTest(tester.genRecordTime("2023-04-04", "12:00:00")),
											tester.withRestEndRecordTest(tester.genRecordTime("2023-04-04", "13:00:00")),
										).TimeRecord
									}(),
								},
							},
						},
					},
					err: nil,
				}
			},
		},
		// CASE3 ==============================================================================================
		{
			name: `CASE3_[OK]:KOT data associated with employee is returned. 
					- data has attendance start_end, rest start_end.
					- data has morning off and afternoo off and whole day off`,
			setupMocks: func(t *testing.T) {
				/*
					Employee API
				*/
				tester.setupKOTEmployeeRequesterMock(t, KOTEmployeesResponse{
					{Code: "0001", Key: "1"},
					{Code: "0002", Key: "2"},
				}, nil)
				/*
					DailyWorkings API
				*/
				tester.setupKOTDailyWorkingsRequesterMock(t, []KOTDailyWorkings{
					{Date: "2023-04-03", DailyWorkings: []KOTDailyWorking{
						tester.genKOTDailyWorkingTest(testKOTDailyWorkingProps{
							Date:        "2023-04-03",
							EmployeeKey: "1",
							BreakTime:   0,
							TotalWork:   0,
						}, tester.withFulltimeHolidayTest),
						tester.genKOTDailyWorkingTest(testKOTDailyWorkingProps{
							Date:        "2023-04-03",
							EmployeeKey: "2",
							BreakTime:   60,
							TotalWork:   480,
						}, tester.withMorningHalfHolidayTest),
					}},
					{Date: "2023-04-04", DailyWorkings: []KOTDailyWorking{
						tester.genKOTDailyWorkingTest(testKOTDailyWorkingProps{
							Date:        "2023-04-04",
							EmployeeKey: "1",
							BreakTime:   0,
							TotalWork:   240,
						}, tester.withAfternoonHalfHolidayTest),
						tester.genKOTDailyWorkingTest(testKOTDailyWorkingProps{
							Date:        "2023-04-04",
							EmployeeKey: "2",
							BreakTime:   0,
							TotalWork:   0,
						}, tester.withFulltimeHolidayTest),
					}},
				}, nil)
				/*
					Timerecords API
				*/
				tester.setupKOTTimerecordsRequesterMock(t, []KOTTimerecord{
					{Date: "2023-04-03", DailyWorkings: []KOTTimerecordDailyWorking{
						tester.genTimerecordDailyWorking("2023-04-03", "2",
							tester.withAttendanceStartRecordTest(tester.genRecordTime("2023-04-03", "13:00:00")),
							tester.withAttendanceEndRecordTest(tester.genRecordTime("2023-04-03", "22:00:00")),
							tester.withRestStartRecordTest(tester.genRecordTime("2023-04-03", "14:00:00")),
							tester.withRestEndRecordTest(tester.genRecordTime("2023-04-03", "15:00:00")),
						),
					}},
					{Date: "2023-04-04", DailyWorkings: []KOTTimerecordDailyWorking{
						tester.genTimerecordDailyWorking("2023-04-04", "1",
							tester.withAttendanceStartRecordTest(tester.genRecordTime("2023-04-04", "09:00:00")),
							tester.withAttendanceEndRecordTest(tester.genRecordTime("2023-04-04", "13:00:00")),
						),
					}},
				}, nil)
			},
			getRequester: func() *kotReqester {
				return &kotReqester{
					actx:         &actx,
					integSetting: integSetting,
					client:       &clinet,
					days:         tester.genTimeRange("2023-04-01", "2023-04-29"),
					usersInfo: UsersInfo{
						{Id: 1, Code: "0001", DayEndTime: dayEndTime},
						{Id: 2, Code: "0002", DayEndTime: dayEndTime},
					},
					targetSystem: config.IntegTargetSystemKOT,
				}
			},
			getWant: func() want {
				return want{
					res: []KOTData{
						{
							EmployeeID:   "1",
							EmployeeCode: "0001",
							DayEndTime:   dayEndTime,
							Attendances: []KOTDailyWorkingWithTimerecords{
								{
									KOTDailyWorking: tester.genKOTDailyWorkingTest(testKOTDailyWorkingProps{
										Date:        "2023-04-03",
										EmployeeKey: "1",
										BreakTime:   0,
										TotalWork:   0,
									}, tester.withFulltimeHolidayTest),
								},
								{
									KOTDailyWorking: tester.genKOTDailyWorkingTest(testKOTDailyWorkingProps{
										Date:        "2023-04-04",
										EmployeeKey: "1",
										BreakTime:   0,
										TotalWork:   240,
									}, tester.withAfternoonHalfHolidayTest),
									TimeRecords: func() []KOTTimeRecord {
										return tester.genTimerecordDailyWorking("2023-04-04", "1",
											tester.withAttendanceStartRecordTest(tester.genRecordTime("2023-04-04", "09:00:00")),
											tester.withAttendanceEndRecordTest(tester.genRecordTime("2023-04-04", "13:00:00")),
										).TimeRecord
									}(),
								},
							},
						},
						{
							EmployeeID:   "2",
							EmployeeCode: "0002",
							DayEndTime:   dayEndTime,
							Attendances: []KOTDailyWorkingWithTimerecords{
								{
									KOTDailyWorking: tester.genKOTDailyWorkingTest(testKOTDailyWorkingProps{
										Date:        "2023-04-03",
										EmployeeKey: "2",
										BreakTime:   60,
										TotalWork:   480,
									}, tester.withMorningHalfHolidayTest),
									TimeRecords: func() []KOTTimeRecord {
										return tester.genTimerecordDailyWorking("2023-04-03", "2",
											tester.withAttendanceStartRecordTest(tester.genRecordTime("2023-04-03", "13:00:00")),
											tester.withAttendanceEndRecordTest(tester.genRecordTime("2023-04-03", "22:00:00")),
											tester.withRestStartRecordTest(tester.genRecordTime("2023-04-03", "14:00:00")),
											tester.withRestEndRecordTest(tester.genRecordTime("2023-04-03", "15:00:00")),
										).TimeRecord
									}(),
								},
								{
									KOTDailyWorking: tester.genKOTDailyWorkingTest(testKOTDailyWorkingProps{
										Date:        "2023-04-04",
										EmployeeKey: "2",
										BreakTime:   0,
										TotalWork:   0,
									}, tester.withFulltimeHolidayTest),
								},
							},
						},
					},
					err: nil,
				}
			},
		},
		// CASE4 ==============================================================================================
		{
			name: `CASE4_[OK]:KOT data associated with employee is returned. 
					- data has attendance start_end, rest start_end.
					- data has hour day off or combination of hour and half day off`,
			setupMocks: func(t *testing.T) {
				/*
					Employee API
				*/
				tester.setupKOTEmployeeRequesterMock(t, KOTEmployeesResponse{
					{Code: "0001", Key: "1"},
					{Code: "0002", Key: "2"},
				}, nil)
				/*
					DailyWorkings API
				*/
				tester.setupKOTDailyWorkingsRequesterMock(t, []KOTDailyWorkings{
					{Date: "2023-04-03", DailyWorkings: []KOTDailyWorking{
						tester.genKOTDailyWorkingTest(testKOTDailyWorkingProps{
							Date:        "2023-04-03",
							EmployeeKey: "1",
							BreakTime:   60,
							TotalWork:   480,
						}, tester.withHourHolidayTest(60)),
						tester.genKOTDailyWorkingTest(testKOTDailyWorkingProps{
							Date:        "2023-04-03",
							EmployeeKey: "2",
							BreakTime:   120,
							TotalWork:   720,
						}),
					}},
					{Date: "2023-04-04", DailyWorkings: []KOTDailyWorking{
						tester.genKOTDailyWorkingTest(testKOTDailyWorkingProps{
							Date:        "2023-04-04",
							EmployeeKey: "1",
							BreakTime:   90,
							TotalWork:   540,
						}),
						tester.genKOTDailyWorkingTest(testKOTDailyWorkingProps{
							Date:        "2023-04-04",
							EmployeeKey: "2",
							BreakTime:   60,
							TotalWork:   480,
						}, tester.withMorningHalfHolidayTest, tester.withHourHolidayTest(60)),
					}},
				}, nil)
				/*
					Timerecords API
				*/
				tester.setupKOTTimerecordsRequesterMock(t, []KOTTimerecord{
					{Date: "2023-04-03", DailyWorkings: []KOTTimerecordDailyWorking{
						tester.genTimerecordDailyWorking("2023-04-03", "1",
							tester.withAttendanceStartRecordTest(tester.genRecordTime("2023-04-03", "09:00:00")),
							tester.withAttendanceEndRecordTest(tester.genRecordTime("2023-04-03", "18:00:00")),
							tester.withRestStartRecordTest(tester.genRecordTime("2023-04-03", "12:00:00")),
							tester.withRestEndRecordTest(tester.genRecordTime("2023-04-03", "13:00:00")),
						),
						tester.genTimerecordDailyWorking("2023-04-03", "2",
							tester.withAttendanceStartRecordTest(tester.genRecordTime("2023-04-03", "08:00:00")),
							tester.withAttendanceEndRecordTest(tester.genRecordTime("2023-04-03", "22:00:00")),
							tester.withRestStartRecordTest(tester.genRecordTime("2023-04-03", "12:00:00")),
							tester.withRestEndRecordTest(tester.genRecordTime("2023-04-03", "13:00:00")),
							tester.withRestStartRecordTest(tester.genRecordTime("2023-04-03", "17:00:00")),
							tester.withRestEndRecordTest(tester.genRecordTime("2023-04-03", "18:00:00")),
						),
					}},
					{Date: "2023-04-04", DailyWorkings: []KOTTimerecordDailyWorking{
						tester.genTimerecordDailyWorking("2023-04-04", "1",
							tester.withAttendanceStartRecordTest(tester.genRecordTime("2023-04-04", "09:00:00")),
							tester.withAttendanceEndRecordTest(tester.genRecordTime("2023-04-04", "19:30:00")),
							tester.withRestStartRecordTest(tester.genRecordTime("2023-04-04", "12:00:00")),
							tester.withRestEndRecordTest(tester.genRecordTime("2023-04-04", "13:00:00")),
							tester.withRestStartRecordTest(tester.genRecordTime("2023-04-04", "15:00:00")),
							tester.withRestEndRecordTest(tester.genRecordTime("2023-04-04", "15:30:00")),
						),
						tester.genTimerecordDailyWorking("2023-04-04", "2",
							tester.withAttendanceStartRecordTest(tester.genRecordTime("2023-04-04", "13:00:00")),
							tester.withAttendanceEndRecordTest(tester.genRecordTime("2023-04-04", "22:00:00")),
							tester.withRestStartRecordTest(tester.genRecordTime("2023-04-04", "18:00:00")),
							tester.withRestEndRecordTest(tester.genRecordTime("2023-04-04", "19:00:00")),
						),
					}},
				}, nil)
			},
			getRequester: func() *kotReqester {
				return &kotReqester{
					actx:         &actx,
					integSetting: integSetting,
					client:       &clinet,
					days:         tester.genTimeRange("2023-04-01", "2023-04-29"),
					usersInfo: UsersInfo{
						{Id: 1, Code: "0001", DayEndTime: dayEndTime},
						{Id: 2, Code: "0002", DayEndTime: dayEndTime},
					},
					targetSystem: config.IntegTargetSystemKOT,
				}
			},
			getWant: func() want {
				return want{
					res: []KOTData{
						{
							EmployeeID:   "1",
							EmployeeCode: "0001",
							DayEndTime:   dayEndTime,
							Attendances: []KOTDailyWorkingWithTimerecords{
								{
									KOTDailyWorking: tester.genKOTDailyWorkingTest(testKOTDailyWorkingProps{
										Date:        "2023-04-03",
										EmployeeKey: "1",
										BreakTime:   60,
										TotalWork:   480,
									}, tester.withHourHolidayTest(60)),
									TimeRecords: func() []KOTTimeRecord {
										return tester.genTimerecordDailyWorking("2023-04-03", "1",
											tester.withAttendanceStartRecordTest(tester.genRecordTime("2023-04-03", "09:00:00")),
											tester.withAttendanceEndRecordTest(tester.genRecordTime("2023-04-03", "18:00:00")),
											tester.withRestStartRecordTest(tester.genRecordTime("2023-04-03", "12:00:00")),
											tester.withRestEndRecordTest(tester.genRecordTime("2023-04-03", "13:00:00")),
										).TimeRecord
									}(),
								},
								{
									KOTDailyWorking: tester.genKOTDailyWorkingTest(testKOTDailyWorkingProps{
										Date:        "2023-04-04",
										EmployeeKey: "1",
										BreakTime:   90,
										TotalWork:   540,
									}),
									TimeRecords: func() []KOTTimeRecord {
										return tester.genTimerecordDailyWorking("2023-04-04", "1",
											tester.withAttendanceStartRecordTest(tester.genRecordTime("2023-04-04", "09:00:00")),
											tester.withAttendanceEndRecordTest(tester.genRecordTime("2023-04-04", "19:30:00")),
											tester.withRestStartRecordTest(tester.genRecordTime("2023-04-04", "12:00:00")),
											tester.withRestEndRecordTest(tester.genRecordTime("2023-04-04", "13:00:00")),
											tester.withRestStartRecordTest(tester.genRecordTime("2023-04-04", "15:00:00")),
											tester.withRestEndRecordTest(tester.genRecordTime("2023-04-04", "15:30:00")),
										).TimeRecord
									}(),
								},
							},
						},
						{
							EmployeeID:   "2",
							EmployeeCode: "0002",
							DayEndTime:   dayEndTime,
							Attendances: []KOTDailyWorkingWithTimerecords{
								{
									KOTDailyWorking: tester.genKOTDailyWorkingTest(testKOTDailyWorkingProps{
										Date:        "2023-04-03",
										EmployeeKey: "2",
										BreakTime:   120,
										TotalWork:   720,
									}),
									TimeRecords: func() []KOTTimeRecord {
										return tester.genTimerecordDailyWorking("2023-04-03", "2",
											tester.withAttendanceStartRecordTest(tester.genRecordTime("2023-04-03", "08:00:00")),
											tester.withAttendanceEndRecordTest(tester.genRecordTime("2023-04-03", "22:00:00")),
											tester.withRestStartRecordTest(tester.genRecordTime("2023-04-03", "12:00:00")),
											tester.withRestEndRecordTest(tester.genRecordTime("2023-04-03", "13:00:00")),
											tester.withRestStartRecordTest(tester.genRecordTime("2023-04-03", "17:00:00")),
											tester.withRestEndRecordTest(tester.genRecordTime("2023-04-03", "18:00:00")),
										).TimeRecord
									}(),
								},
								{
									KOTDailyWorking: tester.genKOTDailyWorkingTest(testKOTDailyWorkingProps{
										Date:        "2023-04-04",
										EmployeeKey: "2",
										BreakTime:   60,
										TotalWork:   480,
									}, tester.withMorningHalfHolidayTest, tester.withHourHolidayTest(60)),
									TimeRecords: func() []KOTTimeRecord {
										return tester.genTimerecordDailyWorking("2023-04-04", "2",
											tester.withAttendanceStartRecordTest(tester.genRecordTime("2023-04-04", "13:00:00")),
											tester.withAttendanceEndRecordTest(tester.genRecordTime("2023-04-04", "22:00:00")),
											tester.withRestStartRecordTest(tester.genRecordTime("2023-04-04", "18:00:00")),
											tester.withRestEndRecordTest(tester.genRecordTime("2023-04-04", "19:00:00")),
										).TimeRecord
									}(),
								},
							},
						},
					},
					err: nil,
				}
			},
		},
		// CASE5 ==============================================================================================
		{
			name: `CASE5_[NG]: Emloyee API returns error`,
			setupMocks: func(t *testing.T) {
				/*
					Employee API
				*/
				tester.setupKOTEmployeeRequesterMock(t, nil, &[]KOTError{
					{Message: "TEST_MSG", Code: 1},
					{Message: "TEST_MSG", Code: 2},
				})
				/*
					DailyWorkings API
				*/
				tester.setupKOTDailyWorkingsRequesterMock(t, []KOTDailyWorkings{
					{Date: "2023-04-03", DailyWorkings: []KOTDailyWorking{
						tester.genKOTDailyWorkingTest(testKOTDailyWorkingProps{
							Date:        "2023-04-03",
							EmployeeKey: "1",
							BreakTime:   60,
							TotalWork:   480,
						}),
						tester.genKOTDailyWorkingTest(testKOTDailyWorkingProps{
							Date:        "2023-04-03",
							EmployeeKey: "2",
							BreakTime:   120,
							TotalWork:   720,
						}),
					}},
					{Date: "2023-04-04", DailyWorkings: []KOTDailyWorking{
						tester.genKOTDailyWorkingTest(testKOTDailyWorkingProps{
							Date:        "2023-04-04",
							EmployeeKey: "1",
							BreakTime:   90,
							TotalWork:   540,
						}),
						tester.genKOTDailyWorkingTest(testKOTDailyWorkingProps{
							Date:        "2023-04-04",
							EmployeeKey: "2",
							BreakTime:   60,
							TotalWork:   480,
						}),
					}},
				}, nil)
				/*
					Timerecords API
				*/
				tester.setupKOTTimerecordsRequesterMock(t, []KOTTimerecord{
					{Date: "2023-04-03", DailyWorkings: []KOTTimerecordDailyWorking{
						tester.genTimerecordDailyWorking("2023-04-03", "1",
							tester.withAttendanceStartRecordTest(tester.genRecordTime("2023-04-03", "09:00:00")),
							tester.withAttendanceEndRecordTest(tester.genRecordTime("2023-04-03", "18:00:00")),
							tester.withRestStartRecordTest(tester.genRecordTime("2023-04-03", "12:00:00")),
							tester.withRestEndRecordTest(tester.genRecordTime("2023-04-03", "13:00:00")),
						),
						tester.genTimerecordDailyWorking("2023-04-03", "2",
							tester.withAttendanceStartRecordTest(tester.genRecordTime("2023-04-03", "08:00:00")),
							tester.withAttendanceEndRecordTest(tester.genRecordTime("2023-04-03", "22:00:00")),
							tester.withRestStartRecordTest(tester.genRecordTime("2023-04-03", "12:00:00")),
							tester.withRestEndRecordTest(tester.genRecordTime("2023-04-03", "13:00:00")),
							tester.withRestStartRecordTest(tester.genRecordTime("2023-04-03", "17:00:00")),
							tester.withRestEndRecordTest(tester.genRecordTime("2023-04-03", "18:00:00")),
						),
					}},
					{Date: "2023-04-04", DailyWorkings: []KOTTimerecordDailyWorking{
						tester.genTimerecordDailyWorking("2023-04-04", "1",
							tester.withAttendanceStartRecordTest(tester.genRecordTime("2023-04-04", "09:00:00")),
							tester.withAttendanceEndRecordTest(tester.genRecordTime("2023-04-04", "19:30:00")),
							tester.withRestStartRecordTest(tester.genRecordTime("2023-04-04", "12:00:00")),
							tester.withRestEndRecordTest(tester.genRecordTime("2023-04-04", "13:00:00")),
							tester.withRestStartRecordTest(tester.genRecordTime("2023-04-04", "15:00:00")),
							tester.withRestEndRecordTest(tester.genRecordTime("2023-04-04", "15:30:00")),
						),
						tester.genTimerecordDailyWorking("2023-04-04", "2",
							tester.withAttendanceStartRecordTest(tester.genRecordTime("2023-04-04", "09:00:00")),
							tester.withAttendanceEndRecordTest(tester.genRecordTime("2023-04-04", "18:00:00")),
							tester.withRestStartRecordTest(tester.genRecordTime("2023-04-04", "12:00:00")),
							tester.withRestEndRecordTest(tester.genRecordTime("2023-04-04", "13:00:00")),
						),
					}},
				}, nil)
			},
			getRequester: func() *kotReqester {
				return &kotReqester{
					actx:         &actx,
					integSetting: integSetting,
					client:       &clinet,
					days:         tester.genTimeRange("2023-04-01", "2023-04-29"),
					usersInfo: UsersInfo{
						{Id: 1, Code: "0001", DayEndTime: dayEndTime},
						{Id: 2, Code: "0002", DayEndTime: dayEndTime},
					},
					targetSystem: config.IntegTargetSystemKOT,
				}
			},
			getWant: func() want {
				return want{
					res: nil,
					err: &KOTErrorResponse{
						APIError: []KOTError{
							{Message: "TEST_MSG", Code: 1},
							{Message: "TEST_MSG", Code: 2},
						},
						sysError: ErrAPIAccess,
					},
				}
			},
		},
		// CASE6 ==============================================================================================
		{
			name: `CASE6_[NG]: DailyWorkings API returns error`,
			setupMocks: func(t *testing.T) {
				/*
					Employee API
				*/
				tester.setupKOTEmployeeRequesterMock(t, KOTEmployeesResponse{
					{Code: "0001", Key: "1"},
					{Code: "0002", Key: "2"},
				}, nil)
				/*
					DailyWorkings API
				*/
				tester.setupKOTDailyWorkingsRequesterMock(t, nil, &[]KOTError{
					{Message: "TEST_MSG", Code: 1},
					{Message: "TEST_MSG", Code: 2},
				})
				/*
					Timerecords API
				*/
				tester.setupKOTTimerecordsRequesterMock(t, []KOTTimerecord{
					{Date: "2023-04-03", DailyWorkings: []KOTTimerecordDailyWorking{
						tester.genTimerecordDailyWorking("2023-04-03", "1",
							tester.withAttendanceStartRecordTest(tester.genRecordTime("2023-04-03", "09:00:00")),
							tester.withAttendanceEndRecordTest(tester.genRecordTime("2023-04-03", "18:00:00")),
							tester.withRestStartRecordTest(tester.genRecordTime("2023-04-03", "12:00:00")),
							tester.withRestEndRecordTest(tester.genRecordTime("2023-04-03", "13:00:00")),
						),
						tester.genTimerecordDailyWorking("2023-04-03", "2",
							tester.withAttendanceStartRecordTest(tester.genRecordTime("2023-04-03", "08:00:00")),
							tester.withAttendanceEndRecordTest(tester.genRecordTime("2023-04-03", "22:00:00")),
							tester.withRestStartRecordTest(tester.genRecordTime("2023-04-03", "12:00:00")),
							tester.withRestEndRecordTest(tester.genRecordTime("2023-04-03", "13:00:00")),
							tester.withRestStartRecordTest(tester.genRecordTime("2023-04-03", "17:00:00")),
							tester.withRestEndRecordTest(tester.genRecordTime("2023-04-03", "18:00:00")),
						),
					}},
					{Date: "2023-04-04", DailyWorkings: []KOTTimerecordDailyWorking{
						tester.genTimerecordDailyWorking("2023-04-04", "1",
							tester.withAttendanceStartRecordTest(tester.genRecordTime("2023-04-04", "09:00:00")),
							tester.withAttendanceEndRecordTest(tester.genRecordTime("2023-04-04", "19:30:00")),
							tester.withRestStartRecordTest(tester.genRecordTime("2023-04-04", "12:00:00")),
							tester.withRestEndRecordTest(tester.genRecordTime("2023-04-04", "13:00:00")),
							tester.withRestStartRecordTest(tester.genRecordTime("2023-04-04", "15:00:00")),
							tester.withRestEndRecordTest(tester.genRecordTime("2023-04-04", "15:30:00")),
						),
						tester.genTimerecordDailyWorking("2023-04-04", "2",
							tester.withAttendanceStartRecordTest(tester.genRecordTime("2023-04-04", "09:00:00")),
							tester.withAttendanceEndRecordTest(tester.genRecordTime("2023-04-04", "18:00:00")),
							tester.withRestStartRecordTest(tester.genRecordTime("2023-04-04", "12:00:00")),
							tester.withRestEndRecordTest(tester.genRecordTime("2023-04-04", "13:00:00")),
						),
					}},
				}, nil)
			},
			getRequester: func() *kotReqester {
				return &kotReqester{
					actx:         &actx,
					integSetting: integSetting,
					client:       &clinet,
					days:         tester.genTimeRange("2023-04-01", "2023-04-29"),
					usersInfo: UsersInfo{
						{Id: 1, Code: "0001", DayEndTime: dayEndTime},
						{Id: 2, Code: "0002", DayEndTime: dayEndTime},
					},
					targetSystem: config.IntegTargetSystemKOT,
				}
			},
			getWant: func() want {
				return want{
					res: nil,
					err: &KOTErrorResponse{
						APIError: []KOTError{
							{Message: "TEST_MSG", Code: 1},
							{Message: "TEST_MSG", Code: 2},
						},
						sysError: ErrAPIAccess,
					},
				}
			},
		},
		// CASE7 ==============================================================================================
		{
			name: `CASE7_[NG]: Timerecord API returns error`,
			setupMocks: func(t *testing.T) {
				/*
					Employee API
				*/
				tester.setupKOTEmployeeRequesterMock(t, KOTEmployeesResponse{
					{Code: "0001", Key: "1"},
					{Code: "0002", Key: "2"},
				}, nil)
				/*
					DailyWorkings API
				*/
				tester.setupKOTDailyWorkingsRequesterMock(t, []KOTDailyWorkings{
					{Date: "2023-04-03", DailyWorkings: []KOTDailyWorking{
						tester.genKOTDailyWorkingTest(testKOTDailyWorkingProps{
							Date:        "2023-04-03",
							EmployeeKey: "1",
							BreakTime:   60,
							TotalWork:   480,
						}),
						tester.genKOTDailyWorkingTest(testKOTDailyWorkingProps{
							Date:        "2023-04-03",
							EmployeeKey: "2",
							BreakTime:   120,
							TotalWork:   720,
						}),
					}},
					{Date: "2023-04-04", DailyWorkings: []KOTDailyWorking{
						tester.genKOTDailyWorkingTest(testKOTDailyWorkingProps{
							Date:        "2023-04-04",
							EmployeeKey: "1",
							BreakTime:   90,
							TotalWork:   540,
						}),
						tester.genKOTDailyWorkingTest(testKOTDailyWorkingProps{
							Date:        "2023-04-04",
							EmployeeKey: "2",
							BreakTime:   60,
							TotalWork:   480,
						}),
					}},
				}, nil)
				/*
					Timerecords API
				*/
				tester.setupKOTTimerecordsRequesterMock(t, nil, &[]KOTError{
					{Message: "TEST_MSG", Code: 1},
					{Message: "TEST_MSG", Code: 2},
				})
			},
			getRequester: func() *kotReqester {
				return &kotReqester{
					actx:         &actx,
					integSetting: integSetting,
					client:       &clinet,
					days:         tester.genTimeRange("2023-04-01", "2023-04-29"),
					usersInfo: UsersInfo{
						{Id: 1, Code: "0001", DayEndTime: dayEndTime},
						{Id: 2, Code: "0002", DayEndTime: dayEndTime},
					},
					targetSystem: config.IntegTargetSystemKOT,
				}
			},
			getWant: func() want {
				return want{
					res: nil,
					err: &KOTErrorResponse{
						APIError: []KOTError{
							{Message: "TEST_MSG", Code: 1},
							{Message: "TEST_MSG", Code: 2},
						},
						sysError: ErrAPIAccess,
					},
				}
			},
		},
		// CASE8 ==============================================================================================
		{
			name: `CASE8_[OK]: No data. There is no employee who is match with KOT employee`,
			setupMocks: func(t *testing.T) {
				/*
					Employee API
				*/
				tester.setupKOTEmployeeRequesterMock(t, KOTEmployeesResponse{
					{Code: "0001", Key: "1"},
					{Code: "0002", Key: "2"},
				}, nil)
				/*
					DailyWorkings API
				*/
				tester.setupKOTDailyWorkingsRequesterMock(t, []KOTDailyWorkings{
					{Date: "2023-04-03", DailyWorkings: []KOTDailyWorking{
						tester.genKOTDailyWorkingTest(testKOTDailyWorkingProps{
							Date:        "2023-04-03",
							EmployeeKey: "1",
							BreakTime:   60,
							TotalWork:   480,
						}),
						tester.genKOTDailyWorkingTest(testKOTDailyWorkingProps{
							Date:        "2023-04-03",
							EmployeeKey: "2",
							BreakTime:   120,
							TotalWork:   720,
						}),
					}},
					{Date: "2023-04-04", DailyWorkings: []KOTDailyWorking{
						tester.genKOTDailyWorkingTest(testKOTDailyWorkingProps{
							Date:        "2023-04-04",
							EmployeeKey: "1",
							BreakTime:   90,
							TotalWork:   540,
						}),
						tester.genKOTDailyWorkingTest(testKOTDailyWorkingProps{
							Date:        "2023-04-04",
							EmployeeKey: "2",
							BreakTime:   60,
							TotalWork:   480,
						}),
					}},
				}, nil)
				/*
					Timerecords API
				*/
				tester.setupKOTTimerecordsRequesterMock(t, []KOTTimerecord{
					{Date: "2023-04-03", DailyWorkings: []KOTTimerecordDailyWorking{
						tester.genTimerecordDailyWorking("2023-04-03", "1",
							tester.withAttendanceStartRecordTest(tester.genRecordTime("2023-04-03", "09:00:00")),
							tester.withAttendanceEndRecordTest(tester.genRecordTime("2023-04-03", "18:00:00")),
							tester.withRestStartRecordTest(tester.genRecordTime("2023-04-03", "12:00:00")),
							tester.withRestEndRecordTest(tester.genRecordTime("2023-04-03", "13:00:00")),
						),
						tester.genTimerecordDailyWorking("2023-04-03", "2",
							tester.withAttendanceStartRecordTest(tester.genRecordTime("2023-04-03", "08:00:00")),
							tester.withAttendanceEndRecordTest(tester.genRecordTime("2023-04-03", "22:00:00")),
							tester.withRestStartRecordTest(tester.genRecordTime("2023-04-03", "12:00:00")),
							tester.withRestEndRecordTest(tester.genRecordTime("2023-04-03", "13:00:00")),
							tester.withRestStartRecordTest(tester.genRecordTime("2023-04-03", "17:00:00")),
							tester.withRestEndRecordTest(tester.genRecordTime("2023-04-03", "18:00:00")),
						),
					}},
					{Date: "2023-04-04", DailyWorkings: []KOTTimerecordDailyWorking{
						tester.genTimerecordDailyWorking("2023-04-04", "1",
							tester.withAttendanceStartRecordTest(tester.genRecordTime("2023-04-04", "09:00:00")),
							tester.withAttendanceEndRecordTest(tester.genRecordTime("2023-04-04", "19:30:00")),
							tester.withRestStartRecordTest(tester.genRecordTime("2023-04-04", "12:00:00")),
							tester.withRestEndRecordTest(tester.genRecordTime("2023-04-04", "13:00:00")),
							tester.withRestStartRecordTest(tester.genRecordTime("2023-04-04", "15:00:00")),
							tester.withRestEndRecordTest(tester.genRecordTime("2023-04-04", "15:30:00")),
						),
						tester.genTimerecordDailyWorking("2023-04-04", "2",
							tester.withAttendanceStartRecordTest(tester.genRecordTime("2023-04-04", "09:00:00")),
							tester.withAttendanceEndRecordTest(tester.genRecordTime("2023-04-04", "18:00:00")),
							tester.withRestStartRecordTest(tester.genRecordTime("2023-04-04", "12:00:00")),
							tester.withRestEndRecordTest(tester.genRecordTime("2023-04-04", "13:00:00")),
						),
					}},
				}, nil)
			},
			getRequester: func() *kotReqester {
				return &kotReqester{
					actx:         &actx,
					integSetting: integSetting,
					client:       &clinet,
					days:         tester.genTimeRange("2023-04-01", "2023-04-29"),
					usersInfo: UsersInfo{
						{Id: 1, Code: "NOT_MATCHED_1", DayEndTime: dayEndTime},
						{Id: 2, Code: "NOT_MATCHED_2", DayEndTime: dayEndTime},
					},
					targetSystem: config.IntegTargetSystemKOT,
				}
			},
			getWant: func() want {
				return want{
					res: []KOTData{},
					err: nil,
				}
			},
		},
		// CASE9 ==============================================================================================
		{
			name: `CASE1_[OK]:KOT data associated with employee is returned. 
			         - one KOT employee		
			         - data has attendance start_end, rest start_end.`,
			setupMocks: func(t *testing.T) {
				/*
					Employee API
				*/
				tester.setupKOTEmployeeRequesterMock(t, KOTEmployeesResponse{
					{Code: "0001", Key: "1"},
				}, nil)
				/*
					DailyWorkings API
				*/
				tester.setupKOTDailyWorkingsRequesterMock(t, []KOTDailyWorkings{
					{Date: "2023-04-03", DailyWorkings: []KOTDailyWorking{
						tester.genKOTDailyWorkingTest(testKOTDailyWorkingProps{
							Date:        "2023-04-03",
							EmployeeKey: "1",
							BreakTime:   60,
							TotalWork:   480,
						}),
					}},
					{Date: "2023-04-04", DailyWorkings: []KOTDailyWorking{
						tester.genKOTDailyWorkingTest(testKOTDailyWorkingProps{
							Date:        "2023-04-04",
							EmployeeKey: "1",
							BreakTime:   90,
							TotalWork:   540,
						}),
					}},
				}, nil)
				/*
					Timerecords API
				*/
				tester.setupKOTTimerecordsRequesterMock(t, []KOTTimerecord{
					{Date: "2023-04-03", DailyWorkings: []KOTTimerecordDailyWorking{
						tester.genTimerecordDailyWorking("2023-04-03", "1",
							tester.withAttendanceStartRecordTest(tester.genRecordTime("2023-04-03", "09:00:00")),
							tester.withAttendanceEndRecordTest(tester.genRecordTime("2023-04-03", "18:00:00")),
							tester.withRestStartRecordTest(tester.genRecordTime("2023-04-03", "12:00:00")),
							tester.withRestEndRecordTest(tester.genRecordTime("2023-04-03", "13:00:00")),
						),
					}},
					{Date: "2023-04-04", DailyWorkings: []KOTTimerecordDailyWorking{
						tester.genTimerecordDailyWorking("2023-04-04", "1",
							tester.withAttendanceStartRecordTest(tester.genRecordTime("2023-04-04", "09:00:00")),
							tester.withAttendanceEndRecordTest(tester.genRecordTime("2023-04-04", "19:30:00")),
							tester.withRestStartRecordTest(tester.genRecordTime("2023-04-04", "12:00:00")),
							tester.withRestEndRecordTest(tester.genRecordTime("2023-04-04", "13:00:00")),
							tester.withRestStartRecordTest(tester.genRecordTime("2023-04-04", "15:00:00")),
							tester.withRestEndRecordTest(tester.genRecordTime("2023-04-04", "15:30:00")),
						),
					}},
				}, nil)
			},
			getRequester: func() *kotReqester {
				return &kotReqester{
					actx:         &actx,
					integSetting: integSetting,
					client:       &clinet,
					days:         tester.genTimeRange("2023-04-01", "2023-04-29"),
					usersInfo: UsersInfo{
						{Id: 1, Code: "0001", DayEndTime: dayEndTime},
						{Id: 2, Code: "0002", DayEndTime: dayEndTime},
					},
					targetSystem: config.IntegTargetSystemKOT,
				}
			},
			getWant: func() want {
				return want{
					res: []KOTData{
						{
							EmployeeID:   "1",
							EmployeeCode: "0001",
							DayEndTime:   dayEndTime,
							Attendances: []KOTDailyWorkingWithTimerecords{
								{
									KOTDailyWorking: tester.genKOTDailyWorkingTest(testKOTDailyWorkingProps{
										Date:        "2023-04-03",
										EmployeeKey: "1",
										BreakTime:   60,
										TotalWork:   480,
									}),
									TimeRecords: func() []KOTTimeRecord {
										return tester.genTimerecordDailyWorking("2023-04-03", "1",
											tester.withAttendanceStartRecordTest(tester.genRecordTime("2023-04-03", "09:00:00")),
											tester.withAttendanceEndRecordTest(tester.genRecordTime("2023-04-03", "18:00:00")),
											tester.withRestStartRecordTest(tester.genRecordTime("2023-04-03", "12:00:00")),
											tester.withRestEndRecordTest(tester.genRecordTime("2023-04-03", "13:00:00")),
										).TimeRecord
									}(),
								},
								{
									KOTDailyWorking: tester.genKOTDailyWorkingTest(testKOTDailyWorkingProps{
										Date:        "2023-04-04",
										EmployeeKey: "1",
										BreakTime:   90,
										TotalWork:   540,
									}),
									TimeRecords: func() []KOTTimeRecord {
										return tester.genTimerecordDailyWorking("2023-04-04", "1",
											tester.withAttendanceStartRecordTest(tester.genRecordTime("2023-04-04", "09:00:00")),
											tester.withAttendanceEndRecordTest(tester.genRecordTime("2023-04-04", "19:30:00")),
											tester.withRestStartRecordTest(tester.genRecordTime("2023-04-04", "12:00:00")),
											tester.withRestEndRecordTest(tester.genRecordTime("2023-04-04", "13:00:00")),
											tester.withRestStartRecordTest(tester.genRecordTime("2023-04-04", "15:00:00")),
											tester.withRestEndRecordTest(tester.genRecordTime("2023-04-04", "15:30:00")),
										).TimeRecord
									}(),
								},
							},
						},
					},
					err: nil,
				}
			},
		},

		// CASE10 ==============================================================================================
		{
			name: `CASE10_[NG]: Emloyee API returns single error`,
			setupMocks: func(t *testing.T) {
				/*
					Employee API
				*/
				tester.setupKOTEmployeeRequesterMock(t, nil, &[]KOTError{
					{Message: "TEST_MSG", Code: 1},
				})
				/*
					DailyWorkings API
				*/
				tester.setupKOTDailyWorkingsRequesterMock(t, []KOTDailyWorkings{
					{Date: "2023-04-03", DailyWorkings: []KOTDailyWorking{
						tester.genKOTDailyWorkingTest(testKOTDailyWorkingProps{
							Date:        "2023-04-03",
							EmployeeKey: "1",
							BreakTime:   60,
							TotalWork:   480,
						}),
						tester.genKOTDailyWorkingTest(testKOTDailyWorkingProps{
							Date:        "2023-04-03",
							EmployeeKey: "2",
							BreakTime:   120,
							TotalWork:   720,
						}),
					}},
					{Date: "2023-04-04", DailyWorkings: []KOTDailyWorking{
						tester.genKOTDailyWorkingTest(testKOTDailyWorkingProps{
							Date:        "2023-04-04",
							EmployeeKey: "1",
							BreakTime:   90,
							TotalWork:   540,
						}),
						tester.genKOTDailyWorkingTest(testKOTDailyWorkingProps{
							Date:        "2023-04-04",
							EmployeeKey: "2",
							BreakTime:   60,
							TotalWork:   480,
						}),
					}},
				}, nil)
				/*
					Timerecords API
				*/
				tester.setupKOTTimerecordsRequesterMock(t, []KOTTimerecord{
					{Date: "2023-04-03", DailyWorkings: []KOTTimerecordDailyWorking{
						tester.genTimerecordDailyWorking("2023-04-03", "1",
							tester.withAttendanceStartRecordTest(tester.genRecordTime("2023-04-03", "09:00:00")),
							tester.withAttendanceEndRecordTest(tester.genRecordTime("2023-04-03", "18:00:00")),
							tester.withRestStartRecordTest(tester.genRecordTime("2023-04-03", "12:00:00")),
							tester.withRestEndRecordTest(tester.genRecordTime("2023-04-03", "13:00:00")),
						),
						tester.genTimerecordDailyWorking("2023-04-03", "2",
							tester.withAttendanceStartRecordTest(tester.genRecordTime("2023-04-03", "08:00:00")),
							tester.withAttendanceEndRecordTest(tester.genRecordTime("2023-04-03", "22:00:00")),
							tester.withRestStartRecordTest(tester.genRecordTime("2023-04-03", "12:00:00")),
							tester.withRestEndRecordTest(tester.genRecordTime("2023-04-03", "13:00:00")),
							tester.withRestStartRecordTest(tester.genRecordTime("2023-04-03", "17:00:00")),
							tester.withRestEndRecordTest(tester.genRecordTime("2023-04-03", "18:00:00")),
						),
					}},
					{Date: "2023-04-04", DailyWorkings: []KOTTimerecordDailyWorking{
						tester.genTimerecordDailyWorking("2023-04-04", "1",
							tester.withAttendanceStartRecordTest(tester.genRecordTime("2023-04-04", "09:00:00")),
							tester.withAttendanceEndRecordTest(tester.genRecordTime("2023-04-04", "19:30:00")),
							tester.withRestStartRecordTest(tester.genRecordTime("2023-04-04", "12:00:00")),
							tester.withRestEndRecordTest(tester.genRecordTime("2023-04-04", "13:00:00")),
							tester.withRestStartRecordTest(tester.genRecordTime("2023-04-04", "15:00:00")),
							tester.withRestEndRecordTest(tester.genRecordTime("2023-04-04", "15:30:00")),
						),
						tester.genTimerecordDailyWorking("2023-04-04", "2",
							tester.withAttendanceStartRecordTest(tester.genRecordTime("2023-04-04", "09:00:00")),
							tester.withAttendanceEndRecordTest(tester.genRecordTime("2023-04-04", "18:00:00")),
							tester.withRestStartRecordTest(tester.genRecordTime("2023-04-04", "12:00:00")),
							tester.withRestEndRecordTest(tester.genRecordTime("2023-04-04", "13:00:00")),
						),
					}},
				}, nil)
			},
			getRequester: func() *kotReqester {
				return &kotReqester{
					actx:         &actx,
					integSetting: integSetting,
					client:       &clinet,
					days:         tester.genTimeRange("2023-04-01", "2023-04-29"),
					usersInfo: UsersInfo{
						{Id: 1, Code: "0001", DayEndTime: dayEndTime},
						{Id: 2, Code: "0002", DayEndTime: dayEndTime},
					},
					targetSystem: config.IntegTargetSystemKOT,
				}
			},
			getWant: func() want {
				return want{
					res: nil,
					err: &KOTError{
						Message: "TEST_MSG", Code: 1,
						sysError: ErrAPIAccess,
					},
				}
			},
		},
		// CASE11 ==============================================================================================
		{
			name: `CASE11_[NG]: DailyWorkings API returns single error`,
			setupMocks: func(t *testing.T) {
				/*
					Employee API
				*/
				tester.setupKOTEmployeeRequesterMock(t, KOTEmployeesResponse{
					{Code: "0001", Key: "1"},
					{Code: "0002", Key: "2"},
				}, nil)
				/*
					DailyWorkings API
				*/
				tester.setupKOTDailyWorkingsRequesterMock(t, nil, &[]KOTError{
					{Message: "TEST_MSG", Code: 1},
				})
				/*
					Timerecords API
				*/
				tester.setupKOTTimerecordsRequesterMock(t, []KOTTimerecord{
					{Date: "2023-04-03", DailyWorkings: []KOTTimerecordDailyWorking{
						tester.genTimerecordDailyWorking("2023-04-03", "1",
							tester.withAttendanceStartRecordTest(tester.genRecordTime("2023-04-03", "09:00:00")),
							tester.withAttendanceEndRecordTest(tester.genRecordTime("2023-04-03", "18:00:00")),
							tester.withRestStartRecordTest(tester.genRecordTime("2023-04-03", "12:00:00")),
							tester.withRestEndRecordTest(tester.genRecordTime("2023-04-03", "13:00:00")),
						),
						tester.genTimerecordDailyWorking("2023-04-03", "2",
							tester.withAttendanceStartRecordTest(tester.genRecordTime("2023-04-03", "08:00:00")),
							tester.withAttendanceEndRecordTest(tester.genRecordTime("2023-04-03", "22:00:00")),
							tester.withRestStartRecordTest(tester.genRecordTime("2023-04-03", "12:00:00")),
							tester.withRestEndRecordTest(tester.genRecordTime("2023-04-03", "13:00:00")),
							tester.withRestStartRecordTest(tester.genRecordTime("2023-04-03", "17:00:00")),
							tester.withRestEndRecordTest(tester.genRecordTime("2023-04-03", "18:00:00")),
						),
					}},
					{Date: "2023-04-04", DailyWorkings: []KOTTimerecordDailyWorking{
						tester.genTimerecordDailyWorking("2023-04-04", "1",
							tester.withAttendanceStartRecordTest(tester.genRecordTime("2023-04-04", "09:00:00")),
							tester.withAttendanceEndRecordTest(tester.genRecordTime("2023-04-04", "19:30:00")),
							tester.withRestStartRecordTest(tester.genRecordTime("2023-04-04", "12:00:00")),
							tester.withRestEndRecordTest(tester.genRecordTime("2023-04-04", "13:00:00")),
							tester.withRestStartRecordTest(tester.genRecordTime("2023-04-04", "15:00:00")),
							tester.withRestEndRecordTest(tester.genRecordTime("2023-04-04", "15:30:00")),
						),
						tester.genTimerecordDailyWorking("2023-04-04", "2",
							tester.withAttendanceStartRecordTest(tester.genRecordTime("2023-04-04", "09:00:00")),
							tester.withAttendanceEndRecordTest(tester.genRecordTime("2023-04-04", "18:00:00")),
							tester.withRestStartRecordTest(tester.genRecordTime("2023-04-04", "12:00:00")),
							tester.withRestEndRecordTest(tester.genRecordTime("2023-04-04", "13:00:00")),
						),
					}},
				}, nil)
			},
			getRequester: func() *kotReqester {
				return &kotReqester{
					actx:         &actx,
					integSetting: integSetting,
					client:       &clinet,
					days:         tester.genTimeRange("2023-04-01", "2023-04-29"),
					usersInfo: UsersInfo{
						{Id: 1, Code: "0001", DayEndTime: dayEndTime},
						{Id: 2, Code: "0002", DayEndTime: dayEndTime},
					},
					targetSystem: config.IntegTargetSystemKOT,
				}
			},
			getWant: func() want {
				return want{
					res: nil,
					err: &KOTError{
						Message: "TEST_MSG", Code: 1,
						sysError: ErrAPIAccess,
					},
				}
			},
		},
		// CASE12 ==============================================================================================
		{
			name: `CASE12_[NG]: Timerecord API returns single error`,
			setupMocks: func(t *testing.T) {
				/*
					Employee API
				*/
				tester.setupKOTEmployeeRequesterMock(t, KOTEmployeesResponse{
					{Code: "0001", Key: "1"},
					{Code: "0002", Key: "2"},
				}, nil)
				/*
					DailyWorkings API
				*/
				tester.setupKOTDailyWorkingsRequesterMock(t, []KOTDailyWorkings{
					{Date: "2023-04-03", DailyWorkings: []KOTDailyWorking{
						tester.genKOTDailyWorkingTest(testKOTDailyWorkingProps{
							Date:        "2023-04-03",
							EmployeeKey: "1",
							BreakTime:   60,
							TotalWork:   480,
						}),
						tester.genKOTDailyWorkingTest(testKOTDailyWorkingProps{
							Date:        "2023-04-03",
							EmployeeKey: "2",
							BreakTime:   120,
							TotalWork:   720,
						}),
					}},
					{Date: "2023-04-04", DailyWorkings: []KOTDailyWorking{
						tester.genKOTDailyWorkingTest(testKOTDailyWorkingProps{
							Date:        "2023-04-04",
							EmployeeKey: "1",
							BreakTime:   90,
							TotalWork:   540,
						}),
						tester.genKOTDailyWorkingTest(testKOTDailyWorkingProps{
							Date:        "2023-04-04",
							EmployeeKey: "2",
							BreakTime:   60,
							TotalWork:   480,
						}),
					}},
				}, nil)
				/*
					Timerecords API
				*/
				tester.setupKOTTimerecordsRequesterMock(t, nil, &[]KOTError{
					{Message: "TEST_MSG", Code: 1},
				})
			},
			getRequester: func() *kotReqester {
				return &kotReqester{
					actx:         &actx,
					integSetting: integSetting,
					client:       &clinet,
					days:         tester.genTimeRange("2023-04-01", "2023-04-29"),
					usersInfo: UsersInfo{
						{Id: 1, Code: "0001", DayEndTime: dayEndTime},
						{Id: 2, Code: "0002", DayEndTime: dayEndTime},
					},
					targetSystem: config.IntegTargetSystemKOT,
				}
			},
			getWant: func() want {
				return want{
					res: nil,
					err: &KOTError{
						Message: "TEST_MSG", Code: 1,
						sysError: ErrAPIAccess,
					},
				}
			},
		},
	}
	for _, test := range cases {
		t.Run(test.name, func(t *testing.T) {
			test.setupMocks(t)
			want := test.getWant()
			requester := test.getRequester()
			res, err := requester.GetAttData()
			if err != nil {
				assert.Equal(t, want.err, err)
				return
			}
			assert.ElementsMatch(t, want.res, res)
		})
	}
}

type kotRequesterTester struct{}

func (kot *kotRequesterTester) setupKOTHTTPMock() func() {
	httpmock.ActivateNonDefault(client.GetClient())
	return func() {
		httpmock.DeactivateAndReset()
	}
}

func (kot *kotRequesterTester) setupKOTEmployeeRequesterMock(t *testing.T, res KOTEmployeesResponse, errRes *[]KOTError) {
	t.Helper()
	if errRes != nil {
		httpmock.RegisterResponder("GET", config.Integ.KOT.EmployeeApiUrl,
			func(req *http.Request) (*http.Response, error) {
				var res map[string]any
				if len(*errRes) == 1 {
					e := (*errRes)[0]
					res = map[string]any{"message": e.Message, "code": e.Code}
				} else {
					resMap := kot.structToJSONMap(t, errRes)
					res = map[string]any{"errors": resMap}
				}
				resp, err := httpmock.NewJsonResponse(http.StatusBadRequest, &res)
				if err != nil {
					return httpmock.NewStringResponse(http.StatusInternalServerError, ""), nil
				}
				return resp, nil
			})
		return
	}
	httpmock.RegisterResponder("GET", config.Integ.KOT.EmployeeApiUrl,
		func(req *http.Request) (*http.Response, error) {
			resMap := kot.structToJSONMap(t, res)
			resp, err := httpmock.NewJsonResponse(http.StatusOK, &resMap)
			if err != nil {
				return httpmock.NewStringResponse(http.StatusInternalServerError, ""), nil
			}
			return resp, nil
		})
}

func (kot *kotRequesterTester) setupKOTDailyWorkingsRequesterMock(t *testing.T, res []KOTDailyWorkings, errRes *[]KOTError) {
	t.Helper()
	if errRes != nil {
		httpmock.RegisterResponder("GET", config.Integ.KOT.DailyWorkingsApiUrl,
			func(req *http.Request) (*http.Response, error) {
				var res map[string]any
				if len(*errRes) == 1 {
					e := (*errRes)[0]
					res = map[string]any{"message": e.Message, "code": e.Code}
				} else {
					resMap := kot.structToJSONMap(t, errRes)
					res = map[string]any{"errors": resMap}
				}
				resp, err := httpmock.NewJsonResponse(http.StatusBadRequest, &res)
				if err != nil {
					return httpmock.NewStringResponse(http.StatusInternalServerError, ""), nil
				}
				return resp, nil
			})
		return
	}
	httpmock.RegisterResponder("GET", config.Integ.KOT.DailyWorkingsApiUrl,
		func(req *http.Request) (*http.Response, error) {
			resMap := kot.structToJSONMap(t, res)
			resp, err := httpmock.NewJsonResponse(http.StatusOK, &resMap)
			if err != nil {
				return httpmock.NewStringResponse(http.StatusInternalServerError, ""), nil
			}
			return resp, nil
		})
}

func (kot *kotRequesterTester) setupKOTTimerecordsRequesterMock(t *testing.T, res []KOTTimerecord, errRes *[]KOTError) {
	t.Helper()
	if errRes != nil {
		httpmock.RegisterResponder("GET", config.Integ.KOT.DailyWorkingsTimerecordApiUrl,
			func(req *http.Request) (*http.Response, error) {
				var res map[string]any
				if len(*errRes) == 1 {
					e := (*errRes)[0]
					res = map[string]any{"message": e.Message, "code": e.Code}
				} else {
					resMap := kot.structToJSONMap(t, errRes)
					res = map[string]any{"errors": resMap}
				}
				resp, err := httpmock.NewJsonResponse(http.StatusBadRequest, &res)
				if err != nil {
					return httpmock.NewStringResponse(http.StatusInternalServerError, ""), nil
				}
				return resp, nil
			})
		return
	}
	httpmock.RegisterResponder("GET", config.Integ.KOT.DailyWorkingsTimerecordApiUrl,
		func(req *http.Request) (*http.Response, error) {
			resMap := kot.structToJSONMap(t, res)
			resp, err := httpmock.NewJsonResponse(http.StatusOK, &resMap)
			if err != nil {
				return httpmock.NewStringResponse(http.StatusInternalServerError, ""), nil
			}
			return resp, nil
		})
}
func (kot *kotRequesterTester) structToJSONMap(t *testing.T, res interface{}) []map[string]interface{} {
	responseJSON, err := json.Marshal(&res)
	if err != nil {
		t.Fatalf("fail to marshal json: %v", err)
	}
	resMap := []map[string]interface{}{}
	err = json.Unmarshal(responseJSON, &resMap)
	if err != nil {
		t.Fatalf("fail to marshal json: %v", err)
	}
	return resMap
}

type testKOTDailyWorkingProps struct {
	Date        string
	EmployeeKey string
	BreakTime   int
	TotalWork   int
}

type TestHolidayOptionFn func(ho *KOTHolidaysObtained)

func (kot *kotRequesterTester) genKOTDailyWorkingTest(props testKOTDailyWorkingProps, optFn ...TestHolidayOptionFn) KOTDailyWorking {
	holiday := KOTHolidaysObtained{}
	for _, fn := range optFn {
		fn(&holiday)
	}
	return KOTDailyWorking{
		Date:             props.Date,
		EmployeeKey:      props.EmployeeKey,
		BreakTime:        props.BreakTime,
		TotalWork:        props.TotalWork,
		HolidaysObtained: holiday,
	}
}

func (kot *kotRequesterTester) withMorningHalfHolidayTest(ho *KOTHolidaysObtained) {
	morningOff := KOTHalfdayHoliday{
		Code:     "1",
		Name:     "有給",
		TypeName: string(config.KOTHalfdayHolidayMorningOff),
	}
	ho.HalfdayHolidays = append(ho.HalfdayHolidays, morningOff)
}

func (kot *kotRequesterTester) withAfternoonHalfHolidayTest(ho *KOTHolidaysObtained) {
	afternoonOff := KOTHalfdayHoliday{
		Code:     "1",
		Name:     "有給",
		TypeName: string(config.KOTHalfdayHolidayAfternoonOff),
	}
	ho.HalfdayHolidays = append(ho.HalfdayHolidays, afternoonOff)
}

func (kot *kotRequesterTester) withFulltimeHolidayTest(ho *KOTHolidaysObtained) {
	n := "公休"
	c := "11"
	fullOff := KOTFulltimeHoliday{
		Code: &c,
		Name: &n,
	}
	ho.FulltimeHoliday = fullOff
}

func (kot *kotRequesterTester) withHourHolidayTest(min int) TestHolidayOptionFn {
	return func(ho *KOTHolidaysObtained) {
		hourHoliday := KOTHourHoliday{
			// we decided to see hourHoliday as breakTime
			// so far, there is no need to check start and end
			Start:   "",
			End:     "",
			Minutes: fmt.Sprintf("%d", min),
			Code:    "11",
			Name:    "時間休",
		}
		ho.HourHolidays = append(ho.HourHolidays, hourHoliday)
	}
}

type testTimeRecordOptionFn func(trs *[]KOTTimeRecord)

func (kot *kotRequesterTester) genTimerecordDailyWorking(date string, EmpKey string, optsFn ...testTimeRecordOptionFn) KOTTimerecordDailyWorking {
	timerecords := []KOTTimeRecord{}
	for _, fn := range optsFn {
		fn(&timerecords)
	}
	return KOTTimerecordDailyWorking{
		Date:        date,
		EmployeeKey: EmpKey,
		TimeRecord:  timerecords,
	}
}

func (kot *kotRequesterTester) withAttendanceStartRecordTest(t time.Time) testTimeRecordOptionFn {
	return func(trs *[]KOTTimeRecord) {
		ret := append(*trs, KOTTimeRecord{
			Time: t,
			Code: string(config.KOTAttendanceStart),
		})
		*trs = ret
	}
}

func (kot *kotRequesterTester) withAttendanceEndRecordTest(t time.Time) testTimeRecordOptionFn {
	return func(trs *[]KOTTimeRecord) {
		ret := append(*trs, KOTTimeRecord{
			Time: t,
			Code: string(config.KOTAttendanceEnd),
		})
		*trs = ret
	}
}

func (kot *kotRequesterTester) withRestStartRecordTest(t time.Time) testTimeRecordOptionFn {
	return func(trs *[]KOTTimeRecord) {
		ret := append(*trs, KOTTimeRecord{
			Time: t,
			Code: "3",
		})
		*trs = ret
	}
}

func (kot *kotRequesterTester) withRestEndRecordTest(t time.Time) testTimeRecordOptionFn {
	return func(trs *[]KOTTimeRecord) {
		ret := append(*trs, KOTTimeRecord{
			Time: t,
			Code: "4",
		})
		*trs = ret
	}
}

func (kot *kotRequesterTester) genRecordTime(date string, timeStr string) time.Time {
	layout := "2006-01-02 15:04:05"
	base := fmt.Sprintf("%s %s", date, timeStr)
	ret, err := time.Parse(layout, base)
	if err != nil {
		panic(err)
	}
	return ret
}
func (kot *kotRequesterTester) genTimeRange(start, end string) []time.Time {
	layout := "2006-01-02"
	s, err := time.Parse(layout, start)
	if err != nil {
		panic(err)
	}
	e, err := time.Parse(layout, end)
	if err != nil {
		panic(err)
	}
	ret := []time.Time{s}
	max := 100
	for i := 1; i < max; i++ {
		next := s.Add(time.Hour * time.Duration(24*i))
		if e.Before(next) {
			break
		}
		ret = append(ret, next)
	}
	return ret
}

func Test_kotReqester_isValidTimerecords(t *testing.T) {
	l := "2006-01-02 15:04"
	tests := []struct {
		name string

		genArgs func() []KOTTimeRecord
		want    bool
	}{
		{
			name: "OK:1 pair attendance",
			genArgs: func() []KOTTimeRecord {
				start1, err := time.Parse(l, "2023-04-03 09:00")
				if err != nil {
					t.Fatal(err)
				}

				end1, err := time.Parse(l, "2023-04-03 18:00")
				if err != nil {
					t.Fatal(err)
				}

				return []KOTTimeRecord{
					{
						Time: start1,
						Code: string(config.KOTAttendanceStart),
					},
					{
						Time: end1,
						Code: string(config.KOTAttendanceEnd),
					},
				}
			},
			want: true,
		},
		{
			name: "OK:1 pair attendance and 1 pair rests",
			genArgs: func() []KOTTimeRecord {
				start1, err := time.Parse(l, "2023-04-03 09:00")
				if err != nil {
					t.Fatal(err)
				}
				restStart1, err := time.Parse(l, "2023-04-03 12:00")
				if err != nil {
					t.Fatal(err)
				}
				restEnd1, err := time.Parse(l, "2023-04-03 13:00")
				if err != nil {
					t.Fatal(err)
				}
				end1, err := time.Parse(l, "2023-04-03 18:00")
				if err != nil {
					t.Fatal(err)
				}

				return []KOTTimeRecord{
					{
						Time: start1,
						Code: string(config.KOTAttendanceStart),
					},
					{
						Time: restStart1,
						Code: string(config.KOTRestStart),
					},
					{
						Time: restEnd1,
						Code: string(config.KOTRestEnd),
					},
					{
						Time: end1,
						Code: string(config.KOTAttendanceEnd),
					},
				}
			},
			want: true,
		},
		{
			name: "OK:2 pair attendance and 2 pair rests",
			genArgs: func() []KOTTimeRecord {
				start1, err := time.Parse(l, "2023-04-03 09:00")
				if err != nil {
					t.Fatal(err)
				}
				restStart1, err := time.Parse(l, "2023-04-03 12:00")
				if err != nil {
					t.Fatal(err)
				}
				restEnd1, err := time.Parse(l, "2023-04-03 13:00")
				if err != nil {
					t.Fatal(err)
				}
				end1, err := time.Parse(l, "2023-04-03 18:00")
				if err != nil {
					t.Fatal(err)
				}

				start2, err := time.Parse(l, "2023-04-03 19:00")
				if err != nil {
					t.Fatal(err)
				}
				restStart2, err := time.Parse(l, "2023-04-03 20:00")
				if err != nil {
					t.Fatal(err)
				}
				restEnd2, err := time.Parse(l, "2023-04-03 20:30")
				if err != nil {
					t.Fatal(err)
				}
				end2, err := time.Parse(l, "2023-04-03 22:00")
				if err != nil {
					t.Fatal(err)
				}

				return []KOTTimeRecord{
					{
						Time: start1,
						Code: string(config.KOTAttendanceStart),
					},
					{
						Time: restStart1,
						Code: string(config.KOTRestStart),
					},
					{
						Time: restEnd1,
						Code: string(config.KOTRestEnd),
					},
					{
						Time: end1,
						Code: string(config.KOTAttendanceEnd),
					},
					{
						Time: start2,
						Code: string(config.KOTAttendanceStart),
					},
					{
						Time: restStart2,
						Code: string(config.KOTRestStart),
					},
					{
						Time: restEnd2,
						Code: string(config.KOTRestEnd),
					},
					{
						Time: end2,
						Code: string(config.KOTAttendanceEnd),
					},
				}
			},
			want: true,
		},
		{
			name: "NG:attendance that start and end are reversed",
			genArgs: func() []KOTTimeRecord {
				end1, err := time.Parse(l, "2023-04-03 09:00")
				if err != nil {
					t.Fatal(err)
				}
				start1, err := time.Parse(l, "2023-04-03 10:00")
				if err != nil {
					t.Fatal(err)
				}

				end2, err := time.Parse(l, "2023-04-03 13:00")
				if err != nil {
					t.Fatal(err)
				}
				start2, err := time.Parse(l, "2023-04-03 13:01")
				if err != nil {
					t.Fatal(err)
				}

				return []KOTTimeRecord{
					{
						Time: end1,
						Code: string(config.KOTAttendanceEnd),
					},
					{
						Time: start1,
						Code: string(config.KOTAttendanceStart),
					},
					{
						Time: end2,
						Code: string(config.KOTAttendanceEnd),
					},
					{
						Time: start2,
						Code: string(config.KOTAttendanceStart),
					},
				}
			},
			want: false,
		},
		{
			name: "NG:attendance that start and end are reversed#2",
			genArgs: func() []KOTTimeRecord {
				end1, err := time.Parse(l, "2023-04-03 09:00")
				if err != nil {
					t.Fatal(err)
				}
				start1, err := time.Parse(l, "2023-04-03 10:00")
				if err != nil {
					t.Fatal(err)
				}

				start2, err := time.Parse(l, "2023-04-03 13:00")
				if err != nil {
					t.Fatal(err)
				}
				end2, err := time.Parse(l, "2023-04-03 13:01")
				if err != nil {
					t.Fatal(err)
				}

				return []KOTTimeRecord{
					{
						Time: end1,
						Code: string(config.KOTAttendanceEnd),
					},
					{
						Time: start1,
						Code: string(config.KOTAttendanceStart),
					},
					{
						Time: start2,
						Code: string(config.KOTAttendanceStart),
					},
					{
						Time: end2,
						Code: string(config.KOTAttendanceEnd),
					},
				}
			},
			want: false,
		},
		{
			name: "NG:attendance that start and end are reversed#3",
			genArgs: func() []KOTTimeRecord {
				start1, err := time.Parse(l, "2023-04-03 09:00")
				if err != nil {
					t.Fatal(err)
				}
				end1, err := time.Parse(l, "2023-04-03 10:00")
				if err != nil {
					t.Fatal(err)
				}

				end2, err := time.Parse(l, "2023-04-03 13:00")
				if err != nil {
					t.Fatal(err)
				}
				start2, err := time.Parse(l, "2023-04-03 13:01")
				if err != nil {
					t.Fatal(err)
				}

				return []KOTTimeRecord{
					{
						Time: end1,
						Code: string(config.KOTAttendanceEnd),
					},
					{
						Time: start1,
						Code: string(config.KOTAttendanceStart),
					},
					{
						Time: end2,
						Code: string(config.KOTAttendanceEnd),
					},
					{
						Time: start2,
						Code: string(config.KOTAttendanceStart),
					},
				}
			},
			want: false,
		},
		{
			name: "OK:no recored",
			genArgs: func() []KOTTimeRecord {
				return []KOTTimeRecord{}
			},
			want: true,
		},
		{
			name: "NG:one record",
			genArgs: func() []KOTTimeRecord {
				start1, err := time.Parse(l, "2023-04-03 09:00")
				if err != nil {
					t.Fatal(err)
				}
				return []KOTTimeRecord{

					{
						Time: start1,
						Code: string(config.KOTAttendanceStart),
					},
				}
			},
			want: false,
		},
		{
			name: "NG:incompleted attendance",
			genArgs: func() []KOTTimeRecord {
				start1, err := time.Parse(l, "2023-04-03 09:00")
				if err != nil {
					t.Fatal(err)
				}
				restStart1, err := time.Parse(l, "2023-04-03 12:00")
				if err != nil {
					t.Fatal(err)
				}
				restEnd1, err := time.Parse(l, "2023-04-03 13:00")
				if err != nil {
					t.Fatal(err)
				}
				end1, err := time.Parse(l, "2023-04-03 18:00")
				if err != nil {
					t.Fatal(err)
				}
				end2, err := time.Parse(l, "2023-04-03 19:00")
				if err != nil {
					t.Fatal(err)
				}

				return []KOTTimeRecord{
					{
						Time: start1,
						Code: string(config.KOTAttendanceStart),
					},
					{
						Time: restStart1,
						Code: string(config.KOTRestStart),
					},
					{
						Time: restEnd1,
						Code: string(config.KOTRestEnd),
					},
					{
						Time: end1,
						Code: string(config.KOTAttendanceEnd),
					},
					{
						Time: end2,
						Code: string(config.KOTAttendanceEnd),
					},
				}
			},
			want: false,
		},
		{
			name: "NG:incompleted attendance#2",
			genArgs: func() []KOTTimeRecord {
				start1, err := time.Parse(l, "2023-04-03 09:00")
				if err != nil {
					t.Fatal(err)
				}
				start2, err := time.Parse(l, "2023-04-03 09:01")
				if err != nil {
					t.Fatal(err)
				}
				restStart1, err := time.Parse(l, "2023-04-03 12:00")
				if err != nil {
					t.Fatal(err)
				}
				restEnd1, err := time.Parse(l, "2023-04-03 13:00")
				if err != nil {
					t.Fatal(err)
				}
				end1, err := time.Parse(l, "2023-04-03 18:00")
				if err != nil {
					t.Fatal(err)
				}

				return []KOTTimeRecord{
					{
						Time: start1,
						Code: string(config.KOTAttendanceStart),
					},
					{
						Time: start2,
						Code: string(config.KOTAttendanceStart),
					},
					{
						Time: restStart1,
						Code: string(config.KOTRestStart),
					},
					{
						Time: restEnd1,
						Code: string(config.KOTRestEnd),
					},
					{
						Time: end1,
						Code: string(config.KOTAttendanceEnd),
					},
				}
			},
			want: false,
		},
		{
			name: "NG:incompleted rest",
			genArgs: func() []KOTTimeRecord {
				start1, err := time.Parse(l, "2023-04-03 09:00")
				if err != nil {
					t.Fatal(err)
				}
				restStart1, err := time.Parse(l, "2023-04-03 12:00")
				if err != nil {
					t.Fatal(err)
				}
				end1, err := time.Parse(l, "2023-04-03 18:00")
				if err != nil {
					t.Fatal(err)
				}

				return []KOTTimeRecord{
					{
						Time: start1,
						Code: string(config.KOTAttendanceStart),
					},
					{
						Time: restStart1,
						Code: string(config.KOTRestStart),
					},
					{
						Time: end1,
						Code: string(config.KOTAttendanceEnd),
					},
				}
			},
			want: false,
		},
		{
			name: "NG:incompleted rest#2",
			genArgs: func() []KOTTimeRecord {
				start1, err := time.Parse(l, "2023-04-03 09:00")
				if err != nil {
					t.Fatal(err)
				}
				restEnd1, err := time.Parse(l, "2023-04-03 13:00")
				if err != nil {
					t.Fatal(err)
				}
				end1, err := time.Parse(l, "2023-04-03 18:00")
				if err != nil {
					t.Fatal(err)
				}

				return []KOTTimeRecord{
					{
						Time: start1,
						Code: string(config.KOTAttendanceStart),
					},
					{
						Time: restEnd1,
						Code: string(config.KOTRestEnd),
					},
					{
						Time: end1,
						Code: string(config.KOTAttendanceEnd),
					},
				}
			},
			want: false,
		},
		{
			name: "NG:incompleted rest#3",
			genArgs: func() []KOTTimeRecord {
				start1, err := time.Parse(l, "2023-04-03 09:00")
				if err != nil {
					t.Fatal(err)
				}

				restStart1, err := time.Parse(l, "2023-04-03 12:00")
				if err != nil {
					t.Fatal(err)
				}
				restStart2, err := time.Parse(l, "2023-04-03 13:00")
				if err != nil {
					t.Fatal(err)
				}
				restEnd1, err := time.Parse(l, "2023-04-03 13:30")
				if err != nil {
					t.Fatal(err)
				}
				restEnd2, err := time.Parse(l, "2023-04-03 14:00")
				if err != nil {
					t.Fatal(err)
				}
				end1, err := time.Parse(l, "2023-04-03 18:00")
				if err != nil {
					t.Fatal(err)
				}

				return []KOTTimeRecord{
					{
						Time: start1,
						Code: string(config.KOTAttendanceStart),
					},

					{
						Time: restStart1,
						Code: string(config.KOTRestStart),
					},
					{
						Time: restStart2,
						Code: string(config.KOTRestStart),
					},
					{
						Time: restEnd1,
						Code: string(config.KOTRestEnd),
					},
					{
						Time: restEnd2,
						Code: string(config.KOTRestEnd),
					},
					{
						Time: end1,
						Code: string(config.KOTAttendanceEnd),
					},
				}
			},
			want: false,
		},
		{
			name: "NG:invalid rest(wrong place)",
			genArgs: func() []KOTTimeRecord {
				start1, err := time.Parse(l, "2023-04-03 09:00")
				if err != nil {
					t.Fatal(err)
				}

				end1, err := time.Parse(l, "2023-04-03 18:00")
				if err != nil {
					t.Fatal(err)
				}
				restStart1, err := time.Parse(l, "2023-04-03 18:00")
				if err != nil {
					t.Fatal(err)
				}

				restEnd1, err := time.Parse(l, "2023-04-03 18:30")
				if err != nil {
					t.Fatal(err)
				}

				return []KOTTimeRecord{
					{
						Time: start1,
						Code: string(config.KOTAttendanceStart),
					},
					{
						Time: end1,
						Code: string(config.KOTAttendanceEnd),
					},
					{
						Time: restStart1,
						Code: string(config.KOTRestStart),
					},
					{
						Time: restEnd1,
						Code: string(config.KOTRestEnd),
					},
				}
			},
			want: false,
		},
		{
			name: "NG:invalid rest(wrong place)#2",
			genArgs: func() []KOTTimeRecord {
				start1, err := time.Parse(l, "2023-04-03 09:00")
				if err != nil {
					t.Fatal(err)
				}

				end1, err := time.Parse(l, "2023-04-03 18:00")
				if err != nil {
					t.Fatal(err)
				}
				restStart1, err := time.Parse(l, "2023-04-03 18:00")
				if err != nil {
					t.Fatal(err)
				}

				restEnd1, err := time.Parse(l, "2023-04-03 18:30")
				if err != nil {
					t.Fatal(err)
				}
				start2, err := time.Parse(l, "2023-04-03 19:00")
				if err != nil {
					t.Fatal(err)
				}

				end2, err := time.Parse(l, "2023-04-03 22:00")
				if err != nil {
					t.Fatal(err)
				}

				return []KOTTimeRecord{
					{
						Time: start1,
						Code: string(config.KOTAttendanceStart),
					},
					{
						Time: end1,
						Code: string(config.KOTAttendanceEnd),
					},
					{
						Time: restStart1,
						Code: string(config.KOTRestStart),
					},
					{
						Time: restEnd1,
						Code: string(config.KOTRestEnd),
					},
					{
						Time: start2,
						Code: string(config.KOTAttendanceStart),
					},
					{
						Time: end2,
						Code: string(config.KOTAttendanceEnd),
					},
				}
			},
			want: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			r := &kotReqester{}
			if got := r.isValidTimerecords(tt.genArgs()); got != tt.want {
				t.Errorf("kotReqester.isValidTimerecords() = %v, want %v", got, tt.want)
			}
		})
	}
}
