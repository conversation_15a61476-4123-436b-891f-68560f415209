package integration

import (
	"errors"
	"sort"
	"strconv"
	"time"

	"gitlab.com/innopm/deneb/config"
	"gitlab.com/innopm/deneb/domain/context"
	"gitlab.com/innopm/deneb/domain/model"
	"gitlab.com/innopm/deneb/util"
	"gitlab.com/innopm/deneb/util/dateutil"
	"gitlab.com/innopm/deneb/util/strutil"
)

var (
	ErrInvalidKOTAttendanceData = errors.New("invalid kot attendance data was found")
)

type KOTConverter struct {
	Ctx          *context.Context
	EmpIdByCode  *map[string]int
	Src          interface{}
	SettingId    int
	targetSystem config.IntegTargetSystem
}

func NewKOTConverter(cr *ConvReq) Converter {
	ctx := (*cr)
	return &KOTConverter{Ctx: ctx.Ctx, EmpIdByCode: ctx.EmpIdByCode,
		Src: ctx.Src, SettingId: cr.IntegSettingId, targetSystem: config.IntegTargetSystemKOT}
}

func (k *KOTConverter) Convert() (ret AttData) {
	// TODO??
	return
}

func (k *KOTConverter) ToIntegAttsByEmployees() (integAtts []model.IntegAttByEmployee) {
	empIdByCode := *(*k).EmpIdByCode
	kotRes := ((*k).Src).([]KOTData)
	for _, data := range kotRes {
		empID := empIdByCode[data.EmployeeCode]
		attendances := k.toIntegAttsByEmployee(empID, data.EmployeeCode, data)
		if len(attendances) > 0 {
			employeeInfo := model.NewIntegAttEmployeeInfo(
				empID,
				data.EmployeeCode,
				dateutil.ToTimeFormat(data.DayEndTime),
			)
			integAtt := model.NewIntegAttByEmployee(employeeInfo, attendances)
			integAtts = append(integAtts, integAtt)
		}
	}
	return
}

func (k *KOTConverter) toIntegAttsByEmployee(empID int, empCode string,
	data KOTData) (atts []model.IntegAttInfo) {
	for _, attendance := range data.Attendances {
		att, err := k.toIntegAttInfo(empID, attendance)
		if err != nil {
			continue
		}
		atts = append(atts, att)
	}
	return
}

func (k *KOTConverter) toIntegAttInfo(empID int,
	attendance KOTDailyWorkingWithTimerecords) (att model.IntegAttInfo, err error) {
	// invalid date format
	if !strutil.IsDateFormat(&attendance.Date) {
		return att, ErrInvalidJobcanAttendanceData
	}
	date := util.DateToTime(attendance.Date)

	// day off(check day off first, start and end is null)
	holday := k.getHolidayType(attendance)
	if holday == config.TsHldyWholeDay {
		return model.NewIntegAttInfo(
			k.SettingId,
			string(k.targetSystem),
			empID,
			date,
			dateutil.HourClockTotalDurToTime(date, emptyTime),
			dateutil.HourClockTotalDurToTime(date, emptyTime),
			int(attendance.TotalWork),
			int(attendance.BreakTime),
			int(config.TsHldyWholeDay),
		), nil
	}

	rest, err := k.getRestMins(attendance)
	if err != nil {
		return
	}
	start, err := k.getStartTime(attendance)
	if err != nil {
		return
	}
	end, err := k.getEndTime(attendance)
	if err != nil {
		return
	}
	return model.NewIntegAttInfo(
		k.SettingId,
		string(k.targetSystem),
		empID,
		date,
		start,
		end,
		int(attendance.TotalWork),
		rest,
		int(holday),
	), nil
}

func (k *KOTConverter) getStartTime(attendance KOTDailyWorkingWithTimerecords) (time.Time, error) {
	starts := []time.Time{}
	for _, tr := range attendance.TimeRecords {
		if tr.Code == string(config.KOTAttendanceStart) {
			starts = append(starts, tr.Time)
		}
	}
	sort.Slice(starts, func(i, j int) bool {
		return starts[i].Before(starts[j])
	})

	if len(starts) == 0 {
		return time.Time{}, nil
	}

	start := starts[0]
	return time.Parse("2006-01-02 15:04:05", start.Format("2006-01-02 15:04:05"))
}

func (k *KOTConverter) getEndTime(attendance KOTDailyWorkingWithTimerecords) (time.Time, error) {
	ends := []time.Time{}
	for _, tr := range attendance.TimeRecords {
		if tr.Code == string(config.KOTAttendanceEnd) {
			ends = append(ends, tr.Time)

		}
	}
	sort.Slice(ends, func(i, j int) bool {
		return ends[i].Before(ends[j])
	})

	if len(ends) == 0 {
		return time.Time{}, nil
	}

	end := ends[len(ends)-1]
	return time.Parse("2006-01-02 15:04:05", end.Format("2006-01-02 15:04:05"))
}

func (k *KOTConverter) getHolidayType(attendance KOTDailyWorkingWithTimerecords) config.TsHolidayType {
	if attendance.HolidaysObtained.FulltimeHoliday.Code != nil &&
		*attendance.HolidaysObtained.FulltimeHoliday.Code != "" {
		return config.TsHldyWholeDay
	}
	for _, h := range attendance.HolidaysObtained.HalfdayHolidays {
		if config.KOTHalfdayHolidayTypeName(h.TypeName) == config.KOTHalfdayHolidayMorningOff {
			return config.TsHldyAM
		}
		if config.KOTHalfdayHolidayTypeName(h.TypeName) == config.KOTHalfdayHolidayAfternoonOff {
			return config.TsHldyPM
		}
	}
	return config.TsHldyNone
}

func (k *KOTConverter) getRestMins(attendance KOTDailyWorkingWithTimerecords) (int, error) {
	breakMins := attendance.BreakTime
	// HourHolidays will be handled as break time
	for _, hh := range attendance.HolidaysObtained.HourHolidays {
		mins, err := strconv.Atoi(hh.Minutes)
		if err != nil {
			return 0, err
		}
		breakMins += mins
	}

	if len(attendance.TimeRecords) == 0 {
		return breakMins, nil
	}

	kotAtts := newKotAttendances(attendance.TimeRecords)
	intervalMins := kotAtts.getIntervalMins()

	breakMins += intervalMins
	return breakMins, nil
}

type kotAttendances []kotAttendance

func newKotAttendances(timerecords []KOTTimeRecord) kotAttendances {
	if len(timerecords) > 1 {
		sort.Slice(timerecords, func(i, j int) bool {
			return timerecords[i].Time.Before(timerecords[j].Time)
		})
	}
	attendances := kotAttendances(make([]kotAttendance, len(timerecords)))
	for _, tr := range timerecords {
		attendances.setTimerecord(tr)
	}
	return attendances
}

func (atts *kotAttendances) setTimerecord(tr KOTTimeRecord) {
	for i, att := range *atts {
		if !att.isFixed() {
			ok := att.trySetTimerecord(tr)
			if !ok {
				continue
			}
			attsSli := []kotAttendance(*atts)
			attsSli[i] = att
			kot := kotAttendances(attsSli)
			atts = &kot
			break
		}
	}
}
func (atts *kotAttendances) getIntervalMins() int {
	if len(*atts) == 0 {
		return 0
	}

	mins := 0
	loopNum := len(*atts) / 2
	for i := 0; i < loopNum; i++ {
		next := i + 1
		if len(*atts) < next+1 {
			break
		}
		att1 := (*atts)[i]
		att2 := (*atts)[next]
		intervalMins := att1.getIntervalMins(att2)
		mins += intervalMins
	}
	return mins
}

type kotAttendance struct {
	start *time.Time
	end   *time.Time
}

func (k *kotAttendance) trySetTimerecord(tr KOTTimeRecord) bool {
	if k.start == nil && (tr.Code == string(config.KOTAttendanceStart) || tr.Code == string(config.KOTRestStart)) {
		k.start = &tr.Time
		return true
	}
	if k.end == nil && (tr.Code == string(config.KOTAttendanceEnd) || tr.Code == string(config.KOTRestEnd)) {
		k.end = &tr.Time
		return true
	}
	return false
}

func (k *kotAttendance) isFixed() bool {
	return k.start != nil && k.end != nil
}

func (k *kotAttendance) getIntervalMins(att kotAttendance) int {
	if !k.isFixed() || !att.isFixed() {
		return 0
	}
	interval := att.start.Sub(*k.end)
	mins := interval.Minutes()
	if mins < 0 {
		return 0
	}
	return int(mins)
}
