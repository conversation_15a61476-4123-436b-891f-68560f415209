package dao

import (
	"errors"
	"fmt"
	"sort"
	"strings"
	"time"

	"gitlab.com/innopm/deneb/common/idb"
	"gitlab.com/innopm/deneb/domain/model"
	"gitlab.com/innopm/deneb/domain/model/export"
	"gitlab.com/innopm/deneb/domain/model/tbl"
	"gitlab.com/innopm/deneb/domain/repo"
	"gitlab.com/innopm/deneb/util"
	"gitlab.com/innopm/deneb/util/dateutil"
	"gorm.io/gorm"
)

func NewProjectRepo(db *gorm.DB) repo.Project {
	return &project{
		db: db,
	}
}

type project struct {
	basedao
	db *gorm.DB
}

func (d *project) Find(compNo int, findOpt *model.FindOption,
	withOpt *model.WithOption, pOpt *model.PermOption) (projects []tbl.Project, total int, err error) {

	base := d.db.Model(&projects).
		Scopes(d.withPreload(withOpt)).
		Where("company_no = ?", compNo).
		Scopes(d.filterByFields(findOpt)).
		Order("project_no")

	if findOpt != nil && findOpt.Filters["user_ids"] != nil {
		base = d.filterByUserIDs(compNo, base, findOpt.Filters["user_ids"])
	}

	if pOpt != nil {
		base = base.Scopes(
			idb.ProjectByUserPermission(compNo, pOpt.UserID, pOpt.DepartmentNo,
				pOpt.Access, pOpt.PermType),
		)
	}

	total, err = d.findWithFindOption(base, findOpt, tbl.Project{}, &projects)
	return
}

func (d *project) FindAvailable(
	compNo, empID int,
	orderColumn, order string,
	findOpt model.FindOption,
	dates []time.Time,
	projectStr string,
) (
	projects []tbl.Project,
	total int,
	err error,
) {
	if len(dates) >= 1 {
		// Sort by date in order of newest to oldest
		sort.Slice(dates, func(i, j int) bool {
			return dates[i].Before(dates[j])
		})
	}

	search := findOpt.FuzzyKey
	// we decided not to use filterByFindOption with fuzzy search
	// because filterByFindOption can not do across two tables
	// we use project.fuzzySearch
	findOpt.FuzzyKey = ""

	// CAUTION: DO NOT pass the orderColumn and order directly to the user input
	// because it can be SQL injection
	// we need to check the orderColumn and order
	// and make sure it is safe to use
	orderStr := fmt.Sprintf("%s %s, project_no DESC", orderColumn, order)

	mainQuery := "WITH" +
		" cte_project_emp AS (?)," +
		" cte_project_dept AS (?)," +
		" cte_project AS (?)" +
		" ?"

	cteJobtime := d.buildCTEJobtime(empID)
	cteProjectEmp, err := d.buildCTEProjectEmp(
		compNo,
		empID,
		cteJobtime,
		dates,
		orderStr,
		search,
		projectStr,
		findOpt,
	)
	if err != nil {
		return
	}

	cteProjectDept, err := d.buildCTEProjectDept(
		compNo,
		empID,
		cteJobtime,
		dates,
		orderStr,
		search,
		projectStr,
		findOpt,
	)
	if err != nil {
		return
	}

	cteProject := d.buildCTEProject()
	baseQuery := d.buildBaseQuery(orderStr)

	err = d.db.
		Raw(
			mainQuery,
			cteProjectEmp,
			cteProjectDept,
			cteProject,
			baseQuery,
		).
		Preload("Customer").
		Find(&projects).
		Error
	if err != nil {
		return
	}

	total = len(projects)

	return
}

func (d *project) buildCTEProjectEmp(
	compNo, empID int,
	cteJobtime *gorm.DB,
	dates []time.Time,
	order, search, projectStr string,
	findOpt model.FindOption,
) (*gorm.DB, error) {
	basePrjEmp := d.db.
		Session(&gorm.Session{NewDB: true}).
		Table("project").
		Joins("INNER JOIN project_employee pe ON project.project_no = pe.project_no").
		Joins("LEFT JOIN (?) j ON project.project_no = j.project_no", cteJobtime).
		Where("pe.employee_no = ?", empID).
		Where("pe.auth_timesheet_edit = ?", true).
		Where("project.company_no = ?", compNo).
		Where("project.active_flag = ?", true).
		Where("project.start_date <= ?", dates[0]).
		Where("project.end_date >= ?", dates[len(dates)-1]).
		Order(order).
		Scopes(d.fuzzySearch(search, projectStr)).
		Select([]string{
			"project.project_no",
			"project.project_name",
			"project.project_cd",
			"project.active_flag",
			"project.customer_no",
			"project.start_date",
			"project.end_date",
			"project.create_time",
			"j.update_time",
		})

	prjEmp, _, err := d.filterByFindOption(basePrjEmp, &findOpt, tbl.Project{})
	if err != nil {
		return nil, err
	}

	return prjEmp, nil
}

func (d *project) buildCTEProjectDept(
	compNo, empID int,
	cteJobtime *gorm.DB,
	dates []time.Time,
	order, search, projectStr string,
	findOpt model.FindOption,
) (*gorm.DB, error) {
	basePrjDept := d.db.
		Session(&gorm.Session{NewDB: true}).
		Table("project").
		Joins("INNER JOIN project_department pd ON project.project_no = pd.project_no").
		Joins("INNER JOIN employee e ON pd.department_no = e.department_no").
		Joins("LEFT JOIN (?) j ON project.project_no = j.project_no", cteJobtime).
		Where("e.employee_no = ?", empID).
		Where("pd.auth_timesheet_edit = ?", true).
		Where("project.company_no = ?", compNo).
		Where("pd.company_no = ?", compNo).
		Where("project.active_flag = ?", true).
		Where("project.start_date <= ?", dates[0]).
		Where("project.end_date >= ?", dates[len(dates)-1]).
		Order(order).
		Scopes(d.fuzzySearch(search, projectStr)).
		Select([]string{
			"project.project_no",
			"project.project_name",
			"project.project_cd",
			"project.active_flag",
			"project.customer_no",
			"project.start_date",
			"project.end_date",
			"project.create_time",
			"j.update_time",
		})

	prjDept, _, err := d.filterByFindOption(basePrjDept, &findOpt, tbl.Project{})
	if err != nil {
		return nil, err
	}

	return prjDept, nil
}

func (d *project) buildCTEProject() *gorm.DB {
	mainQuery := "? UNION ?"

	prjEmpSubQuery := d.db.Table("cte_project_emp").Select("*")
	prjDeptSubQuery := d.db.Table("cte_project_dept").Select("*")

	return d.db.Raw(mainQuery, prjEmpSubQuery, prjDeptSubQuery)
}

func (d *project) buildCTEJobtime(empID int) *gorm.DB {
	return d.db.
		Session(&gorm.Session{NewDB: true}).
		Table("jobtime").
		Where("employee_no = ?", empID).
		Group("project_no").
		Order("update_time DESC").
		Select([]string{
			"project_no",
			"MAX(update_time) AS update_time",
		})
}

func (d *project) buildBaseQuery(order string) *gorm.DB {
	return d.db.
		Session(&gorm.Session{NewDB: true}).
		Table("cte_project").
		Order(order).
		Select([]string{
			"DISTINCT project_no",
			"project_name",
			"project_cd",
			"active_flag",
			"customer_no",
			"start_date",
			"end_date",
			"create_time",
			"update_time",
		})
}

func (d *project) fuzzySearch(search string, projectStr string) func(tx *gorm.DB) *gorm.DB {
	return func(tx *gorm.DB) *gorm.DB {
		if search == "" {
			return tx
		}

		likeSearch := fmt.Sprintf("%%%s%%", search)

		or := tx.
			Session(&gorm.Session{NewDB: true}).
			Or("project.project_name LIKE ?", likeSearch).
			Or("project.project_cd LIKE ?", likeSearch)

		if strings.Contains(projectStr, "2") {
			tx.Joins("LEFT JOIN customer ON customer.customer_no = project.customer_no")
			or = or.Or("customer.customer_name LIKE ?", likeSearch)
		}

		return tx.Where(or)
	}
}

func (d *project) FindExport(compID int, findOpt *model.FindOption,
	pOpt *model.PermOption) (projectExport export.ProjectExport, total int, err error) {
	var exportProjects []export.Project

	projQuery, total, err := d.baseQueryWithFilterOption(d.db, compID, findOpt, pOpt)
	if err != nil {
		return
	}

	projQuery = projQuery.Order("project.project_cd") // fix order

	subEmp := d.db.Session(&gorm.Session{}).Table(tbl.Employee{}.TableName()).
		Joins("INNER JOIN (?) AS p ON employee.employee_no = p.project_owner",
			projQuery).
		Where("employee.company_no = ?", compID).
		Group("employee.employee_no").
		Select([]string{
			"employee_no",
			"member_code",
			"family_name",
			"first_name",
		})

	// Note: Add subquery to LEFT JOIN or Preload if it is too slow
	err = projQuery.Session(&gorm.Session{}).
		Joins("LEFT JOIN customer AS c ON "+
			"project.customer_no = c.customer_no AND c.company_no = ?", compID).
		Joins("LEFT JOIN business AS b ON "+
			"project.business_no = b.business_no AND b.company_no = ?", compID).
		Joins("LEFT JOIN department AS d ON "+
			"project.department_no = d.department_no AND d.company_no = ?", compID).

		// Use subquery to limit employee records
		Joins("LEFT JOIN (?) AS e ON project.project_owner = e.employee_no", subEmp).
		Select([]string{
			"project.project_cd",
			"project.project_name",
			"DATE_FORMAT(project.start_date, '%Y-%m-%d') AS start_date",
			"DATE_FORMAT(project.end_date, '%Y-%m-%d') AS end_date",
			"project.task_flag",
			"project.notes",
			"project.active_flag",
			"c.management_code AS customer_code",
			"c.customer_name",
			"b.management_code AS business_code",
			"b.business_name",
			"d.management_code AS department_code",
			"d.department_name",
			"e.member_code AS project_owner_code",
			"CONCAT(e.family_name,' ',e.first_name) AS project_owner_name",

			// need to fetch for preload
			"project.project_no",
		}).
		Preload("Fields", func(db *gorm.DB) *gorm.DB {
			return db.Table("t_project_field").
				Joins("INNER JOIN m_project_field ON m_project_field.field_no = t_project_field.field_no").
				Select([]string{
					"t_project_field.project_no",
					"m_project_field.field_name",
					"t_project_field.field_value",
				})
		}).
		Find(&exportProjects).Error

	if err != nil {
		return
	}

	// use m_project_field dao to get project fields
	mField := mProjectField{db: d.db}
	fieldFOpt := &model.FindOption{
		OrderBy: "order_no",
	}

	fields, _, err := mField.Find(compID, fieldFOpt)
	if err != nil {
		return
	}

	projectExport = export.NewProjectExport(exportProjects,
		fields)

	return
}

func (d *project) FindSpecific(compNo, id int, withOpt *model.WithOption) (project tbl.Project, err error) {
	err = d.db.Model(&project).
		Scopes(d.withPreload(withOpt)).
		Where("project_no = ?", id).
		Where("company_no = ?", compNo).
		First(&project).Error

	return
}

func (d *project) FindOneByMgmtCode(compID int, mgmtCode string) (project tbl.Project, err error) {
	err = d.db.Model(&project).
		Where("project_cd = ?", mgmtCode).
		Where("company_no = ?", compID).
		First(&project).Error

	return
}

func (d *project) FindByPermissions(compNo int, fOpt *model.FindOption,
	pOpt *model.MultiPermOption) (projects []tbl.Project, total int, err error) {
	base := d.db.Model(&projects).Where("company_no = ?", compNo)

	// filter by permission
	if pOpt != nil {
		base = base.Scopes(
			idb.ProjectByMultiUserPermission(compNo, pOpt.UserID, pOpt.DepartmentNo,
				pOpt.Access, pOpt.PermTypes),
		)
	}

	total, err = d.findWithFindOption(base, fOpt, tbl.Project{}, &projects)
	return
}

func (d *project) FindByProcGrpID(compID, procGrpID int) (projects []tbl.Project, err error) {
	err = d.db.Model(&projects).
		Where("company_no = ?", compID).
		Where("process_group_no = ?", procGrpID).
		Find(&projects).Error
	return
}

func (d *project) FindByTaskProcGrpID(compID, taskProcGrpID int) (projects []tbl.Project, err error) {
	err = d.db.Model(&projects).
		Where("company_no = ?", compID).
		Where("task_process_group_no = ?", taskProcGrpID).
		Find(&projects).Error
	return
}

func (d *project) Create(data *tbl.Project) (err error) {
	now := time.Now()
	data.CreateTime = now
	data.UpdateTime = now

	err = d.db.Create(data).Error
	return
}

func (d *project) Update(data *tbl.Project) (err error) {
	data.UpdateTime = dateutil.Now()

	mapData := util.Map(data).
		MakeNoZero()

	err = d.db.Model(&tbl.Project{}).
		Where("project_no = ?", data.ProjectNo).
		Where("company_no = ?", data.CompanyNo).
		UpdateColumns(mapData).
		First(data).Error

	return
}

func (d *project) Delete(compNo, id int) (err error) {
	err = d.db.Where("project_no = ?", id).
		Where("company_no = ?", compNo).
		Delete(tbl.Project{}).Error

	return
}

func (d *project) Exist(compNo, id int) (res bool) {
	_, err := d.FindSpecific(compNo, id, nil)

	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return false
		}
		panic(err.Error())
	}

	return true
}

func (d *project) ExistByMgmtCodeExcept(compNo int, mgmntCode *string, exceptID int) (res bool) {
	var project tbl.Project
	db := d.db.Where("company_no = ?", compNo).
		Where("project_cd = ?", mgmntCode)

	if exceptID != 0 {
		db = db.Not("project_no", exceptID)
	}

	return d.exist(db, &project)
}

func (d *project) ExistByMgmtCode(compNo int, mgmntCode *string) (res bool) {
	return d.ExistByMgmtCodeExcept(compNo, mgmntCode, 0)
}

func (d *project) ExistByProcessGroupID(compID, prcGrpID int) bool {
	var projs tbl.Project
	db := d.db.Where("company_no = ?", compID).
		Where("process_group_no = ? OR task_process_group_no = ?",
			prcGrpID, prcGrpID)

	return d.exist(db, &projs)
}

func (d *project) InUseDepartment(compNo, id int) (res bool) {
	var proj tbl.Project
	db := d.db.Where("department_no = ?", id).
		Where("company_no = ?", compNo)

	return d.exist(db, &proj)
}

func (d *project) CountByIds(compNo int, ids []int) (total int) {
	var ttl int64
	err := d.db.Table("project").
		Where("company_no = ?", compNo).
		Where("project_no IN (?)", ids).Count(&ttl).Error

	if err != nil {
		panic(err.Error())
	}

	total = int(ttl)
	return
}

func (d *project) CountByProjOwners(compNo int, projOwners []int) (total int) {
	var ttl int64
	err := d.db.Table("project").
		Where("company_no = ?", compNo).
		Where("project_owner IN (?)", projOwners).Count(&ttl).Error

	if err != nil {
		panic(err.Error())
	}

	total = int(ttl)
	return
}

func (d *project) withPreload(with *model.WithOption) func(db *gorm.DB) *gorm.DB {
	return func(db *gorm.DB) *gorm.DB {
		if with == nil {
			return db
		}

		if with.Contains("members") {
			db = db.Preload("ProjectEmployees.Employee")
		}
		if with.Contains("customer") {
			db = db.Preload("Customer")
		}
		if with.Contains("business") {
			db = db.Preload("Business")
		}
		if with.Contains("department") {
			db = db.Preload("Department")
		}

		return db
	}
}

func (d *project) filterByFields(findOpt *model.FindOption) func(db *gorm.DB) *gorm.DB {
	return func(db *gorm.DB) *gorm.DB {
		if findOpt == nil || findOpt.Filters["fields"] == nil {
			return db
		}

		var (
			query string
			args  []interface{}
			flds  = make(map[string][]string, 0)
		)

		fields := findOpt.Filters["fields"]
		for _, field := range fields {
			fld := strings.SplitN(field, ":", 2)
			if len(fld) < 2 {
				continue
			}

			key, val := fld[0], fld[1]
			flds[key] = append(flds[key], val)
		}

		for key, vals := range flds {
			args = append(args, key)
			query += "(field_no = ? AND "

			fldValQuery := "("
			for _, val := range vals {
				args = append(args, "%"+val+"%")
				fldValQuery += "field_value LIKE ? OR "
			}
			query += strings.TrimSuffix(fldValQuery, " OR ") + ")) OR "
		}

		if query == "" {
			return db
		}

		fieldsQuery := db.Session(&gorm.Session{NewDB: true}).Table(tbl.TProjectField{}.TableName()).
			Where(strings.TrimSuffix(query, " OR "), args...).
			Select("project_no").
			Order("project_no")

		return db.Where("project_no IN (?)", fieldsQuery)
	}
}

func (d *project) filterByUserIDs(compNo int, base *gorm.DB,
	userIDs []string) *gorm.DB {

	pjEmpSubQuery := base.Session(&gorm.Session{NewDB: true}).Table("project_employee AS pe").
		Joins("INNER JOIN project AS p ON p.project_no = pe.project_no").
		Where("pe.employee_no IN (?)", userIDs).
		Where("p.company_no = ?", compNo).
		Select("pe.project_no")

	pjDeptSubQuery := base.Session(&gorm.Session{NewDB: true}).Table("project_department AS pd").
		Joins("INNER JOIN employee AS e"+
			" ON e.department_no = pd.department_no").
		Joins("INNER JOIN project AS p ON p.project_no = pd.project_no").
		Where("e.employee_no IN (?)", userIDs).
		Where("e.company_no = ?", compNo).
		Where("pd.company_no = ?", compNo).
		Where("p.company_no = ?", compNo).
		Select("pd.project_no")

	base = base.Where("project_no IN (?) OR project_no IN (?)", pjEmpSubQuery, pjDeptSubQuery)
	return base
}

// baseQueryWithFilterOption - project base query
// * With Filter Option
//   - FindOption and PermOption
//
// * return
//   - baseQuery: gorm.DB
//   - count, and error
func (d *project) baseQueryWithFilterOption(db *gorm.DB, compNo int,
	findOpt *model.FindOption, pOpt *model.PermOption) (base *gorm.DB, count int, err error) {
	t := &tbl.Project{}
	base = db.Session(&gorm.Session{NewDB: true}).Table(t.TableName()).
		Scopes(d.filterByFields(findOpt)).
		Where("project.company_no = ?", compNo).
		Order("project_no")

	// filter by project employee(member/department)
	if findOpt != nil && findOpt.Filters["user_ids"] != nil {
		base = d.filterByUserIDs(compNo, base, findOpt.Filters["user_ids"])
	}

	// filter by permission
	if pOpt != nil {
		base = base.Scopes(
			idb.ProjectByUserPermission(compNo, pOpt.UserID, pOpt.DepartmentNo,
				pOpt.Access, pOpt.PermType),
		)
	}
	// filter by find option(filter and order) and return
	return d.filterByFindOption(base, findOpt, t)
}
