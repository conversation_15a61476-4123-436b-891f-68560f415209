package dao

import (
	"gitlab.com/innopm/deneb/domain/model/tbl"
	"gitlab.com/innopm/deneb/domain/repo"
	"gorm.io/gorm"
)

func NewEmpDeptHistoryRepo(db *gorm.DB) repo.EmpDeptHistory {
	return &empDeptHistory{
		db: db,
	}
}

type empDeptHistory struct {
	basedao
	db *gorm.DB
}

func (d *empDeptHistory) InUseDepartment(compNo, id int) (res bool) {
	var deptHistory tbl.EmployeeDepartmentHistory
	db := d.db.Where("department_no = ?", id).
		Where("company_no = ?", compNo)

	return d.exist(db, &deptHistory)
}

// buildEmpDeptHistoryLogQuery build employee department history log
// * add current department of employee
// * add start and end date of employee department history
// * SELECT
// - company_no
// - employee_no
// - department_no (inlcudes current department of employee)
// - department_start_date (employees or departments start_date)
//   - employee start_date: first department of employee(1000-01-01 If NULL)
//   - departments start date: second to current department of employee
//
// - department_end_date (9999-12-31 or department's end date)
func buildEmpDeptHistoryLogQuery(db *gorm.DB, compID int) *gorm.DB {
	// base employee department history
	baseEmpDeptQuery := db.Session(&gorm.Session{NewDB: true}).
		Table("employee_department_history edh0").
		Where("edh0.company_no = ?", compID)

	// employee's last department; used for current department start date
	empLastDeptJoinSubQuery := baseEmpDeptQuery.Session(&gorm.Session{}).
		Group("edh0.employee_no").
		Select([]string{
			"edh0.employee_no",
			"MAX(edh0.end_date) end_date",
		})

	// employee's previous department of each department history;
	// used for start date of its next department and lateral left join
	empPrevDeptLatJoinSubQuery := baseEmpDeptQuery.Session(&gorm.Session{}).
		Where("edh0.end_date < edh.end_date").
		Order("edh0.end_date DESC").Limit(1).
		Select([]string{
			"edh0.employee_no",
			"edh0.end_date",
		})

	// employee's current department; get start date
	// Only include current department if it's different from the last historical department
	// or if there's no department history at all
	empCurDeptSubQuery := db.Session(&gorm.Session{NewDB: true}).Table("employee e").
		Where("company_no = ?", compID).
		Joins("LEFT JOIN (?) edh ON edh.employee_no = e.employee_no",
			empLastDeptJoinSubQuery).
		// Only include current department if employee has no department history
		// OR if current department is different from the last historical department
		Where("edh.employee_no IS NULL OR NOT EXISTS (" +
			"SELECT 1 FROM employee_department_history edh2 " +
			"WHERE edh2.employee_no = e.employee_no AND edh2.company_no = e.company_no " +
			"AND edh2.department_no = e.department_no " +
			"AND edh2.end_date = (SELECT MAX(end_date) FROM employee_department_history edh3 " +
			"WHERE edh3.employee_no = e.employee_no AND edh3.company_no = e.company_no))").
		Select([]string{
			"e.company_no",
			"e.employee_no",
			"e.department_no",
			"CAST(IFNULL(" +
				" ADDDATE( edh.end_date, INTERVAL 1 DAY )," +
				// employee.start_date accepts NULL in table definition, but required in app.
				// Therefore, set start_date to `1000-01-01` just in case.
				" IFNULL(e.start_date, '1000-01-01')" +
				" ) AS DATE) department_start_date",
			"CAST('9999-12-31' AS DATE) department_end_date",
		})

	// employee department history; get start date
	empDeptHistSubQuery := db.Session(&gorm.Session{NewDB: true}).
		Table("employee_department_history edh").
		Where("edh.company_no = ?", compID).
		Joins("LEFT JOIN LATERAL (?) edh1"+
			" ON edh1.employee_no = edh.employee_no",
			empPrevDeptLatJoinSubQuery).
		Joins("LEFT JOIN employee e" +
			" ON e.employee_no = edh.employee_no").
		Select([]string{
			"edh.company_no",
			"edh.employee_no",
			"edh.department_no",
			"CAST(IFNULL(" +
				" ADDDATE( edh1.end_date, INTERVAL 1 DAY )," +
				// employee.start_date accepts NULL in table definition, but required in app.
				// Therefore, set start_date to `1000-01-01` just in case.
				" IFNULL(e.start_date, '1000-01-01')" +
				" ) AS DATE) department_start_date",
			"edh.end_date department_end_date",
		})

	// join/union employee current and history department
	return db.Raw("(?) UNION (?) ORDER BY employee_no, department_start_date", empCurDeptSubQuery, empDeptHistSubQuery)
}

func (d *empDeptHistory) buildRecEmpDeptHist(compID int) *gorm.DB {
	// base employee department history
	baseEmpDeptQuery := d.db.Session(&gorm.Session{NewDB: true}).
		Table("employee_department_history edh0").
		Where("edh0.company_no = ?", compID)

	// employee's last department; used for current department start date
	empLastDeptJoinSubQuery := baseEmpDeptQuery.Session(&gorm.Session{}).
		Group("edh0.employee_no").
		Select([]string{
			"edh0.employee_no",
			"MAX(edh0.end_date) end_date",
		})

	// employee's previous department of each department history;
	// used for start date of its next department and lateral left join
	empPrevDeptLatJoinSubQuery := baseEmpDeptQuery.Session(&gorm.Session{}).
		Where("edh0.end_date < edh.end_date").
		Order("edh0.end_date DESC").Limit(1).
		Select([]string{
			"edh0.employee_no",
			"edh0.end_date",
		})

	// employee's current department; get start date
	// Only include current department if it's different from the last historical department
	// or if there's no department history at all
	empCurDeptSubQuery := d.db.Session(&gorm.Session{NewDB: true}).Table("employee e").
		Where("company_no = ?", compID).
		Joins("LEFT JOIN (?) edh ON edh.employee_no = e.employee_no",
			empLastDeptJoinSubQuery).
		// Only include current department if employee has no department history
		// OR if current department is different from the last historical department
		Where("edh.employee_no IS NULL OR NOT EXISTS (" +
			"SELECT 1 FROM employee_department_history edh2 " +
			"WHERE edh2.employee_no = e.employee_no AND edh2.company_no = e.company_no " +
			"AND edh2.department_no = e.department_no " +
			"AND edh2.end_date = (SELECT MAX(end_date) FROM employee_department_history edh3 " +
			"WHERE edh3.employee_no = e.employee_no AND edh3.company_no = e.company_no))").
		Select([]string{
			"e.company_no",
			"e.employee_no",
			"e.department_no",
			"CAST(IFNULL(" +
				" ADDDATE( edh.end_date, INTERVAL 1 DAY )," +
				// employee.start_date accepts NULL in table definition, but required in app.
				// Therefore, set start_date to `1000-01-01` just in case.
				" IFNULL(e.start_date, '1000-01-01')" +
				" ) AS DATE) department_start_date",
			"CAST('9999-12-31' AS DATE) department_end_date",
		})

	// employee department history; get start date
	empDeptHistSubQuery := d.db.Session(&gorm.Session{NewDB: true}).
		Table("employee_department_history edh").
		Where("edh.company_no = ?", compID).
		Joins("LEFT JOIN LATERAL (?) edh1"+
			" ON edh1.employee_no = edh.employee_no",
			empPrevDeptLatJoinSubQuery).
		Joins("LEFT JOIN employee e" +
			" ON e.employee_no = edh.employee_no").
		Select([]string{
			"edh.company_no",
			"edh.employee_no",
			"edh.department_no",
			"CAST(IFNULL(" +
				" ADDDATE( edh1.end_date, INTERVAL 1 DAY )," +
				// employee.start_date accepts NULL in table definition, but required in app.
				// Therefore, set start_date to `1000-01-01` just in case.
				" IFNULL(e.start_date, '1000-01-01')" +
				" ) AS DATE) department_start_date",
			"edh.end_date department_end_date",
		})

	// join/union employee current and history department
	return d.db.Raw("(?) UNION (?)", empCurDeptSubQuery, empDeptHistSubQuery)
}

func (d *empDeptHistory) buildCTEEmpDeptHist() *gorm.DB {
	return d.db.
		Session(&gorm.Session{NewDB: true}).
		Table("rec_emp_dept_hist").
		Order([]string{"employee_no", "department_start_date"}).
		Select("*")

}
