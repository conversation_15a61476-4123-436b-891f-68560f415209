word:
  admin: admin
  attendance: 考勤
  attendance_integration: 时间和考勤的联系
  afternoon_day_off: 下午休息日
  attendance_difference_reason: 分歧原因
  attendance_work_match: 考勤匹配
  budget: 预算
  business: business
  category: category
  company_process_input_is_0: company process input is 0
  company_process_input_is_lt_2: company process input is less than 2
  company_process_input_is_lt_3: company process input is less than 3
  company_work_unit: company work unit
  contract: contract
  date: date
  datetime: datetime
  department: department
  department_history: department history
  difference: 差分
  dimension: Dimension
  hierarchy:  階層
  holiday_type: 假期类型
  jobs: jobs
  kot: KING OF TIME
  kinkakuji: Kinkakuji
  man_day: 每人每日
  man_hour: 每人每时
  man_minute: 每人每分
  man_month: 每人每月
  morning_day_off: 早上放假
  notification: notification
  parent_department: parent department
  pclog: 电脑日志
  process: process
  process_caterogy: process category
  process_use_count_not_used: process use count is not used
  process_group: process group
  project: project
  project_department: project department
  project_gantt: project gantt
  project_info: project info
  project_process: project process
  project_process_or_group: project (process, or process group)
  project_range: project range
  project_work: project work
  result: 实绩
  role_permission: role permission
  task: task
  tasks: tasks
  timesheet: timesheet
  timesheet_authorizer: timesheet authorizer
  timesheet_copy: timesheet copy
  timesheet_date: timesheet date
  timesheet_period_notified: timesheet period to be notified
  title: title
  unassigned_task: 未分配的任务
  unit_price_start_date: start date of unit price
  user: user
  user_period: user's period
  user_work_day: user's work day
  whole_number: whole number
  work: 工时
  work_date: work date
  day_off: 全天休息
  work_time_attendance_unmatch: Unmatch between work time and attendance
  project_str_none: (无)

msg:
  not_found: Not found
  notif:
    smp:
      subject: 测试通知
      body: 这是一条测试信息
    tsl:
      subject: 时间表输入状态列表

err_msg:
  already_exist: "%s already exist"
  attendance_not_exist: "Attendance not exist"
  attendance_time_exist: "Attendance time already exist"
  auth_failed: Authentication failed
  canceled: "Request canceled"
  cannot_be_set: "%s cannot be set in %s"
  cannot_be_set_at_same_time: "%s and %s cannot be set at the same time"
  cannot_be_set_at_same_time_when: "%s and %s cannot be set at the same time when %s is specified"
  cannot_be_set_when: "%s cannot be set in %s when %s"
  cannot_be_updated: "The value %s cannot be updated"
  cannot_deactivate_status_when_in_use: cannot deactivate status when in use by %s
  cannot_execute_in_a_row: "cannot execute in a row as long as it's not been %d min since the last execution."
  cannot_stamp_when_not_rest: "Cannot stamp end when not during rest"
  cannot_stamp_when_rest: "Cannot stamp %s when during rest"
  cannot_update_category_process: cannot update category process
  cannot_use_calendar_sync: "External calendar setting is set to 'None'"
  circulation_hierarchies: "%s hierarchies are circulating"
  company_ai_work_report_summary_disabled: "Not available since 'Use AI Work Report Summary' is disabled in company settings"
  company_am_use_of_attendance_must_not_use: &attendance_must_not_use "Not available if Attendance Management 'Use of Attendance' is used in company settings"
  company_gantt_chart_not_used: "Creating a gantt project is not allowed if 'Gantt Chart' is not being used in company settings"
  company_process_disabled: "Not enable if 'Use of Process Input' is disabled in company settings"
  company_ts_use_of_attendance_must_use: "Not available if Timesheet 'Use of Attendance' is not used in company settings"
  company_ts_use_of_approval_must_use: "Not available if Timesheet 'Use of Approval' is not used in company settings"
  contains_invalid_value: "%s contains an invalid value"
  dates_gte: "%s and %s must be greater than or equal to %s"
  date_is_before_closing_date: "'%s' is before closing date"
  difference_in_timesheet_settings: there is a difference between request data and company settings
  duplicate: The value is Duplicate (%v)
  expired_oauth_code: "Cannot exchange token due to auth code was expired"
  favorite_custom_work_report_exist: "Favorite custom work report already exist"
  file_format_not_supported: "%s not supported"
  forbidden: &forbidden "forbidden: %s"
  gte: "%s must be greater than or equal to %s"
  in_use: Data is in use
  in_use_detail: "Data is in use by '%s'"
  integ_setting_not_found: "Integration setting is not found"
  integ_system_is_unavailable: 「%s」在「%s」之间不可用
  invalid_access_token: "Invalid Access Token"
  invalid_date_format: "Invalid date format %s"
  invalid_date_range: "The date range between %s and %s is incorrect (within %d days)"
  invalid_format: "Invalid %s format"
  invalid_format_in_one_of_dates: "Invalid date format in %s or %s"
  invalid_input: Invalid input
  invalid_jobcan_api_client_id_or_secret: &invalid_jobcan_api_client_id_or_secret "无效的 Jobcan API 客户端 ID 或密钥"
  invalid_hrmos_company_key_or_secret: &invalid_hrmos_company_key_or_secret "无效的 HRMOS 公司名称或秘密"
  invalid_start_day: "Invalid day end time: %v"
  invalid_target_system: Invalid target system
  invalid_value: "Value of %v is invalid"
  lt: "%s must be less than to %s"
  lte: "%s must be less than or equal to %s"
  max: "%s and %s must be %d or less"
  max_records_reached: "Number of max %s records (%d) has been reached"
  must_be_between: "%s must be between %d and %d"
  must_be_assign_to: "%s must be assign to %s"
  must_be_one_of: "%s must be one of %v"
  must_be_set_to_when: "%s must be set to %s when %s"
  must_be_set_when: "%s must be set when %s"
  must_be_within: "%s must be within %s"
  must_be_within_when_copy_timesheet: "Nothing to copy due to %s must be within %s"
  must_follow_pw_restr: "Password must follow password string restriction"
  must_belong_to: "%s must belong to %s"
  must_not_be: "%s must not be %s"
  must_not_set_when: "%s must not set when %s"
  must_not_set_when_use_day_off_is_not_use: "%s must not set when 'Use of Day Off' is not use"
  must_overlap: "%s must overlap with %s"
  must_same: "The %s must be same as %s"
  no_difference: "%v 和 %v 之间没有区别"
  no_resource: No Resource
  not_available: "This %s is unavailable"
  not_child: "'%s' must not be value of its child/ren"
  not_divisible: "the value of '%s' is not divisible by '%s'"
  not_exist: "the value of '%s' does not exist"
  not_same_as: "'%s' must not be the same as '%s'"
  notif_failed: "Sending notification failed"
  number_of_integration_settings_exceeds_limit: "Number of integration settings exceeds limit（%v）"
  oauth_token_exchange_failed: "OAuth token exchange failed"
  only_one_field_must_have_value: "only one of %s and %s must have a value"
  only_one_of_must_have_value: "only one of %s must have value"
  other_integration_is_running: "other integration is running"
  other_work_report_sync_is_running: "work reoprt sync is already in progress"
  password_invalid_format: "password invalid format"
  process_duplicated_combination: "Duplicated combination in 'processes' found"
  process_not_match: "'processes' does not match 'Use of Process Input' settings"
  process_set_as_category: "this process is set as a category"
  process_type_using_group: "this project is using process group"
  process_type_not_use_group: "this project is not using process group"
  project_type_not_process: "this project is not process project"
  request_failed: Request failed
  required: "%s is required"
  required_two_and_or: "%s and/or %s are/is required"
  required_at_least_one: "At least one is must be %s in [%s]"
  required_both: "%s and %s must be required"
  required_project_master_field_exist: "Creating/updating project is not allowed if an existing 'Project Master Field' is required"
  request_exceeds_ai_usage_limit: "Request exceeds AI usage limit (Total usage: %d, Pending usage: %d, Limit: %d)"
  report_type_invalid_format: "report type invalid format"
  server_error: internal server error
  set_value_at: "'%s' must have a value to set '%s'"
  some_not_exist: "Some values of '%s' does not exist"
  some_values_of_are_inactive: "Some values of '%s' are inactive:%v"
  some_values_of_do_not_exist: "Some values of '%s' do not exist:%v"
  some_values_of_are_process_category: "Some values of '%s' are process category:%v"
  timesheet_future_disabled: "Timesheet future registration is disabled"
  timesheet_is_closed: "Timesheet date is earlier or on closing date"
  timesheet_export_category_not_supported: "%s not supported"
  timesheet_no_approver: "尚未设置批准人"
  timesheet_not_use_approval: "时间表审批功能尚未启用"
  timesheet_not_exists_to_apply: "无法申请审批不存在的时间表"
  timesheet_not_exists_to_unapply: "可取消申请的待审批时间表不存在"
  timesheet_not_editable_to_closed_date: "Cannot edit a date-closed timesheet"
  timesheet_not_editable_out_of_user_period: "Cannot edit a timesheet whose date is out of the employee period"
  value_of_is_inactive: "The value of '%s' is inactive"
  work_and_attendance_must_be_matched: "Work and attendance must be matched"
  workflow_not_editable: "Timesheet workflow status is pending, or approved"
  work_report_max_dimensions: "Number of dimensions may not exceed %d."
  work_report_data_empty: "Work Report Data is empty."
  work_budget_exceed: "Work for one month exceed %v %v"
  work_budget_out_of_project_period: "Cannot be changed due to outside the project period"
  work_ignored_as_copy: "Some works outside the project period will not be copied"
  pattern_exist: "这个收藏已注册。"
  sw_status_conflict: "秒表状态不正确。"
  sw_limit_max_count: "最多只能有%d个秒表。"
  sw_invalid_start_end_time: "无效的计时开始或结束时间。"
  sw_exists_another_with_timing: "有其他秒表正在计时，无法开始。"
  sw_time_duplicated_apply: "秒表计时结果「%s~%s」已计入时间表。"
  task_group_exceed_nested_limit: “任务父级超过最大嵌套限制：[%s]”
  target_task_must_be_in_request: "目标任务必须包含在请求正文中。 [任务 taskId: %s, in: %s, ope: %s]"

perm_err_msg:
  forbidden: *forbidden
  insufficient_perm_to_update: "insufficient permission to update %s"
  insufficient_perm_to_copy: "insufficient permission to copy %s"
  insufficient_perm_to_use_the_value: "insufficient permission to use the value of %s"
  insufficient_pj_perm_or_auth: insufficient project permission or authorization
  insufficient_system_admin: insufficient system admin permission
  insufficient_user_pj_auth_ts_input: insufficient user project authorization (timesheet input)
  insufficient_x_in_perm_or_pj_auth: insufficient %s settings in the permission master or project authorization
  insufficient_x_perm: insufficient %s permission
  must_not_use_of_att_mng: *attendance_must_not_use
  must_use_ts_att_and_not_att_mng: "available only when 'Use of Attendance' of Timesheet is used and Attendance Management 'Use of Attendance' is not used in company settings"

integration_err_msg:
  api_access_error: "API访问错误"
  api_call_limit_exceeded: "超出 API 调用限制"
  attendance_synchronized_by_another_condition_existed: "在其他条件下已经同步出席的情况也存在"
  attendance_modified: "出勤率发生了变化"
  cannot_convert_attendance: "出勤率无法转换"
  cannot_get_access_token: "无法获取访问令牌"
  cannot_get_crowdlog_employee: "获取 CrowdLog 成员失败"
  cannot_get_employee: "有一位合伙人员工无法获得"
  cannot_save_attendance: "保存考勤失败"
  employee_not_found: "没有员工"
  invalid_access_token: "无效的访问令牌"
  integ_setting_not_found: "链接设置不存在"
  invalid_jobcan_api_client_id_or_secret: *invalid_jobcan_api_client_id_or_secret
  no_attendance: "出席不存在"
  other_integration_is_running: "另一个集成正在运行"
  integration_is_already_running: "集成已在运行"
  server_error: "内部服务器错误"
  target_end_date_is_before_closing_date: "目标结束日期在截止日期之前指定"
  target_start_date_is_changed: "符合条件的开始日期已更改"
  unable_to_start_integration: "无法开始集成"
  unknown_target_system: "未知的整合目标系统"

# export_format.name must be same to config.ExportName
# export_format.name.key must be same to model/export/xxx.go > struct tag key
export_format:
  contract:
    management_code: { name: management_code }
    employ_type_name: { name: employ_type_name }
    order_no: { name: order_no }
    active_flag: { name: active_flag }
  gantt_chart:
    project_code: { name: 项目代码 }
    project_name: { name: 项目名称 }
    task_type: { name: 种类 }
    task_code: { name: 任务代码 }
    task_name: { name: 任务名称 }
    task_group_code: { name: 任务组代码 }
    task_group_name: { name: 任务组名称 }
    start_date: { name: 开始日期 }
    end_date: { name: 结束日期 }
    work_first: { name: 工时开始日期 }
    work_last: { name: 工时结束日期 }
    work_budget: { name: 工时预算 }
    work_result: { name: 工时实绩 }
    progress: { name: 进度 }
    assignee_member_code: { name: 负责人员工代码 }
    assignee_name: { name: 负责人 }
    transl_mgmt_code@@: { name: "%s:管理代码" } # use as <process>:管理代码
    transl_name@@: { name: "%s:名称" } # use as <process>:名称
    upstream_task_code: { name: 上层任务代码 }
    upstream_task_name: { name: 上级链接 }
    status: { name: 状态 }
    comments: { name: 备注 }
  project:
    project_code: { name: 项目代码 }
    project_name: { name: 项目名称 }
    customer_code: { name: 客户管理代码 }
    customer_name: { name: 客户名称 }
    business_code: { name: 业务管理代码 }
    business_name: { name: 业务名称 }
    department_code: { name: 部门管理代码 }
    department_name: { name: 部门名称 }
    project_owner_code: { name: 项目负责人员工代码 }
    project_owner_name: { name: 项目责任人名称 }
    start_date: { name: 开始日期 }
    end_date: { name: 结束日期 }
    ganttchart: { name: 甘特图 }
    active_flag: { name: 活跃 }
    comments: { name: 备注 }
  project_department:
    project_code: { name: 项目代码 }
    project_name: { name: 项目名称 }
    department_code: { name: 部门代码 }
    department_name: { name: 部门名称 }
    active_flag: { name: 活跃 }
    timesheet_input: { name: 权限:时间表输入 }
    auth_basic: { name: 权限:基本设置 }
    auth_accounting: { name: 权限:盈亏 }
    auth_work: { name: 权限:工时 }
  project_member:
    project_code: { name: 项目代码 }
    project_name: { name: 项目名称 }
    member_code: { name: 员工代码 }
    member_name: { name: 员工名称 }
    timesheet_input: { name: 权限:时间表输入 }
    auth_basic: { name: 权限:基本设置 }
    auth_accounting: { name: 权限:盈亏 }
    auth_work: { name: 权限:工时 }
  project_process:
    project_code: { name: 项目代码 }
    project_name: { name: 项目名称 }
    process_code: { name: 进程管理代码 }
    process_name: { name: 进程名称 }
    level: { name: 层次 }
    active_flag: { name: 活跃 }
  project_process_group:
    project_code: { name: 项目代码 }
    project_name: { name: 项目名称 }
    process_group_code: { name: 进程管理代码 }
    process_group_name: { name: 进程名称 }
  project_task_process:
    project_code: { name: 项目代码 }
    project_name: { name: 项目名称 }
    process_code: { name: 进程管理代码 }
    process_name: { name: 进程名称 }
    level: { name: 层次 }
    active_flag: { name: 活跃 }
  project_task_process_group:
    project_code: { name: 项目代码 }
    project_name: { name: 项目名称 }
    process_group_code: { name: 进程管理代码 }
    process_group_name: { name: 进程名称 }
  work_budget:
    project_code: { name: 项目代码 }
    project_name: { name: 项目名称 }
    member_code: { name: 员工代码 }
    member_name: { name: 员工名称 }
    date: { name: 日期 }
    work_budget: { name: 工时预算 }
    task_work_budget: { name: 任务工时预算 }
    total_work_budget: { name: 总工时预算 }
    work_budget_min: { name: 工时预算（分钟） }
    task_work_budget_min: { name: 任务工时预算（分钟） }
    total_work_budget_min: { name: 总工时预算（分钟） }
  work_report:
    project_code: { name: 项目代码 }
    project_name: { name: 项目名称 }
    project_business_code: { name: 业务管理代码 }
    project_business_name: { name: 业务名称 }
    transl_proj_busi_code_lvl@@: { name: 业务管理代码 (%s等级制度) }
    transl_proj_busi_name_lvl@@: { name: 业务名称 (%s等级制度) }
    project_customer_code: { name: 客户管理代码 }
    project_customer_name: { name: 客户名称 }
    project_department_code: { name: 项目部门管理代码 }
    project_department_name: { name: 项目部门名称 }
    transl_proj_dept_code_lvl@@: { name: 项目部门管理代码 (%s等级制度) }
    transl_proj_dept_name_lvl@@: { name: 项目部门名称 (%s等级制度) }
    project_owner_code: { name: 项目负责人员工代码 }
    project_owner_name: { name: 项目责任人名称 }
    member_code: { name: 员工代码 }
    member_name: { name: 员工名称 }
    member_employ_type_code: { name: 雇用类型管理代码 }
    member_employ_type_name: { name: 雇用类型名称 }
    member_customer_code: { name: 公司所属管理代码 }
    member_customer_name: { name: 公司所属 }
    member_department_code: { name: 员工部门管理代码 }
    member_department_name: { name: 员工部门名称 }
    transl_mem_dept_code_lvl@@: { name: 员工部门管理代码 (%s等级制度) }
    transl_mem_dept_name_lvl@@: { name: 员工部门名称 (%s等级制度) }
    member_title_code: { name: 职务管理代码 }
    member_title_name: { name: 职务名称 }
    transl_mgmt_code@@: { name: "%s:管理代码" } # use as <process>:management_code
    transl_name@@: { name: "%s:名称" } # use as <process>:name
    type: {name: 类型}
    work_unit: { name: 工时输出单元 }
    total: { name: 合计 }
  timesheet:
    member_code: { name: 成员代码 }
    member_name: { name: 成员名 }
    email: { name: 电子邮件 }
    title_name: { name: 位置 }
    contract_name: { name: 就业 }
    customer_name: { name: 所属公司 }
    department_code: { name: 部门代码 }
    department_name: { name: 部门 }
    type: { name: 类型 }
    total: { name: 全部的 }
  title:
    management_code: { name: 管理代码 }
    title_name: { name: 职务名称 }
    order_no: { name: 显示顺序 }
    active_flag: { name: 活跃 }
  business:
    management_code: { name: 管理代码 }
    business_name: { name: 业务名称 }
    parent_code: { name: 上层管理代码 }
    parent_name: { name: 上层业务名称 }
    comments: { name: 说明或描述 }
    order_no: { name: 显示顺序 }
    active_flag: { name: 活跃 }
  customer:
    management_code: { name: 管理代码 }
    customer_name: { name: 客户名称 }
    receive_flag: { name: 订单受理 }
    order_flag: { name: 订单 }
    zip: { name: 邮政编号 }
    address: { name: 住址 }
    tel: { name: 电话号码 }
    charge_name: { name: 客户负责人名 }
    member_code: { name: 负责人员工代码 }
    member_name: { name: 负责人员工名 }
    pay_closing_day: { name: 收款及付款方式:截止日期 }
    pay_sight_month: { name: 收款及付款方式:月 }
    pay_sight_day: { name: 收款及付款方式:日 }
    notes: { name: 备注 }
    active_flag: { name: 活跃 }
  attendance_difference:
    attendance_date: { name: 日期 }
    member_code: { name: 成员代码 }
    member_name: { name: 成员名 }
    email: { name: 电子邮件 }
    title_name: { name: 位置 }
    contract_name: { name: 就业 }
    customer_name: { name: 所属公司 }
    department_code: { name: 部门代码 }
    department_name: { name: 部门 }
    start_time: { name: 起始时间 }
    pclog_start_time: { name: PCLog起始时间 }
    start_time_diff: { name: 开始时间偏离时间 }
    start_time_diff_flag: { name: 开始时间偏差标志 }
    end_time: { name: 结束时间 }
    pclog_end_time: { name: PCLog结束时间 }
    end_time_diff: { name: 结束时间偏差时间 }
    end_time_diff_flag: { name: 结束时间偏差标志 }
    work_time: { name: 工作时间 }
    pclog_work_time: { name: PCLog工作时间 }
    work_time_diff: { name: 工作时间偏差时间 }
    idle_time: { name: 空闲时间 }
    reas_reason_class: { name: 分歧原因 }
    reas_reason: { name: 备注 }
    reas_time: { name: 差异原因登记日期和时间 }
    fixed_status: { name: 稳定的 }

notification:
  wir:
    mail:
      common:
        sender_name: "CrowdLog 支持服务台"
        receiver: "致 %s"
        notif_condition1: "・工作总时长与工时总数不符\n"
        notif_condition2: "・工时总数未满%.2f小时\n"
        notif_condition3: "▼通知条件\n%s%s"
        notif_period1: "2006-01-02"
        notif_period2: "▼所涉期间\n%s〜%s\n*不包括周六、周日和公共节假日。\n"
        notif_unsubscribe: "▼退订此邮件"
        disclaimer: "※此电子邮件地址仅供传送之用，恕不回复。"
        day_of_week: "周日,周一,周二,周三,周四,周五,周六"
      for_employee:
        title: "【CrowdLog】请完成工时的输入"
        main_message: "有些日期的工时输入尚未完成。\n请在时间表中完成工时输入。\n"
        call_to_action: "▼登录 CrowdLog\n\n%s\n"
      for_sysadmin:
        title: "【CrowdLog】发送工时输入遗漏的提醒通知【%s】"
        main_message: "通知名称: %s\n\n%d 名成员的工时输入尚未完成。\n已向尚未完成工时输入的成员发出通知。\n\n更多信息,请参阅 时间表报告。\n"
        call_to_action: "▼检查 时间表报告\n\n%s\n"
      for_employee_with_title:
        title: "【CrowdLog】成员的工时输入尚未完成"
        main_message: "已向尚未完成下列工时输入的成员发出通知。\n此电子邮件已发送给 %s。"
        recipients_title: "将被通知的成员包括。"
        recipients: "剩余%d天"
    title:
      member: 对个人的通知
      title_member: 对官员的摘要通知
      sysadmin: 对系统管理员的摘要通知
    word:
      configured_notification_name: 设置的通知名称

attendance_difference:
  reason_class:
    others: "其他（自由填写）"
    pc_used_not_for_work: "有业务用途以外的电脑使用"
    pc_unnecessarily: "有不需要使用电脑的业务（包括外出或出行等）"
    pc_log_unreadable: "电脑日志获取异常（网络故障或系统异常等）"
    pc_off_omission: "电脑未正确关闭（未关机或未锁屏等）"
