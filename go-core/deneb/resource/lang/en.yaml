word:
  admin: admin
  attendance: Attendance
  attendance_integration: attendance integration
  afternoon_day_off: Afternoon day off
  attendance_difference_reason: attendance difference reason
  attendance_work_match: Work-Attendance match
  budget: Budget
  business: business
  category: category
  company_process_input_is_0: company process input is 0
  company_process_input_is_lt_2: company process input is less than 2
  company_process_input_is_lt_3: company process input is less than 3
  company_work_unit: company work unit
  contract: contract
  date: date
  datetime: datetime
  department: department
  department_history: department history
  difference: Difference
  dimension: Dimension
  hierarchy: hierarchy
  holiday_type: Holiday type
  jobs: jobs
  kot: KING OF TIME
  kinkakuji: Kinkakuji
  man_day: Man-Day
  man_hour: Man-Hour
  man_minute: Man-Minute
  man_month: Man-Month
  morning_day_off: Morning day off
  notification: notification
  parent_department: parent department
  pclog: PCLog
  process: process
  process_caterogy: process category
  process_use_count_not_used: process use count is not used
  process_group: process group
  project: project
  project_department: project department
  project_gantt: project gantt
  project_info: project info
  project_process: project process
  project_process_or_group: project (process, or process group)
  project_range: project range
  project_work: project work
  result: Result
  role_permission: role permission
  task: task
  tasks: tasks
  timesheet: timesheet
  timesheet_authorizer: timesheet authorizer
  timesheet_copy: timesheet copy
  timesheet_date: timesheet date
  timesheet_period_notified: timesheet period to be notified
  title: title
  unassigned_task: Unassigned Task
  unit_price_start_date: start date of unit price
  user: user
  user_period: user's period
  user_work_day: user's work day
  whole_number: whole number
  work: Work
  work_date: work date
  work_time_attendance_unmatch: Unmatch between work time and attendance
  day_off: Day off
  project_str_none: (None)

msg:
  not_found: Not found
  notif:
    smp:
      subject: Test Notification
      body: This is a test message
    tsl:
      subject: Timesheet Input Status List

err_msg:
  already_exist: "%s already exist"
  attendance_not_exist: "Attendance not exist"
  attendance_time_exist: "Attendance time already exist"
  auth_failed: Authentication failed
  canceled: "Request canceled"
  cannot_be_set: "%s cannot be set in %s"
  cannot_be_set_at_same_time: "%s and %s cannot be set at the same time"
  cannot_be_set_at_same_time_when: "%s and %s cannot be set at the same time when %s is specified"
  cannot_be_set_when: "%s cannot be set in %s when %s"
  cannot_be_updated: "The value %s cannot be updated"
  cannot_deactivate_status_when_in_use: cannot deactivate status when in use by %s
  cannot_execute_in_a_row: cannot execute in a row as long as it's not been %d min since the last execution
  cannot_stamp_when_not_rest: "Cannot stamp end when not during rest"
  cannot_stamp_when_rest: "Cannot stamp %s when during rest"
  cannot_update_category_process: cannot update category process
  cannot_use_calendar_sync: "External calendar setting is set to 'None'"
  circulation_hierarchies: "%s hierarchies are circulating"
  company_ai_work_report_summary_disabled: "Not available since 'Use AI Work Report Summary' is disabled in company settings"
  company_am_use_of_attendance_must_not_use: &attendance_must_not_use "Not available if Attendance Management 'Use of Attendance' is used in company settings"
  company_gantt_chart_not_used: "Creating a gantt project is not allowed if 'Gantt Chart' is not being used in company settings"
  company_process_disabled: "Not enable if 'Use of Process Input' is disabled in company settings"
  company_ts_use_of_attendance_must_use: "Not available if Timesheet 'Use of Attendance' is not used in company settings"
  company_ts_use_of_approval_must_use: "Not available if Timesheet 'Use of Approval' is not used in company settings"
  contains_invalid_value: "%s contains an invalid value"
  dates_gte: "%s and %s must be greater than or equal to %s"
  date_is_before_closing_date: "'%s' is before closing date"
  difference_in_timesheet_settings: there is a difference between request data and company settings
  duplicate: The value is Duplicate (%v)
  expired_oauth_code: "Cannot exchange token due to auth code was expired"
  favorite_custom_work_report_exist: "Favorite custom work report already exist"
  file_format_not_supported: "%s not supported"
  forbidden: &forbidden "forbidden: %s"
  gte: "%s must be greater than or equal to %s"
  in_use: Data is in use
  in_use_detail: "Data is in use by '%s'"
  integ_setting_not_found: "Integration setting is not found"
  integ_system_is_unavailable: "「%s」is unavailable between 「%s」"
  invalid_access_token: "Invalid Access Token"
  invalid_date_format: "Invalid date format %s"
  invalid_date_range: "The date range between %s and %s is incorrect (within %d days)"
  invalid_format: "Invalid %s format"
  invalid_format_in_one_of_dates: "Invalid date format in %s or %s"
  invalid_input: Invalid input
  invalid_jobcan_api_client_id_or_secret: &invalid_jobcan_api_client_id_or_secret "Invalid Jobcan API Client ID or Secret"
  invalid_hrmos_company_key_or_secret: &invalid_hrmos_company_key_or_secret "Invalid HRMOS Company Key or Secret"
  invalid_start_day: "Invalid day end time: %v"
  invalid_target_system: Invalid target system
  invalid_value: "Value of %v is invalid"
  lt: "%s must be less than to %s"
  lte: "%s must be less than or equal to %s"
  max: "%s and %s must be %d or less"
  max_records_reached: "Number of max %s records (%d) has been reached"
  must_be_assign_to: "%s must be assign to %s"
  must_be_between: "%s must be between %d and %d"
  must_be_one_of: "%s must be one of %v"
  must_be_set_to_when: "%s must be set to %s when %s"
  must_be_set_when: "%s must be set when %s"
  must_be_within: "%s must be within %s"
  must_be_within_when_copy_timesheet: "Nothing to copy due to %s must be within %s"
  must_belong_to: "%s must belong to %s"
  must_follow_pw_restr: "Password must follow password string restriction"
  must_not_be: "%s must not be %s"
  must_not_set_when: "%s must not set when %s"
  must_not_set_when_use_day_off_is_not_use: "%s must not set when 'Use of Day Off' is not use"
  must_overlap: "%s must overlap with %s"
  must_same: "The %s must be same as %s"
  no_difference: "There is no differece between %v and %v"
  no_resource: No Resource
  not_available: "This %s is unavailable"
  not_child: "'%s' must not be value of its child/ren"
  not_divisible: "the value of '%s' is not divisible by '%s'"
  not_exist: "the value of '%s' does not exist"
  not_same_as: "'%s' must not be the same as '%s'"
  notif_failed: "Sending notification failed"
  number_of_integration_settings_exceeds_limit: "Number of integration settings exceeds limit（%v）"
  oauth_token_exchange_failed: "OAuth token exchange failed"
  only_one_field_must_have_value: "only one of %s and %s must have a value"
  only_one_of_must_have_value: "only one of %s must have value"
  other_integration_is_running: "other integration is running"
  other_work_report_sync_is_running: "work reoprt sync is already in progress"
  password_invalid_format: "password invalid format"
  process_duplicated_combination: "Duplicated combination in 'processes' found"
  process_not_match: "'processes' does not match 'Use of Process Input' settings"
  process_set_as_category: "this process is set as a category"
  process_type_using_group: "this project is using process group"
  process_type_not_use_group: "this project is not using process group"
  project_type_not_process: "this project is not process project"
  request_failed: Request failed
  required: "%s is required"
  required_two_and_or: "%s and/or %s are/is required"
  required_at_least_one: "At least one is must be %s in [%s]"
  required_both: "%s and %s must be required"
  required_project_master_field_exist: "Creating/updating project is not allowed if an existing 'Project Master Field' is required"
  request_exceeds_ai_usage_limit: "Request exceeds AI usage limit (Total usage: %d, Pending usage: %d, Limit: %d)"
  report_type_invalid_format: "report type invalid format"
  server_error: internal server error
  set_value_at: "'%s' must have a value to set '%s'"
  some_not_exist: "Some values of '%s' does not exist"
  some_values_of_are_inactive: "Some values of '%s' are inactive:%v"
  some_values_of_do_not_exist: "Some values of '%s' do not exist:%v"
  some_values_of_are_process_category: "Some values of '%s' are process category:%v"
  timesheet_future_disabled: "Timesheet future registration is disabled"
  timesheet_is_closed: "Timesheet date is earlier or on closing date"
  timesheet_export_category_not_supported: "%s not supported"
  timesheet_no_approver: "No approver was set"
  timesheet_not_use_approval: "Timesheet use of approval is not open"
  timesheet_not_exists_to_apply: "No timesheet exists to apply"
  timesheet_not_exists_to_unapply: "No timesheet in pending status exists to unapply"
  timesheet_not_editable_to_closed_date: "Cannot edit a date-closed timesheet"
  timesheet_not_editable_out_of_user_period: "Cannot edit a timesheet whose date is out of the employee period"
  value_of_is_inactive: "The value of '%s' is inactive"
  work_and_attendance_must_be_matched: "Work and attendance must be matched"
  workflow_not_editable: "Timesheet workflow status is pending, or approved"
  work_report_max_dimensions: "Number of dimensions may not exceed %d."
  work_report_data_empty: "Work Report Data is empty."
  work_budget_exceed: "Work for one month exceed %v %v"
  work_budget_out_of_project_period: "Cannot be changed due to outside the project period"
  work_ignored_as_copy: "Some works outside the project period will not be copied"
  pattern_exist: "Pattern already exists."
  sw_status_conflict: "The status of stow watch is not correct to proceed."
  sw_limit_max_count: "Number of stop watches may not exceed %d."
  sw_invalid_start_end_time: "The start or end time of timing is invalid."
  sw_exists_another_with_timing: "Cannot start due to another stop watch is in timing."
  sw_time_duplicated_apply: "The result of stop watch timing '%s~%s' has already been applied to timesheet."
  task_group_exceed_nested_limit: "Task parent exceed max nested limit: [%s]"
  target_task_must_be_in_request: "The target task must be included in the request body. [taskId: %s, in: %s, ope: %s]"

perm_err_msg:
  forbidden: *forbidden
  insufficient_perm_to_update: "insufficient permission to update %s"
  insufficient_perm_to_copy: "insufficient permission to copy %s"
  insufficient_perm_to_use_the_value: "insufficient permission to use the value of %s"
  insufficient_pj_perm_or_auth: insufficient project permission or authorization
  insufficient_system_admin: insufficient system admin permission
  insufficient_user_pj_auth_ts_input: insufficient user project authorization (timesheet input)
  insufficient_x_in_perm_or_pj_auth: insufficient %s settings in the permission master or project authorization
  insufficient_x_perm: insufficient %s permission
  must_not_use_of_att_mng: *attendance_must_not_use
  must_use_ts_att_and_not_att_mng: "available only when 'Use of Attendance' of Timesheet is used and Attendance Management 'Use of Attendance' is not used in company settings"

integration_err_msg:
  api_access_error: "API access error"
  api_call_limit_exceeded: "API call limit exceeded"
  attendance_synchronized_by_another_condition_existed: "Attendance synchronized by another condition existed"
  attendance_modified: "Attendance was modified"
  cannot_convert_attendance: "Cannot convert attendance"
  cannot_get_access_token: "Cannot get Access Token"
  cannot_get_crowdlog_employee: "Cannot get CrowdLog employees"
  cannot_get_employee: "Cannot get employee"
  cannot_save_attendance: "Cannot save attendance"
  employee_not_found: "Employee is not found"
  invalid_access_token: "Invalid Access Token"
  integ_setting_not_found: "Integration setting is not found"
  invalid_jobcan_api_client_id_or_secret: *invalid_jobcan_api_client_id_or_secret
  no_attendance: "No attendance"
  other_integration_is_running: "Other integration is running"
  integration_is_already_running: "Integration is already running"
  server_error: Internal server error
  target_end_date_is_before_closing_date: "Target end date is before closing date"
  target_start_date_is_changed: "Target start date is changed"
  unable_to_start_integration: "Unable to start integration"
  unknown_target_system: "Unknown integration target system"

# export_format.name must be same to config.ExportName
# export_format.name.key must be same to model/export/xxx.go > struct tag key
export_format:
  contract:
    management_code: { name: management_code }
    employ_type_name: { name: employ_type_name }
    order_no: { name: order_no }
    active_flag: { name: active_flag }
  gantt_chart:
    project_code: { name: project_code }
    project_name: { name: project_name }
    task_type: { name: task_type }
    task_code: { name: task_code }
    task_name: { name: task_name }
    task_group_code: { name: task_group_code }
    task_group_name: { name: task_group_name }
    start_date: { name: start_date }
    end_date: { name: end_date }
    work_first: { name: work_first }
    work_last: { name: work_last }
    work_budget: { name: work_budget }
    work_result: { name: work_result }
    progress: { name: progress }
    assignee_member_code: { name: assignee_member_code }
    assignee_name: { name: assignee_name }
    transl_mgmt_code@@: { name: "%s:management_code" } # use as <process>:mangement_code
    transl_name@@: { name: "%s:name" } # use as <process>:name
    upstream_task_code: { name: upstream_task_code }
    upstream_task_name: { name: upstream_task_name }
    status: { name: status }
    comments: { name: comments }
  project:
    project_code: { name: project_code }
    project_name: { name: project_name }
    customer_code: { name: customer_code }
    customer_name: { name: customer_name }
    business_code: { name: business_code }
    business_name: { name: business_name }
    department_code: { name: department_code }
    department_name: { name: department_name }
    project_owner_code: { name: project_owner_code }
    project_owner_name: { name: project_owner_name }
    start_date: { name: start_date }
    end_date: { name: end_date }
    ganttchart: { name: ganttchart }
    active_flag: { name: active_flag }
    comments: { name: comments }
  project_department:
    project_code: { name: project_code }
    project_name: { name: project_name }
    department_code: { name: department_code }
    department_name: { name: department_name }
    active_flag: { name: active_flag }
    timesheet_input: { name: timesheet_input }
    auth_basic: { name: auth:basic }
    auth_accounting: { name: auth:accounting }
    auth_work: { name: auth:work }
  project_member:
    project_code: { name: project_code }
    project_name: { name: project_name }
    member_code: { name: member_code }
    member_name: { name: member_name }
    timesheet_input: { name: timesheet_input }
    auth_basic: { name: auth:basic }
    auth_accounting: { name: auth:accounting }
    auth_work: { name: auth:work }
  project_process:
    project_code: { name: project_code }
    project_name: { name: project_name }
    process_code: { name: process_code }
    process_name: { name: process_name }
    level: { name: level }
    active_flag: { name: active_flag }
  project_process_group:
    project_code: { name: project_code }
    project_name: { name: project_name }
    process_group_code: { name: process_group_code }
    process_group_name: { name: process_group_name }
  project_task_process:
    project_code: { name: project_code }
    project_name: { name: project_name }
    process_code: { name: process_code }
    process_name: { name: process_name }
    level: { name: level }
    active_flag: { name: active_flag }
  project_task_process_group:
    project_code: { name: project_code }
    project_name: { name: project_name }
    process_group_code: { name: process_group_code }
    process_group_name: { name: process_group_name }
  work_budget:
    project_code: { name: project_code }
    project_name: { name: project_name }
    member_code: { name: member_code }
    member_name: { name: member_name }
    date: { name: date }
    work_budget: { name: work_budget }
    task_work_budget: { name: task_work_budget }
    total_work_budget: { name: total_work_budget }
    work_budget_min: { name: work_budget_min }
    task_work_budget_min: { name: task_work_budget_min }
    total_work_budget_min: { name: total_work_budget_min }
  work_report:
    project_code: { name: project_code }
    project_name: { name: project_name }
    project_business_code: { name: business_code }
    project_business_name: { name: business_name }
    transl_proj_busi_code_lvl@@: { name: business_code_level_%s }
    transl_proj_busi_name_lvl@@: { name: business_name_level_%s }
    project_customer_code: { name: customer_code }
    project_customer_name: { name: customer_name }
    project_department_code: { name: project_department_code }
    project_department_name: { name: project_department_name }
    transl_proj_dept_code_lvl@@: { name: project_department_code_level_%s }
    transl_proj_dept_name_lvl@@: { name: project_department_name_level_%s }
    project_owner_code: { name: project_owner_code }
    project_owner_name: { name: project_owner_name }
    member_code: { name: member_code }
    member_name: { name: member_name }
    member_employ_type_code: { name: employ_type_code }
    member_employ_type_name: { name: employ_type_name }
    member_customer_code: { name: member_customer_code }
    member_customer_name: { name: member_customer_name }
    member_department_code: { name: member_department_code }
    member_department_name: { name: member_department_name }
    transl_mem_dept_code_lvl@@: { name: member_department_code_level_%s }
    transl_mem_dept_name_lvl@@: { name: member_department_name_level_%s }
    member_title_code: { name: title_code }
    member_title_name: { name: title_name }
    transl_mgmt_code@@: { name: "%s:management_code" } # use as <process>:management_code
    transl_name@@: { name: "%s:name" } # use as <process>:name
    type: {name: type}
    work_unit: { name: work_unit }
    total: { name: total }
  timesheet:
    member_code: { name: member_code }
    member_name: { name: member_name }
    email: { name: email }
    title_name: { name: title_name }
    contract_name: { name: contract_name }
    customer_name: { name: customer_name }
    department_code: { name: department_code }
    department_name: { name: department_name }
    type: { name: type }
    total: { name: total }
  title:
    management_code: { name: management_code }
    title_name: { name: title_name }
    order_no: { name: order_no }
    active_flag: { name: active_flag }
  business:
    management_code: { name: management_code }
    business_name: { name: business_name }
    parent_code: { name: parent_code }
    parent_name: { name: parent_name }
    comments: { name: comments }
    order_no: { name: order_no }
    active_flag: { name: active_flag }
  customer:
    management_code: { name: management_code }
    customer_name: { name: customer_name }
    receive_flag: { name: receive_flag }
    order_flag: { name: order_flag }
    zip: { name: zip }
    address: { name: address }
    tel: { name: tel }
    charge_name: { name: charge_name }
    member_code: { name: member_code }
    member_name: { name: member_name }
    pay_closing_day: { name: pay_closing_day }
    pay_sight_month: { name: pay_sight_month }
    pay_sight_day: { name: pay_sight_day }
    notes: { name: notes }
    active_flag: { name: active_flag }
  attendance_difference:
    attendance_date: { name: attendance_date }
    member_code: { name: member_code }
    member_name: { name: member_name }
    email: { name: email }
    title_name: { name: title_name }
    contract_name: { name: contract_name }
    customer_name: { name: customer_name }
    department_code: { name: department_code }
    department_name: { name: department_name }
    start_time: { name: start_time }
    pclog_start_time: { name: pclog_start_time }
    start_time_diff: { name: start_time_diff }
    start_time_diff_flag: { name: start_time_diff_flag }
    end_time: { name: end_time }
    pclog_end_time: { name: pclog_end_time }
    end_time_diff: { name: end_time_diff }
    end_time_diff_flag: { name: end_time_diff_flag }
    work_time: { name: work_time }
    pclog_work_time: { name: pclog_work_time }
    work_time_diff: { name: work_time_diff }
    idle_time: { name: idle_time }
    reas_reason_class: { name: reason_of_attendance_difference }
    reas_reason: { name: remarks }
    reas_time: { name: attendance_difference_reason_store_time }
    fixed_status: { name: finalized }

notification:
  wir:
    mail:
      common:
        sender_name: "CrowdLog Support Desk"
        receiver: "Dear %s"
        notif_condition1: "・Total attendance time and total work time does not match\n"
        notif_condition2: "・Total work time less than %.2f hours\n"
        notif_condition3: "▼Notification Conditions\n%s%s"
        notif_period1: "01-02-2006"
        notif_period2: "▼Notification period\n%s〜%s\n*Excluding Saturdays, Sundays and holidays\n"
        notif_unsubscribe: "▼Unsubscribe from this mail."
        disclaimer: "※This mail address is for sending only and cannot be replied to."
        day_of_week: "Sun,Mon,Tue,Wed,Thu,Fri,Sat"
      for_employee:
        title: "【CrowdLog】Please complete the work time entries"
        main_message: "There are date(s) for which work time entry has not been completed.\nComplete the entry of work time from the timesheet.\n"
        call_to_action: "▼Log in to CloudLog\n\n%s\n"
      for_sysadmin:
        title: "【CrowdLog】Missed work time reminder notifications sent【%s】"
        main_message: "Notification Name: %s\n\nWork time entry for %d member(s) has not been completed.\nNotification has been sent to member(s) who have(has) not completed work time entry.\n\nPlease see timesheet report for details.\n"
        call_to_action: "▼Check timesheet reports\n\n%s\n"
      for_employee_with_title:
        title: "【CrowdLog】Work time entries of employee is not complete"
        main_message: "Notification has been sent to following employees who have not completed the work time entries.\nThis email is sent to the %s."
        recipients_title: "Members to be notified are as follows."
        recipients: "%d day(s) remaining"
    title:
      member: Notification to the Individual
      title_member: Summary Notification to the employee with title
      sysadmin: Summary Notification to the System Administrator
    word:
      configured_notification_name: Configured Notification Name

attendance_difference:
  reason_class:
    others: "Other (Free text)"
    pc_used_not_for_work: "PC has been used for non-work purposes"
    pc_unnecessarily: "Tasks that did not require PC use (outside visits or travel)"
    pc_log_unreadable: "PC log acquisition failure (network issues or system errors)"
    pc_off_omission: "PC was not properly off (shutdown or screen lock was missed)"
