word:
  admin: 管理者
  attendance: 勤怠
  attendance_integration: 勤怠連携
  afternoon_day_off: 午後休
  attendance_difference_reason: 乖離理由
  attendance_work_match: 勤怠工数一致
  budget: 予算
  business: プロジェクトカテゴリ
  category: 親工程
  company_process_input_is_0: 基本設定内の工程入力が無効
  company_process_input_is_lt_2: 基本設定内の工程入力が２つ未満
  company_process_input_is_lt_3: 基本設定内の工程入力が３つ未満
  company_work_unit: 基本設定内の入力単位
  contract: 雇用形態
  date: 日付
  datetime: 日時
  department: 部署
  department_history: 部署履歴
  difference: 差分
  dimension: 集計軸
  hierarchy: 階層
  holiday_type: 休暇種別
  jobs: 工数
  kot: KING OF TIME
  kinkakuji: 勤革時
  man_day: 人日
  man_hour: 人時
  man_minute: 人分
  man_month: 人月
  morning_day_off: 午前休
  notification: 通知
  parent_department: 親の部署
  pclog: PCログ
  process: 工程
  process_caterogy: 工程カテゴリ
  process_use_count_not_used: 工程の件数取得が無効
  process_group: 工程グループ
  project: プロジェクト
  project_department: プロジェクト部署
  project_gantt: ガントチャートプロジェクト
  project_info: プロジェクト情報
  project_process: 工程プロジェクト
  project_process_or_group: プロジェクト（工程または工程グループ）
  project_range: プロジェクト期間
  project_work: プロジェクト工数
  result: 実績
  role_permission: ロール権限
  task: タスク
  tasks: タスク
  timesheet: タイムシート
  timesheet_authorizer: タイムシート承認者
  timesheet_copy: タイムシート複製
  timesheet_date: タイムシートの日付
  timesheet_period_notified: 通知対象のタイムシート期間
  title: 役職
  unassigned_task: 担当者なし
  unit_price_start_date: 単価の開始日
  user: ユーザー
  user_period: ユーザーの契約期間
  user_work_day: ユーザーの勤務時間
  whole_number: 整数
  work: 工数
  work_date: 勤務日
  work_time_attendance_unmatch: 工数と勤怠の不一致
  day_off: 全休
  project_str_none: (なし)

msg:
  not_found: 存在しません
  notif:
    smp:
      subject: テスト通知
      body: これはテストメッセージです
    tsl:
      subject: タイムシート入力状況一覧

err_msg:
  already_exist: "%sが既に存在します"
  attendance_not_exist: "勤怠がありません"
  attendance_time_exist: "勤怠時刻が既に存在します"
  auth_failed: 認証に失敗しました
  canceled: "リクエストがキャンセルされました"
  cannot_be_set: "「%s」は「%s」に設定できません"
  cannot_be_set_at_same_time: "「%s」と「%s」は同時に登録できません"
  cannot_be_set_at_same_time_when: "%sと%sは%sが指定されている場合は同時に指定できません"
  cannot_be_set_when: "%sを%sに指定できません（%s指定時）"
  cannot_be_updated: "「%s」は更新できません"
  cannot_deactivate_status_when_in_use: "%sで使用されているため、ステータスを非アクティブにできません"
  cannot_execute_in_a_row: "前回の実行から%d分経過していない限り連続で実行できません"
  cannot_stamp_when_not_rest: "休憩中ではないため、休憩を終了できません"
  cannot_stamp_when_rest: "休憩中のため%sを登録できません"
  cannot_update_category_process: カテゴリとして登録されている工程は更新できません
  cannot_use_calendar_sync: "外部カレンダー連携の設定が「なし」になっています"
  circulation_hierarchies: "%sが循環しています"
  company_ai_work_report_summary_disabled: "基本設定内の「AI分析サマリレポートを使う」が無効になっている場合は利用できません"
  company_am_use_of_attendance_must_not_use: &attendance_must_not_use "基本設定内の「勤怠管理の使用」が有効の場合は利用できません"
  company_gantt_chart_not_used: "基本設定内の「ガントチャート」が無効になっている場合、ガントチャートプロジェクトは作成できません"
  company_process_disabled: "基本設定内の「工程入力」が無効になっている場合は利用できません"
  company_ts_use_of_attendance_must_use: "基本設定内の「勤怠の使用」が無効の場合は利用できません"
  company_ts_use_of_approval_must_use: "基本設定内の「承認の使用」が無効の場合は利用できません"
  contains_invalid_value: "%sに無効な値が含まれています"
  dates_gte: "「%s」と「%s」は「%s」以降である必要があります"
  date_is_before_closing_date: "「%s」が締日より前に指定されています"
  difference_in_timesheet_settings: "リクエストデータとタイムシート設定に差異があります"
  duplicate: 値が重複しています（%v）
  expired_oauth_code: 認証コードの期限切れのためトークンを取得できません
  favorite_custom_work_report_exist: "このカスタム工数レポートはお気に入りに登録済です"
  file_format_not_supported: "「%s」はサポートしていません"
  forbidden: &forbidden "利用できません: %s"
  gte: "%sは %s 以上である必要があります"
  in_use: データが利用中です
  in_use_detail: "データが「%s」で利用されています"
  integ_setting_not_found: "連携設定が存在しません"
  integ_system_is_unavailable: "「%s」は「%s」の間利用できません"
  invalid_access_token: "不正なアクセストークンです"
  invalid_date_format: "%sの日付形式が不正です"
  invalid_date_range: "%sと%sの日付範囲(%d日以内)が不正です"
  invalid_format: "%sの形式が不正です"
  invalid_format_in_one_of_dates: "「%s」または「%s」の日付形式が不正です"
  invalid_input: 無効な入力値です
  invalid_jobcan_api_client_id_or_secret: &invalid_jobcan_api_client_id_or_secret "無効なAPIクライアントIDまたはシークレットです"
  invalid_hrmos_company_key_or_secret: &invalid_hrmos_company_key_or_secret "無効な会社名またはシークレットです"
  invalid_start_day: "無効な日付切り替え時刻です: %v"
  invalid_target_system: 無効な連携先システムです
  invalid_value: "%vは無効な値です"
  lt: "%sは %s 未満である必要があります"
  lte: "%sは %s 以下である必要があります"
  max: "%sと%sは %d 以下である必要があります"
  max_records_reached: 「%s」の登録件数が上限（%d件）に達しています
  must_be_assign_to: "「%s」は「%s」にアサインされている必要があります"
  must_be_between: "%sは%d〜%dの範囲内である必要があります"
  must_be_one_of: "%s は %v のいずれかでなければなりません"
  must_be_set_to_when: "%sは%sである必要があります（%s指定時）"
  must_be_set_when: "「%s」は「%s」の場合には設定されている必要があります"
  must_be_within: "「%s」は「%s」内である必要があります"
  must_be_within_when_copy_timesheet: "コピーできるものがありません。「%s」は「%s」内である必要があります"
  must_belong_to: "「%s」は「%s」に属している必要があります"
  must_follow_pw_restr: "パスワード文字列の制限を満たすパスワードを設定する必要があります"
  must_not_be: "%sに%sは指定できません"
  must_not_set_when: "「%s」は「%s」の場合には設定できません"
  must_not_set_when_use_day_off_is_not_use: "基本設定内の「休暇の使用」が無効の場合、「%s」を設定できません"
  must_overlap: "「%s」は「%s」と重なっている必要があります"
  must_same: "「%s」は「%s」と同じである必要があります"
  no_difference: "%v と %v に乖離はありまあせん"
  no_resource: リソースがありません
  not_available: "利用できない%sです"
  not_child: "「%s」をその子階層としては登録できません"
  not_divisible: "「%s」は「%s」で割り切れません"
  not_exist: "「%s」は存在しない値です"
  not_same_as: "「%s」と「%s」は異なる値を指定してください"
  notif_failed: "通知の送信に失敗しました"
  number_of_integration_settings_exceeds_limit: "連携設定の数が上限（%v）をこえています"
  oauth_token_exchange_failed: "OAuthトークンの交換に失敗しました"
  only_one_field_must_have_value: "「%s」と「%s」のどちらか一方のみが設定されている必要があります。"
  only_one_of_must_have_value: "%sのうち、いずれかひとつのみを指定する必要があります"
  other_integration_is_running: "他の連携が実行中です"
  other_work_report_sync_is_running: "工数レポートが既に更新中です"
  password_invalid_format: "パスワードの形式が不正です"
  process_duplicated_combination: "「processes」に重複した組み合わせがあります"
  process_not_match: "「processes」が「工程入力」設定と一致しません"
  process_set_as_category: "この工程はカテゴリとして登録されています"
  process_type_using_group: "このプロジェクトは工程グループを使用しています"
  process_type_not_use_group: "このプロジェクトは工程グループを使用していません"
  project_type_not_process: "このプロジェクトは工程プロジェクトではありません"
  request_failed: リクエストに失敗しました
  required: "%sが必要です"
  required_two_and_or: "%s and/or %sが必要です"
  required_at_least_one: "次のうち、少なくとも1つは「%s」である必要があります。（%s）"
  required_both: "%s と %s の両方が必要です"
  required_project_master_field_exist: "既存のカスタム項目が必須項目として設定されている場合、プロジェクトの作成・更新はできません"
  request_exceeds_ai_usage_limit: "AI利用制限を超えています（現在の利用量: %d、使用保留中: %d、制限: %d）"
  report_type_invalid_format: "レポートタイプの形式が不正です"
  server_error: 内部サーバーエラー
  set_value_at: "「%s」を設定するためには「%s」が必須です"
  some_not_exist: "「%s」 に存在しない値が含まれます"
  some_values_of_are_inactive: "「%s」に非アクティブな値が含まれています:%v"
  some_values_of_do_not_exist: "「%s」に存在しない値が含まれています:%v"
  some_values_of_are_process_category: "「%s」にカテゴリとして登録された値が含まれています:%v"
  timesheet_future_disabled: "未来のタイムシート登録は無効となっています"
  timesheet_is_closed: "タイムシートの日付が締め日以前になっています"
  timesheet_export_category_not_supported: "「%s」はサポートしていません"
  timesheet_no_approver: "承認者が設定されていません"
  timesheet_not_use_approval: "タイムシートの申請・承認機能がオフに設定されています"
  timesheet_not_exists_to_apply: "申請対象のタイムシートが存在しません"
  timesheet_not_exists_to_unapply: "承認待ちのタイムシートではないため申請キャンセルできません"
  timesheet_not_editable_to_closed_date: "締め日より過去のタイムシートのため編集できません"
  timesheet_not_editable_out_of_user_period: "ユーザー契約期間外のタイムシートのため編集できません"
  value_of_is_inactive: "「%s」 の値は非アクティブです。"
  work_and_attendance_must_be_matched: "工数と勤怠は一致している必要があります"
  workflow_not_editable: "タイムシート承認ステータスが承認待ちまたは承認済みです"
  work_report_max_dimensions: "指定可能な最大列数は%dです。"
  work_report_data_empty: "対象の工数レポートが見つかりませんでした。"
  work_budget_exceed: "1ヶ月の工数が%v%vを超えています"
  work_budget_out_of_project_period: "プロジェクト期間外のため変更できません"
  work_ignored_as_copy: "プロジェクト期間外の一部の工数はコピーされません"
  pattern_exist: "マイパターンは登録済みです。"
  sw_status_conflict: "ストップウォッチのステータスが正しくありません。"
  sw_limit_max_count: "追加可能な最大ストップウォッチ数は%dです。"
  sw_invalid_start_end_time: "計測の開始時間または終了時間は無効です。"
  sw_exists_another_with_timing: "ほかの計測中のストップウォッチがあるためスタートできません。"
  sw_time_duplicated_apply: "計測した結果「%s~%s」は既にタイムシートへ反映済みです。"
  task_group_exceed_nested_limit: "タスクグループの階層数が上限を超えています: [%s]"
  target_task_must_be_in_request: "対象のタスクをリクエストボディに含める必要があります。 [taskId: %s, in: %s, ope: %s]"

perm_err_msg:
  forbidden: *forbidden
  insufficient_perm_to_update: "「%s」を更新する権限がありません"
  insufficient_perm_to_copy: "「%s」をコピーする権限がありません"
  insufficient_perm_to_use_the_value: "「%s」を利用する権限がありません"
  insufficient_pj_perm_or_auth: プロジェクトの権限が不足しています
  insufficient_system_admin: システム管理者の権限が不足しています
  insufficient_user_pj_auth_ts_input: プロジェクトの権限が不足しています（タイムシート入力）
  insufficient_x_in_perm_or_pj_auth: 権限マスタまたはプロジェクトの権限にて、「%s」の設定が不足しています
  insufficient_x_perm: 権限マスタにて、「%s」の設定が不足しています
  must_not_use_of_att_mng: *attendance_must_not_use
  must_use_ts_att_and_not_att_mng: "基本設定内の「勤怠の使用」が使用する、かつ「勤怠管理の使用」が使用しない場合のみ利用できます"

integration_err_msg:
  api_access_error: "APIアクセスでエラーとなりました"
  api_call_limit_exceeded: "API呼び出しの上限を超えました"
  attendance_synchronized_by_another_condition_existed: "他の条件で同期済みの勤怠が存在していました"
  attendance_modified: "勤怠が変更されました"
  cannot_convert_attendance: "勤怠を変換できませんでした"
  cannot_get_access_token: "アクセストークンを取得できませんでした"
  cannot_get_crowdlog_employee: "CrowdLogのメンバーを取得できませんでした"
  cannot_get_employee: "取得できない連携先従業員がありました"
  cannot_save_attendance: "勤怠を保存できませんでした"
  employee_not_found: "従業員がありません"
  invalid_access_token: "不正なアクセストークンです"
  integ_setting_not_found: "連携設定が存在しません"
  invalid_jobcan_api_client_id_or_secret: *invalid_jobcan_api_client_id_or_secret
  no_attendance: "勤怠が存在しません"
  other_integration_is_running: "他の連携が実行中です"
  integration_is_already_running: "すでに連携が実行中です"
  server_error: 内部サーバーエラー
  target_end_date_is_before_closing_date: "連携先終了日が締日より前に指定されています"
  target_start_date_is_changed: "連携先開始日が変更されました"
  unable_to_start_integration: "連携を開始できません"
  unknown_target_system: "未知の統合対象システム"

# export_format.name must be same to config.ExportName
# export_format.name.key must be same to model/export/xxx.go > struct tag key
export_format:
  contract:
    management_code: { name: 管理コード }
    employ_type_name: { name: 雇用形態名 }
    order_no: { name: 表示順 }
    active_flag: { name: アクティブ }
  gantt_chart:
    project_code: { name: プロジェクトコード }
    project_name: { name: プロジェクト名 }
    task_type: { name: 種別 }
    task_code: { name: タスクコード }
    task_name: { name: タスク名 }
    task_group_code: { name: タスクグループコード }
    task_group_name: { name: タスクグループ名 }
    start_date: { name: 開始日 }
    end_date: { name: 終了日 }
    work_first: { name: 工数入力開始日 }
    work_last: { name: 工数入力最終日 }
    work_budget: { name: 工数予算 }
    work_result: { name: 工数実績 }
    progress: { name: 進捗 }
    assignee_member_code: { name: 担当者社員コード }
    assignee_name: { name: 担当者 }
    transl_mgmt_code@@: { name: "%s:管理コード" } # use as <process>:管理コード
    transl_name@@: { name: "%s:名称" } # use as <process>:名称
    upstream_task_code: { name: 上位タスクコード }
    upstream_task_name: { name: 上位リンク }
    status: { name: ステータス }
    comments: { name: コメント }
  project:
    project_code: { name: プロジェクトコード }
    project_name: { name: プロジェクト名 }
    customer_code: { name: 取引先管理コード }
    customer_name: { name: 取引先名 }
    business_code: { name: プロジェクトカテゴリ管理コード }
    business_name: { name: プロジェクトカテゴリ名 }
    department_code: { name: 部署管理コード }
    department_name: { name: 部署名 }
    project_owner_code: { name: プロジェクト責任者社員コード }
    project_owner_name: { name: プロジェクト責任者名 }
    start_date: { name: 開始日 }
    end_date: { name: 終了日 }
    ganttchart: { name: ガントチャート }
    active_flag: { name: アクティブ }
    comments: { name: コメント }
  project_department:
    project_code: { name: プロジェクトコード }
    project_name: { name: プロジェクト名 }
    department_code: { name: 部署コード }
    department_name: { name: 部署名 }
    active_flag: { name: アクティブ }
    timesheet_input: { name: タイムシート入力有無 }
    auth_basic: { name: プロジェクト情報権限 }
    auth_accounting: { name: プロジェクト損益権限 }
    auth_work: { name: プロジェクト工数権限 }
  project_member:
    project_code: { name: プロジェクトコード }
    project_name: { name: プロジェクト名 }
    member_code: { name: プロジェクトメンバー社員コード }
    member_name: { name: 社員名 }
    timesheet_input: { name: タイムシート入力有無 }
    auth_basic: { name: プロジェクト情報権限 }
    auth_accounting: { name: プロジェクト損益権限 }
    auth_work: { name: プロジェクト工数権限 }
  project_process:
    project_code: { name: プロジェクトコード }
    project_name: { name: プロジェクト名 }
    process_code: { name: 工程管理コード }
    process_name: { name: 工程名 }
    level: { name: レベル }
    active_flag: { name: アクティブ }
  project_process_group:
    project_code: { name: プロジェクトコード }
    project_name: { name: プロジェクト名 }
    process_group_code: { name: 工程グループコード }
    process_group_name: { name: 工程グループ名 }
  project_task_process:
    project_code: { name: プロジェクトコード }
    project_name: { name: プロジェクト名 }
    process_code: { name: 工程管理コード }
    process_name: { name: 工程名 }
    level: { name: レベル }
    active_flag: { name: アクティブ }
  project_task_process_group:
    project_code: { name: プロジェクトコード }
    project_name: { name: プロジェクト名 }
    process_group_code: { name: 工程グループコード }
    process_group_name: { name: 工程グループ名 }
  work_budget:
    project_code: { name: プロジェクトコード }
    project_name: { name: プロジェクト名 }
    member_code: { name: 社員コード }
    member_name: { name: 社員名 }
    date: { name: 日付 }
    work_budget: { name: 工数予算 }
    task_work_budget: { name: タスク工数予算 }
    total_work_budget: { name: 総工数予算 }
    work_budget_min: { name: 工数予算（分） }
    task_work_budget_min: { name: タスク工数予算（分） }
    total_work_budget_min: { name: 総工数予算（分） }
  work_report:
    project_code: { name: プロジェクトコード }
    project_name: { name: プロジェクト名 }
    project_business_code: { name: プロジェクトカテゴリ管理コード }
    project_business_name: { name: プロジェクトカテゴリ名 }
    transl_proj_busi_code_lvl@@: { name: プロジェクトカテゴリ管理コード (%s階層) }
    transl_proj_busi_name_lvl@@: { name: プロジェクトカテゴリ名 (%s階層) }
    project_customer_code: { name: 取引先管理コード }
    project_customer_name: { name: 取引先名 }
    project_department_code: { name: プロジェクト部署管理コード }
    project_department_name: { name: プロジェクト部署名 }
    transl_proj_dept_code_lvl@@: { name: プロジェクト部署管理コード (%s階層) }
    transl_proj_dept_name_lvl@@: { name: プロジェクト部署名 (%s階層) }
    project_owner_code: { name: プロジェクト責任者社員コード }
    project_owner_name: { name: プロジェクト責任者名 }
    member_code: { name: 社員コード }
    member_name: { name: 社員名 }
    member_employ_type_code: { name: 雇用形態管理コード }
    member_employ_type_name: { name: 雇用形態名 }
    member_customer_code: { name: 所属管理コード }
    member_customer_name: { name: 所属 }
    member_department_code: { name: メンバー部署管理コード }
    member_department_name: { name: メンバー部署名 }
    transl_mem_dept_code_lvl@@: { name: メンバー部署管理コード (%s階層) }
    transl_mem_dept_name_lvl@@: { name: メンバー部署名 (%s階層) }
    member_title_code: { name: 役職管理コード }
    member_title_name: { name: 役職名 }
    transl_mgmt_code@@: { name: "%s:管理コード" } # use as <process>:management_code
    transl_name@@: { name: "%s:名称" } # use as <process>:name
    type: {name: 種類}
    work_unit: { name: 工数単位 }
    total: { name: 合計 }
  timesheet:
    member_code: { name: メンバーコード }
    member_name: { name: メンバー名 }
    email: { name: メールアドレス }
    title_name: { name: 役職 }
    contract_name: { name: 雇用形態 }
    customer_name: { name: 所属 }
    department_code: { name: 部署コード }
    department_name: { name: 部署名 }
    type: { name: 種類 }
    total: { name: 合計 }
  title:
    management_code: { name: 管理コード }
    title_name: { name: 役職名 }
    order_no: { name: 表示順 }
    active_flag: { name: アクティブ }
  business:
    management_code: { name: 管理コード }
    business_name: { name: プロジェクトカテゴリ名 }
    parent_code: { name: 上位管理コード }
    parent_name: { name: 上位プロジェクトカテゴリ名 }
    comments: { name: 説明 }
    order_no: { name: 表示順 }
    active_flag: { name: アクティブ }
  customer:
    management_code: { name: 管理コード }
    customer_name: { name: 取引先名 }
    receive_flag: { name: 受注フラグ }
    order_flag: { name: 発注フラグ }
    zip: { name: 郵便番号 }
    address: { name: 住所 }
    tel: { name: 電話番号 }
    charge_name: { name: 取引先担当者名 }
    member_code: { name: 担当者社員コード }
    member_name: { name: 担当者社員名 }
    pay_closing_day: { name: 入金・支払条件:締め日 }
    pay_sight_month: { name: 入金・支払条件:月 }
    pay_sight_day: { name: 入金・支払条件:日 }
    notes: { name: 備考 }
    active_flag: { name: アクティブ }
  attendance_difference:
    attendance_date: { name: 日付 }
    member_code: { name: 社員コード }
    member_name: { name: 社員名 }
    email: { name: メールアドレス }
    title_name: { name: 役職 }
    contract_name: { name: 雇用形態 }
    customer_name: { name: 所属 }
    department_code: { name: 部署コード }
    department_name: { name: 部署名 }
    start_time: { name: 開始時刻 }
    pclog_start_time: { name: PCログ開始時刻 }
    start_time_diff: { name: 開始時刻乖離時間 }
    start_time_diff_flag: { name: 開始時刻乖離フラグ }
    end_time: { name: 終了時刻 }
    pclog_end_time: { name: PCログ終了時刻 }
    end_time_diff: { name: 終了時刻乖離時間 }
    end_time_diff_flag: { name: 終了時刻乖離フラグ }
    work_time: { name: 労働時間 }
    pclog_work_time: { name: PCログ労働時間 }
    work_time_diff: { name: 労働時間乖離時間 }
    idle_time: { name: 無操作時間 }
    reas_reason_class: { name: 乖離理由 }
    reas_reason: { name: 備考 }
    reas_time: { name: 乖離理由登録日時 }
    fixed_status: { name: 確定済み }

notification:
  wir:
    mail:
      common:
        sender_name: "クラウドログサポートデスク"
        receiver: "%s 様"
        notif_condition1: "・勤務合計と工数合計が一致していない\n"
        notif_condition2: "・工数合計が%.2f時間に満たない\n"
        notif_condition3: "▼通知条件\n%s%s"
        notif_period1: "2006年1月2日"
        notif_period2: "▼対象期間\n%s〜%s\n*土日祝を除く\n"
        notif_unsubscribe: "▼このメールの配信を停止する"
        disclaimer: "※本メールアドレスは送信専用のため、返信できません。"
        day_of_week: "日,月,火,水,木,金,土"
      for_employee:
        title: "【クラウドログ】工数の入力を完了させてください"
        main_message: "工数入力が完了していない日付があります。\nタイムシートから工数の入力を完了させてください。\n"
        call_to_action: "▼クラウドログにログインする\n\n%s\n"
      for_sysadmin:
        title: "【クラウドログ】工数入力漏れリマインド通知を送信しました【%s】"
        main_message: "通知名: %s\n\n%d人のメンバーの工数入力が完了していません。\n工数入力が完了していないメンバーへ通知が送信されました。\n\n詳細はタイムシートレポートをご確認ください。\n"
        call_to_action: "▼タイムシートレポートを確認する\n\n%s\n"
      for_employee_with_title:
        title: "【クラウドログ】メンバーの工数入力が完了していません"
        main_message: "以下の工数入力が完了していないメンバーへ通知が送信されました。\nこのメールは%sの方へ送信しています。"
        recipients_title: "通知対象メンバーは以下の通りです。"
        recipients: "残り%d日分"
    title:
      member: 本人への通知
      title_member: 役職者へのサマリ通知
      sysadmin: システム管理者へのサマリ通知
    word:
      configured_notification_name: 設定した通知名

attendance_difference:
  reason_class:
    others: "その他（自由記述）"
    pc_used_not_for_work: "業務外でPCの利用があった"
    pc_unnecessarily: "PC操作が不要な業務があった（外出・移動を含む等）"
    pc_log_unreadable: "PCログ取得不具合（ネットワーク不良、システム不具合等）"
    pc_off_omission: "PC停止漏れ（シャットダウン/画面ロック等）"
